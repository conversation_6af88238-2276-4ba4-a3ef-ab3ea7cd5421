#pragma once

#include <string>
#include "yaml_config.h"
#include "cx_work.h"
#include "cx_singleton.h"
#include "cx_thread.h"
#include "DataType.h"
#include <mutex>
#include <condition_variable>
#include <deque>
#include <Eigen/Core>
#include "use-ikfom.h"
#include "kdtreeManage.h"
#include "relocalization.h"  // 修改为正确的相对路径
#include "subcribe.h"
#include "IMU_Processing.h"

using namespace common_lib;
namespace localization{

class Localization: public CXWork
{
public:
    Localization(const std::string &config_file_path);
    ~Localization();

    void Start();

public:
    void Initialize();
    void InitIMU();//初始化IMU处理
    void InitDSFS();//初始化当前一帧点云降采样容器
    void LoadPointMap();//加载全局地图点云
    void InitMapManage();//初始化地图管理
    void InitParameters();//

private:
#ifdef WIN32
    static void MainLoop(PTP_CALLBACK_INSTANCE Instance, PVOID pContext);
#else
    static void* MainLoop(void *pContext);
#endif

private:
    cx_int Run();
    void Reloc();
    void Process(const MeasureGroup& Measures);
    void PublishDSGlobalMap();
    void Resetikdtree();
    void publish_frame_world();
    void publish_frame_body();
    void publish_local_map();
    void publish_odometry_tf_path(state_ikfom &state_point);

private:
    std::string m_sMapPath; // 地图路径
    bool m_bscan_pub_en;
    bool m_bdense_pub_en;
    bool m_bscan_body_pub_en;

    // 线程相关
    CXThread* m_pThread;
    CXMutex m_mtxData;

     //参数
    Eigen::Matrix3d m_Sigma_leg;
    Eigen::Matrix3d m_Sigma_rtk;
    Eigen::Vector3d m_z_leg;
    Eigen::Vector3d m_z_rtk;

    bool m_initialized; // 是否初始化
    bool m_system_initialized; // 系统是否初始化
    double m_last_timestamp; // 上一帧时间戳
    Eigen::Matrix4d m_current_pose; // 当前位姿
    Eigen::Vector3d m_current_velocity; // 当前速度
    Eigen::Vector3d m_current_ba; // 当前加速度偏差
    Eigen::Vector3d m_current_bg; // 当前角速度偏差

    V3D m_Lidar_T_wrt_IMU{0, 0, 0};
    M3D m_Lidar_R_wrt_IMU;

    pcl::VoxelGrid<PointType> m_downSizeFilterSurf;//当前帧降采样点云容器
    pcl::VoxelGrid<PointType> m_downSizepointcloudmap;//全局降采样地图容器

    pcl::PointCloud<PointType>::Ptr m_DSpointcloudmap;//全局降采样地图
    pcl::PointCloud<PointType>::Ptr m_pointcloudmap;//全局点云地图
    PointCloudXYZI::Ptr m_feats_undistort;
    PointCloudXYZI::Ptr m_feats_down_body;  // 畸变纠正后降采样的单帧点云，lidar系
    PointCloudXYZI::Ptr m_feats_down_world; // 畸变纠正后降采样的单帧点云，W系
    PointCloudXYZI::Ptr m_LocalMap;


    map_management m_mapManagement;

    Relocalization* m_Reloc{nullptr}; // 重定位服务指针
    bool m_bneed_relocal;
    int m_iMapCount;
    bool m_bflag_manualpos;
    bool m_bflag_gnss_pose;
    GnssENU m_gnss_pose;

    state_ikfom m_state_point;
    state_ikfom m_state_point_last; // 上一时刻的状态

    // kd树
    KdtreeManage* m_kdtreeManage;
    pcl::KdTreeFLANN<PointType> m_kdtree1;
    pcl::KdTreeFLANN<PointType> m_kdtree2;
    bool m_bflag_ikdtree_initial;
    PointType m_pos_last;
    PointType m_pos_now;

    vector<PointVector> m_Nearest_Points;

    bool m_flg_first_scan;
    double m_dfirst_lidar_time;
    bool m_bflg_EKF_inited;

    int m_ifeats_down_size;
    int m_ieffct_feat_num;
    int m_ivaild_points; 
    int m_iNUM_MAX_ITERATIONS;

    shared_ptr<ImuProcess> m_pImu;

    MeasureGroup m_MeasureData; // 测量组数据

    esekfom::esekf m_kf;
    bool m_bextrinsic_est_en;
    bool m_buseleg;

    double m_max_z{0.0};
    double m_min_z{0.0};

    double m_drtk_heading;
    bool m_brtk_vaild;
    bool m_brtk_heading_vaild;
    // 配置
    common_lib::YamlConfig& m_Config;

    LocSubcribe* m_ptrLocSubcribe{nullptr};
};

} // namespace localization{
