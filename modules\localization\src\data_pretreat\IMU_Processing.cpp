#include "IMU_Processing.h"

ImuProcess::ImuProcess()
    : b_first_frame_(true), imu_need_init_(true), start_timestamp_(-1)
{
    init_iter_num = 1;
    Q = process_noise_cov();
    cov_acc = V3D(0.1, 0.1, 0.1);
    cov_gyr = V3D(0.1, 0.1, 0.1);
    cov_bias_gyr = V3D(0.0001, 0.0001, 0.0001);
    cov_bias_acc = V3D(0.0001, 0.0001, 0.0001);
    mean_acc = V3D(0, 0, -1.0);
    mean_gyr = V3D(0, 0, 0);
    angvel_last = V3D(0, 0, 0);
    Lidar_T_wrt_IMU = V3D(0, 0, 0);
    Lidar_R_wrt_IMU = M3D::Identity();
    //last_imu_.clear();
    feats_undistort_vaild = false;
}

ImuProcess::~ImuProcess() {}

void ImuProcess::Reset()
{
    mean_acc = V3D(0, 0, -1.0);
    mean_gyr = V3D(0, 0, 0);
    angvel_last = V3D(0, 0, 0);
    imu_need_init_ = true;
    start_timestamp_ = -1;
    init_iter_num = 1;
    IMUpose.clear();
    //last_imu_.clear();
    cur_pcl_un_.reset(new PointCloudXYZI());
}

void ImuProcess::set_param(const V3D &transl, const M3D &rot, const V3D &gyr, const V3D &acc, const V3D &gyr_bias, const V3D &acc_bias)
{
    Lidar_T_wrt_IMU = transl;
    Lidar_R_wrt_IMU = rot;
    cov_gyr_scale = gyr;
    cov_acc_scale = acc;
    cov_bias_gyr = gyr_bias;
    cov_bias_acc = acc_bias;
}

void ImuProcess::IMU_init(const MeasureGroup &meas, esekfom::esekf &kf_state, int &N)
{
    V3D cur_acc, cur_gyr;
    
    if (b_first_frame_)
    {
        Reset();
        N = 1;
        b_first_frame_ = false;
        
        // 使用第一帧IMU数据初始化
        cur_acc << meas.imu.front().linear_acceleration[0], 
                  meas.imu.front().linear_acceleration[1], 
                  meas.imu.front().linear_acceleration[2];
        cur_gyr << meas.imu.front().angular_velocity[0],
                  meas.imu.front().angular_velocity[1],
                  meas.imu.front().angular_velocity[2];
        
        mean_acc = cur_acc;
        mean_gyr = cur_gyr;
        first_lidar_time = meas.lidar_beg_time;
    }

    // 计算IMU数据的均值和方差
    for (const auto &imu : meas.imu)
    {
        cur_acc << imu.linear_acceleration[0], imu.linear_acceleration[1], imu.linear_acceleration[2];
        cur_gyr << imu.angular_velocity[0], imu.angular_velocity[1], imu.angular_velocity[2];

        V3D mean_acc_last = mean_acc;
        V3D mean_gyr_last = mean_gyr;

        mean_acc += (cur_acc - mean_acc) / N;
        mean_gyr += (cur_gyr - mean_gyr) / N;

        cov_acc = cov_acc * (N - 1.0) / N + 
                  (cur_acc - mean_acc).cwiseProduct(cur_acc - mean_acc_last) / N;
        cov_gyr = cov_gyr * (N - 1.0) / N + 
                  (cur_gyr - mean_gyr).cwiseProduct(cur_gyr - mean_gyr_last) / N;

        N++;
    }

    // 初始化状态
    state_ikfom init_state = kf_state.get_x();
    init_state.grav = -mean_acc / mean_acc.norm() * G_m_s2;
    init_state.bg = mean_gyr;
    init_state.offset_T_L_I = Lidar_T_wrt_IMU;
    init_state.offset_R_L_I = Sophus::SO3d(Lidar_R_wrt_IMU);
    kf_state.change_x(init_state);

    // 初始化协方差
    Matrix<double, 24, 24> init_P = MatrixXd::Identity(24,24);
    init_P.block<3,3>(6,6).diagonal().array() = 0.00001;
    init_P.block<3,3>(9,9).diagonal().array() = 0.00001;
    init_P.block<3,3>(15,15).diagonal().array() = 0.0001;
    init_P.block<3,3>(18,18).diagonal().array() = 0.001;
    init_P.block<3,3>(21,21).diagonal().array() = 0.00001;
    kf_state.change_P(init_P);
    
    last_imu_ = meas.imu.back();
}

void ImuProcess::UndistortPcl(const MeasureGroup &meas, esekfom::esekf &kf_state, PointCloudXYZI &pcl_out)
{
    // 构建IMU数据队列
    std::deque<IMUData> v_imu = meas.imu;
    v_imu.push_front(last_imu_);
    
    double imu_end_time = v_imu.back().time;
    double pcl_beg_time = meas.lidar_beg_time;
    double pcl_end_time = meas.lidar_end_time;

    // 对点云按时间戳排序
    pcl_out = *(meas.lidar);
    sort(pcl_out.points.begin(), pcl_out.points.end(), time_list);

    // 获取初始状态
    state_ikfom imu_state = kf_state.get_x();
    IMUpose.clear();
    IMUpose.push_back(set_pose6d(0.0, acc_s_last, angvel_last, 
                                imu_state.vel, imu_state.pos, 
                                imu_state.rot.matrix()));

    // IMU前向传播
    V3D angvel_avr, acc_avr, acc_imu, vel_imu, pos_imu;
    M3D R_imu;
    double dt = 0;
    input_ikfom in;

    // 遍历IMU数据进行状态预测
    for (auto it_imu = v_imu.begin(); it_imu < (v_imu.end() - 1); it_imu++)
    {
        auto head = it_imu;
        auto tail = it_imu + 1;

        if (tail->time < last_lidar_end_time_) continue;

        // 计算平均角速度和加速度
        angvel_avr << 0.5 * (head->angular_velocity[0] + tail->angular_velocity[0]),
                     0.5 * (head->angular_velocity[1] + tail->angular_velocity[1]),
                     0.5 * (head->angular_velocity[2] + tail->angular_velocity[2]);
        acc_avr << 0.5 * (head->linear_acceleration[0] + tail->linear_acceleration[0]),
                  0.5 * (head->linear_acceleration[1] + tail->linear_acceleration[1]),
                  0.5 * (head->linear_acceleration[2] + tail->linear_acceleration[2]);

        acc_avr = acc_avr * G_m_s2 / mean_acc.norm();

        // 计算时间增量
        if (head->time < last_lidar_end_time_)
        {
            dt = tail->time - last_lidar_end_time_;
        }
        else
        {
            dt = tail->time - head->time;
        }

        // 处理异常的时间间隔
        if (dt > 0.2)
        {
            dt = 0.005;
        }

        // IMU状态预测
        in.acc = acc_avr;
        in.gyro = angvel_avr;
        Q.block<3,3>(0,0).diagonal() = cov_gyr;
        Q.block<3,3>(3,3).diagonal() = cov_acc;
        Q.block<3,3>(6,6).diagonal() = cov_bias_gyr;
        Q.block<3,3>(9,9).diagonal() = cov_bias_acc;

        kf_state.predict(dt, Q, in);

        // 更新状态
        imu_state = kf_state.get_x();
        angvel_last = V3D(tail->angular_velocity[0], tail->angular_velocity[1], tail->angular_velocity[2]) - imu_state.bg;
        acc_s_last = V3D(tail->linear_acceleration[0], tail->linear_acceleration[1], tail->linear_acceleration[2]) * G_m_s2 / mean_acc.norm();
        acc_s_last = imu_state.rot * (acc_s_last - imu_state.ba) + imu_state.grav;

        // 记录IMU位姿
        double offs_t = tail->time - pcl_beg_time;
        IMUpose.push_back(set_pose6d(offs_t, acc_s_last, angvel_last,
                                    imu_state.vel, imu_state.pos,
                                    imu_state.rot.matrix()));
    }

    // 处理最后一帧IMU数据
    dt = abs(pcl_end_time - imu_end_time);
    kf_state.predict(dt, Q, in);
    imu_state = kf_state.get_x();
    last_imu_ = meas.imu.back();
    last_lidar_end_time_ = pcl_end_time;

    // 点云去畸变
    if (pcl_out.points.empty()) return;
    auto it_pcl = pcl_out.points.end() - 1;

    // 遍历IMU位姿进行去畸变
    for (auto it_kp = IMUpose.end() - 1; it_kp != IMUpose.begin(); it_kp--)
    {
        auto head = it_kp - 1;
        auto tail = it_kp;
        R_imu << MAT_FROM_ARRAY(head->rot);
        vel_imu << VEC_FROM_ARRAY(head->vel);
        pos_imu << VEC_FROM_ARRAY(head->pos);
        acc_imu << VEC_FROM_ARRAY(tail->acc);
        angvel_avr << VEC_FROM_ARRAY(tail->gyr);

        for (; it_pcl->curvature / double(1000) > head->offset_time; it_pcl--)
        {
            dt = it_pcl->curvature / double(1000) - head->offset_time;
            M3D R_i(R_imu * Sophus::SO3d::exp(angvel_avr * dt).matrix());
            V3D P_i(it_pcl->x, it_pcl->y, it_pcl->z);
            V3D T_ei(pos_imu + vel_imu * dt + 0.5 * acc_imu * dt * dt - imu_state.pos);
            V3D P_compensate = imu_state.offset_R_L_I.matrix().transpose() * 
                             (imu_state.rot.matrix().transpose() * 
                             (R_i * (imu_state.offset_R_L_I.matrix() * P_i + imu_state.offset_T_L_I) + T_ei) - 
                             imu_state.offset_T_L_I);

            it_pcl->x = P_compensate(0);
            it_pcl->y = P_compensate(1);
            it_pcl->z = P_compensate(2);

            if (it_pcl == pcl_out.points.begin()) break;
        }
    }
}

bool ImuProcess::Process(const MeasureGroup &meas, esekfom::esekf &kf_state, PointCloudXYZI::Ptr &cur_pcl_un_)
{
    if (meas.imu.empty()) 
    {
        return !imu_need_init_;
    }

    if (meas.lidar == nullptr)
    {
        return false;
    }

    if (imu_need_init_)
    {
        IMU_init(meas, kf_state, init_iter_num);
        imu_need_init_ = true;
        last_imu_ = meas.imu.back();

        if (init_iter_num > MAX_INI_COUNT)
        {
            cov_acc *= pow(G_m_s2 / mean_acc.norm(), 2);
            imu_need_init_ = false;
            cov_acc = cov_acc_scale;
            cov_gyr = cov_gyr_scale;
        }

        return !imu_need_init_;
    }

    feats_undistort_vaild = true;
    UndistortPcl(meas, kf_state, *cur_pcl_un_);
    return true;
}