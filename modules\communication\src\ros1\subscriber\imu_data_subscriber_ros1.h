#pragma once

#if COMMUNICATION_TYPE == ROS1

#include <ros/ros.h>
// #include <deque>
// #include <mutex>
#include <sensor_msgs/Imu.h>
// #include "data_types/imu_data.h"
#include "subscriber_base.h"

namespace communication::ros1{

class ImuDataSubscriberRos1 :  public ImuDataSubscriberBase {
    public:
        ImuDataSubscriberRos1(ros::NodeHandle &nh, const std::string &imu_topic, size_t max_buffer_size = 100);

        ~ImuDataSubscriberRos1() = default;

        void IMUDataCallbackRos1(const sensor_msgs::ImuConstPtr &imu);

    private:
        ros::NodeHandle& nh_;
        ros::Subscriber subscriber_;
    };

} // namespace communication::ros1{

#endif