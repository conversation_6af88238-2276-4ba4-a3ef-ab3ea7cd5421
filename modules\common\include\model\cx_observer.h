#ifndef _CX_OBSERVER_H_
#define _CX_OBSERVER_H_

#include <iostream>
#include <set>
#include <string>

#include "cx_pubhead.h"
namespace common_lib {
using namespace std;

class CXObservable;

class CXObserver {
 public:
  CXObserver() {};
  virtual ~CXObserver() {};

  virtual void Update(CXObservable *observer_ptr, void *arg_ptr = NULL) = 0;
};

enum SUBJECT_TYPE {
  ST_NONE,
  ST_VECHICLE_POSITION,
  ST_MM_RESULT,
  ST_RC_RESTULT,
  ST_MPP_RESULT,  // MPP result
  ST_SENSOR_UBLOX
};

class CXObservable {
 public:
  CXObservable()
      : change_flag_(false)
      , subject_type_(ST_NONE) {};
  virtual ~CXObservable() {};

  void Attach(CXObserver *observer);
  void Detach(CXObserver *observer);
  void DetachAll();

  void Notify(void *arg_ptr = NULL);

  bool HasChanged();
  int GetObserversCount();

  SUBJECT_TYPE GetType();

 protected:
  void SetChanged();
  void ClearChanged();

 private:
  bool change_flag_;
  set<CXObserver *> observers_;

 protected:
  SUBJECT_TYPE subject_type_;
};
}  // namespace common_lib

#endif  // _CX_OBSERVER_H_
