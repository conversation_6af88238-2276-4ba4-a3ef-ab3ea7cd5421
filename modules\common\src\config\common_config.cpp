#include "common_config.h"

#include <iostream>

#include "config.h"
#include "cx_path.h"
namespace common_lib {
void CommonConfig::GetCommonConfig(CommonConfigInfo &common_config_info) {
  if (InitCommonConfig()) {
    common_config_info.version_ =
        common_lib::Config::GetInstance().getParam<std::string>("version", "v1.0");
    common_config_info.color_image_topic_ = common_lib::Config::GetInstance().getParam<std::string>(
        "sensor/color_image_topic", "/camera/color/image_raw");
    common_config_info.color_camera_info_topic_ =
        common_lib::Config::GetInstance().getParam<std::string>("sensor/color_camera_info_topic",
                                                                "/camera/color/camera_info");
    common_config_info.depth_image_topic_ = common_lib::Config::GetInstance().getParam<std::string>(
        "sensor/depth_image_topic", "/camera/depth/image_rect_raww");
    common_config_info.depth_camera_info_topic_ =
        common_lib::Config::GetInstance().getParam<std::string>("sensor/depth_camera_info_topic",
                                                                "/camera/depth/camera_info ");
    common_config_info.depth_to_color_topic_ =
        common_lib::Config::GetInstance().getParam<std::string>(
            "sensor/depth_to_color_topic", "/camera/extrinsics/depth_to_color");
    common_config_info.imu_topic_ =
        common_lib::Config::GetInstance().getParam<std::string>("sensor/imu", "/imu");
    common_config_info.gnss_topic_ =
        common_lib::Config::GetInstance().getParam<std::string>("sensor/gnss", "/chattgps");
    common_config_info.lidar_topic_ = common_lib::Config::GetInstance().getParam<std::string>(
        "sensor/lidar", "/lslidar_point_cloud");
    common_config_info.lidar2imu_extrinsic_t_ =
        common_lib::Config::GetInstance().getParam<std::vector<cx_double>>(
            "transform/extrinsic_T", std::vector<cx_double>());
    common_config_info.lidar2imu_extrinsic_r_ =
        common_lib::Config::GetInstance().getParam<std::vector<cx_double>>(
            "transform/extrinsic_R", std::vector<cx_double>());
    common_config_info.rtk2Lidar_t_ =
        common_lib::Config::GetInstance().getParam<std::vector<cx_double>>(
            "transform/rtk2Lidar_T", std::vector<cx_double>());
    common_config_info.color_to_lidar_q_ =
        common_lib::Config::GetInstance().getParam<std::vector<cx_double>>(
            "transform/color_to_lidar_q", std::vector<cx_double>());
    common_config_info.color_to_lidar_t_ =
        common_lib::Config::GetInstance().getParam<std::vector<cx_double>>(
            "transform/color_to_lidar_t", std::vector<cx_double>());
    common_config_info.lidar_topic_ = common_lib::Config::GetInstance().getParam<std::string>(
        "node_output/perception_output_topic", "/perception/terrain_pointcloud");
  }
}
cx_bool CommonConfig::InitCommonConfig() {
  cx_bool success = false;
  // Load config parameters.
  cx_string config_file;
  common_lib::GetExePath(config_file);
  config_file += "/config/common_config.yaml";
  fprintf(stderr, ">>>> common debug >>>> config file path: %s  \n", config_file.c_str());
  success = common_lib::IsFileExist(config_file);
  if (success) {
    Config::GetInstance().Init(config_file);
  }

  return success;
}
}  // namespace common_lib
