<?xml version="1.0"?>
<launch>
  <!-- Controller <PERSON><PERSON>le Test Launch File -->
  <!-- 用于测试calibration_node和velocity_smoother_node -->
  
  <!-- 参数配置 -->
  <arg name="config_dir" default="$(find controller)/config" />
  <arg name="rviz_config" default="$(find controller)/rviz/controller.rviz" />
  <arg name="use_rviz" default="true" />
  <arg name="test_mode" default="true" />
  
  <!-- 加载配置文件 -->
  <rosparam command="load" file="$(arg config_dir)/calibration.yaml" />
  <rosparam command="load" file="$(arg config_dir)/velocity_smoother_config.yaml" />
  
  <!-- 启动测试用的模拟数据发布器 -->
  <group if="$(arg test_mode)">
    <!-- 发布模拟里程计数据 -->
    <node name="fake_odometry_publisher" pkg="rostopic" type="rostopic" 
          args="pub /Odometry nav_msgs/Odometry '{header: {stamp: now, frame_id: 'odom'}, child_frame_id: 'base_link', pose: {pose: {position: {x: 0.0, y: 0.0, z: 0.0}, orientation: {x: 0.0, y: 0.0, z: 0.0, w: 1.0}}}, twist: {twist: {linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}}}' -r 10" />
    
    <!-- 发布模拟速度命令 -->
    <node name="fake_cmd_vel_publisher" pkg="rostopic" type="rostopic" 
          args="pub /cmd_vel geometry_msgs/Twist '{linear: {x: 0.5, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.2}}' -r 5" />
    
    <!-- 发布模拟目标位姿 -->
    <node name="fake_goal_publisher" pkg="rostopic" type="rostopic" 
          args="pub /move_base_simple/goal geometry_msgs/PoseStamped '{header: {stamp: now, frame_id: 'map'}, pose: {position: {x: 2.0, y: 1.0, z: 0.0}, orientation: {x: 0.0, y: 0.0, z: 0.0, w: 1.0}}}' -r 0.1" />
  </group>
  
  <!-- 启动calibration_node -->
  <node name="calibration_node" pkg="controller" type="calibration_node" output="screen">
    <param name="config_file" value="$(arg config_dir)/calibration.yaml" />
    <param name="communication_config" value="$(arg config_dir)/communication_config.yaml" />
    
    <!-- 重映射话题 -->
    <remap from="/odometry" to="/Odometry" />
    <remap from="/goal" to="/move_base_simple/goal" />
    <remap from="/web_goal" to="/web_goal" />
    <remap from="/mode" to="/calibration_mode" />
    <remap from="/cmd_vel" to="/cmd_vel_raw" />
    <remap from="/stop" to="/emergency_stop" />
    <remap from="/inner_stop" to="/inner_emergency_stop" />
    <remap from="/calibration_mode" to="/calibration_mode_status" />
  </node>
  
  <!-- 启动velocity_smoother_node -->
  <node name="velocity_smoother_node" pkg="controller" type="velocity_smoother_node" output="screen">
    <param name="config_file" value="$(arg config_dir)/velocity_smoother_config.yaml" />
    <param name="communication_config" value="$(arg config_dir)/communication_config.yaml" />
    
    <!-- 重映射话题 -->
    <remap from="/twist/data" to="/twist/data" />
    <remap from="/cmd_vel" to="/cmd_vel_smooth" />
    <remap from="/odometry" to="/Odometry" />
    
    <!-- 速度平滑器参数 -->
    <param name="speed_lim_v" value="2.0" />
    <param name="speed_lim_w" value="1.5" />
    <param name="accel_lim_v" value="1.0" />
    <param name="accel_lim_w" value="0.8" />
    <param name="decel_factor" value="2.0" />
    <param name="frequency" value="50.0" />
    <param name="quiet" value="false" />
    <param name="robot_feedback" value="0" />
  </node>
  
  <!-- 启动RViz可视化 -->
  <group if="$(arg use_rviz)">
    <node name="rviz" pkg="rviz" type="rviz" args="-d $(arg rviz_config)" output="screen" />
  </group>
  
  <!-- 启动静态TF发布器 -->
  <node pkg="tf2_ros" type="static_transform_publisher" name="base_link_to_map" 
        args="0 0 0 0 0 0 map base_link" />
  
  <node pkg="tf2_ros" type="static_transform_publisher" name="odom_to_base_link" 
        args="0 0 0 0 0 0 base_link odom" />
  
  <!-- 启动TF监听器 -->
  <node pkg="tf2_ros" type="buffer_server" name="tf2_buffer_server" />
  
  <!-- 启动TF监听器 -->
  <node pkg="tf2_ros" type="transform_listener" name="tf2_listener" />
  
  <!-- 启动话题监控 -->
  <group if="$(arg test_mode)">
    <node name="topic_monitor" pkg="rostopic" type="rostopic" 
          args="echo /cmd_vel_smooth -n 1" output="screen" />
  </group>
  
</launch> 