#include "obstacle_stop.cpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace obstacle_stop_no_ros;

/**
 * @brief 障碍物停止节点 - 基础节点创建和初始化
 */
int main(int argc, char** argv) {
    std::cout << "=== ObstacleStopNoRos 基础节点程序 ===" << std::endl;
    
    try {
        // 创建障碍物停止器节点
        ObstacleStopNoRos obstacle_stop("obstacle_stop_node");
        
        // 设置基本参数
        std::cout << "\n🔧 配置障碍物停止器参数..." << std::endl;
        obstacle_stop.setParameters(
            0.15,  // obstacleHeightThre - 障碍物高度阈值
            1.0,   // vehicleLength - 载体长度
            0.5,   // vehicleWidth - 载体宽度
            2,     // obsnumThre - 障碍物数量阈值
            3.0,   // adjacentRange - 邻近范围
            5.0,   // replan_time - 重规划时间
            0.0,   // sensorOffsetX - 传感器X偏移
            0.0    // sensorOffsetY - 传感器Y偏移
        );
        
        // 设置回调函数
        std::cout << "\n📡 设置回调函数..." << std::endl;
        obstacle_stop.setModeCallback([](const std::shared_ptr<BoolMsg>& mode) {
            std::cout << "模式切换: " << (mode->data ? "精调模式" : "导航模式") << std::endl;
        });
        
        obstacle_stop.setStopCallback([](const std::shared_ptr<Int8Msg>& stop) {
            std::cout << "停止状态: " << static_cast<int>(stop->data) << std::endl;
        });
        
        obstacle_stop.setPathCallback([](const std::shared_ptr<Path>& path) {
            std::cout << "局部路径发布: 包含 " << path->poses.size() << " 个路径点" << std::endl;
        });
        
        obstacle_stop.setReplanCallback([](const std::shared_ptr<Int8Msg>& replan) {
            std::cout << "重规划信号: " << (replan->data == 1 ? "需要重规划" : "正常导航") << std::endl;
        });
        
        obstacle_stop.setNavigationResultCallback([](const std::shared_ptr<NavigationResult>& result) {
            std::cout << "导航结果: " << std::endl;
            std::cout << "  点位ID: " << result->point_id << std::endl;
            std::cout << "  导航状态: " << result->nav_state << std::endl;
        });
        
        // 初始化
        std::cout << "\n⚙️ 初始化障碍物停止器..." << std::endl;
        if (!obstacle_stop.init()) {
            std::cerr << "❌ 障碍物停止器初始化失败" << std::endl;
            return -1;
        }
        
        // 显示状态
        obstacle_stop.printStatus();
        
        // 启动障碍物停止器
        std::cout << "\n🚀 启动障碍物停止器..." << std::endl;
        obstacle_stop.start();
        
        std::cout << "\n✅ 障碍物停止节点启动完成" << std::endl;
        std::cout << "节点状态: " << (obstacle_stop.isRunning() ? "运行中" : "已停止") << std::endl;
        
        // 保持节点运行
        std::cout << "\n💡 节点正在运行，按Ctrl+C退出..." << std::endl;
        std::cout << "可以通过API输入里程计数据、点云数据和目标点进行测试" << std::endl;
        std::cout << "\n使用说明:" << std::endl;
        std::cout << "1. 首先输入导航目标点 (inputTarget)" << std::endl;
        std::cout << "2. 然后输入局部目标点 (inputGoal)" << std::endl;
        std::cout << "3. 输入里程计数据 (inputOdometry)" << std::endl;
        std::cout << "4. 输入地形点云数据 (inputTerrainCloud) 开始障碍物检测" << std::endl;
        
        while (obstacle_stop.isRunning()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序执行出错: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
