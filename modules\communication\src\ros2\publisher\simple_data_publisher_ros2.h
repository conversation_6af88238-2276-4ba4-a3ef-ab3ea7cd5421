#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/int32.hpp>
#include <std_msgs/msg/float64.hpp>
#include <std_msgs/msg/string.hpp>
#include <std_msgs/msg/bool.hpp>
#include <std_msgs/msg/float32.hpp>

#include "ros2/core/publisher_base_ros2.h"

namespace communication::ros2 {

template<typename T, typename T_MSG>
class SimpleDataPublisherRos2 : public PublisherBaseRos2<T, T_MSG> {
public:
    SimpleDataPublisherRos2(rclcpp::Node::SharedPtr node, 
                           const std::string& topic,
                           size_t max_buffer_size = 10)
        : PublisherBaseRos2<T, T_MSG>(node, topic, max_buffer_size) {}

protected:
    virtual void ToMsg() override {
        this->msg_.data = PublisherBase<T>::data_;
    }
};

using IntDataPublisherRos2 = SimpleDataPublisherRos2<int, std_msgs::msg::Int32>;
using DoubleDataPublisherRos2 = SimpleDataPublisherRos2<double, std_msgs::msg::Float64>;
using StringDataPublisherRos2 = SimpleDataPublisherRos2<std::string, std_msgs::msg::String>;
using BoolDataPublisherRos2 = SimpleDataPublisherRos2<bool, std_msgs::msg::Bool>;
using FloatDataPublisherRos2 = SimpleDataPublisherRos2<float, std_msgs::msg::Float32>;

} // namespace communication::ros2

#endif
