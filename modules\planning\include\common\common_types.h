#ifndef COMMON_TYPES_H
#define COMMON_TYPES_H

#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <mutex>
#include <chrono>
#include <iostream>
#include <fstream>
#include <atomic>
#include <thread>

namespace planning_common {

const double PI = 3.1415926;

/**
 * @brief 时间戳结构
 */
struct TimeStamp {
    double sec;
    
    TimeStamp() : sec(0.0) {}
    TimeStamp(double s) : sec(s) {}
    
    static TimeStamp now() {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        double seconds = std::chrono::duration<double>(duration).count();
        return TimeStamp(seconds);
    }
    
    double toSec() const { return sec; }
};

/**
 * @brief 消息头结构
 */
struct Header {
    TimeStamp stamp;
    std::string frame_id;
    
    Header() : frame_id("") {}
    
    void setCurrentTime() {
        stamp = TimeStamp::now();
    }
};

/**
 * @brief 3D点结构
 */
struct Point {
    double x, y, z;
    
    Point() : x(0.0), y(0.0), z(0.0) {}
    Point(double _x, double _y, double _z) : x(_x), y(_y), z(_z) {}
};

/**
 * @brief Vector3数据结构
 */
struct Vector3 {
    double x, y, z;
    
    Vector3() : x(0.0), y(0.0), z(0.0) {}
    Vector3(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
    
    Vector3& operator=(const Vector3& other) {
        x = other.x;
        y = other.y;
        z = other.z;
        return *this;
    }
    
    bool operator==(const Vector3& other) const {
        return (x == other.x) && (y == other.y) && (z == other.z);
    }
    
    bool operator!=(const Vector3& other) const {
        return !(*this == other);
    }
};

/**
 * @brief 四元数结构
 */
struct Quaternion {
    double x, y, z, w;
    
    Quaternion() : x(0.0), y(0.0), z(0.0), w(1.0) {}
    Quaternion(double _x, double _y, double _z, double _w) : x(_x), y(_y), z(_z), w(_w) {}
    
    // 从欧拉角创建四元数
    static Quaternion fromRPY(double roll, double pitch, double yaw) {
        Quaternion q;
        double cy = cos(yaw * 0.5);
        double sy = sin(yaw * 0.5);
        double cp = cos(pitch * 0.5);
        double sp = sin(pitch * 0.5);
        double cr = cos(roll * 0.5);
        double sr = sin(roll * 0.5);
        
        q.w = cr * cp * cy + sr * sp * sy;
        q.x = sr * cp * cy - cr * sp * sy;
        q.y = cr * sp * cy + sr * cp * sy;
        q.z = cr * cp * sy - sr * sp * cy;
        return q;
    }
    
    // 从Yaw角创建四元数
    static Quaternion fromYaw(double yaw) {
        return fromRPY(0.0, 0.0, yaw);
    }
    
    // 转换为欧拉角
    void toRPY(double& roll, double& pitch, double& yaw) const {
        // Roll (x-axis rotation)
        double sinr_cosp = 2 * (w * x + y * z);
        double cosr_cosp = 1 - 2 * (x * x + y * y);
        roll = atan2(sinr_cosp, cosr_cosp);
        
        // Pitch (y-axis rotation)
        double sinp = 2 * (w * y - z * x);
        if (abs(sinp) >= 1)
            pitch = copysign(PI / 2, sinp); // use 90 degrees if out of range
        else
            pitch = asin(sinp);
        
        // Yaw (z-axis rotation)
        double siny_cosp = 2 * (w * z + x * y);
        double cosy_cosp = 1 - 2 * (y * y + z * z);
        yaw = atan2(siny_cosp, cosy_cosp);
    }
    
    // 获取Yaw角
    double getYaw() const {
        double roll, pitch, yaw;
        toRPY(roll, pitch, yaw);
        return yaw;
    }
};

/**
 * @brief 位姿结构
 */
struct Pose {
    Point position;
    Quaternion orientation;

    Pose() {}
    Pose(const Point& pos, const Quaternion& orient)
        : position(pos), orientation(orient) {}
};

/**
 * @brief 带时间戳的位姿
 */
struct PoseStamped {
    double time = 0.0;
    Pose pose;
    
    PoseStamped() {}
    PoseStamped(double t, const Point& pos, const Quaternion& orient)
        : time(t), pose(pos, orient) {}
};

/**
 * @brief Twist结构
 */
struct TwistMsg {
    Vector3 linear;
    Vector3 angular;
    
    TwistMsg() {}
    TwistMsg(double linear_x, double angular_z) {
        linear.x = linear_x;
        angular.z = angular_z;
    }
    
    TwistMsg& operator=(const TwistMsg& other) {
        linear = other.linear;
        angular = other.angular;
        return *this;
    }
    
    bool operator==(const TwistMsg& other) const {
        return (linear == other.linear) && (angular == other.angular);
    }
    
    bool operator!=(const TwistMsg& other) const {
        return !(*this == other);
    }
};

/**
 * @brief Odometry数据结构
 */
struct OdometryData {
    struct Header {
        double stamp;
        std::string frame_id;
        Header() : stamp(0.0), frame_id("odom") {}
    } header;
    
    struct PoseWithCovariance {
        struct Pose {
            Vector3 position;
            planning_common::Quaternion orientation;
        } pose;
        std::vector<double> covariance; // 6x6 covariance matrix
    } pose;
    
    struct TwistWithCovariance {
        TwistMsg twist;
        std::vector<double> covariance; // 6x6 covariance matrix
    } twist;
    
    OdometryData() {
        pose.covariance.resize(36, 0.0);
        twist.covariance.resize(36, 0.0);
    }
};

/**
 * @brief 路径数据结构（兼容 communication::PathData）
 */
struct PathData {
    Header header; // 路径头部信息
    std::vector<PoseStamped> poses_; // 路径点

    PathData() {}
    void resize(size_t size) { poses_.resize(size); }
    size_t size() const { return poses_.size(); }
    bool empty() const { return poses_.empty(); }
    void clear() { poses_.clear(); }
};

struct PoseArray{
    Header header;
    std::vector<Pose> poses;
    PoseArray() {}
};
/**
 * @brief 速度命令结构
 */
struct TwistData {
    Vector3 linear;
    Vector3 angular;
    
    TwistData() : linear{0,0,0}, angular{0,0,0} {}
    
    TwistData(double vx, double vy, double vz, double wx, double wy, double wz) {
        linear.x = vx;
        linear.y = vy;
        linear.z = vz;
        angular.x = wx;
        angular.y = wy;
        angular.z = wz;
    }
    
    void setZero() {
        linear.x = linear.y = linear.z = 0.0;
        angular.x = angular.y = angular.z = 0.0;
    }
    
    bool isZero() const {
        return (fabs(linear.x) < 1e-6 && fabs(linear.y) < 1e-6 && fabs(linear.z) < 1e-6 &&
                fabs(angular.x) < 1e-6 && fabs(angular.y) < 1e-6 && fabs(angular.z) < 1e-6);
    }
};

/**
 * @brief 布尔消息
 */
struct BoolMsg {
    bool data;
    
    BoolMsg() : data(false) {}
    BoolMsg(bool value) : data(value) {}
};

/**
 * @brief 整数消息
 */
struct Int8Msg {
    int8_t data;
    
    Int8Msg() : data(0) {}
    Int8Msg(int8_t value) : data(value) {}
};

} // namespace planning_common

#endif // COMMON_TYPES_H 