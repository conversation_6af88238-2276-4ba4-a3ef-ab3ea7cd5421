#ifndef COMMON_TYPES_H
#define COMMON_TYPES_H

#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <mutex>
#include <chrono>
#include <iostream>
#include <fstream>
#include <atomic>
#include <thread>

namespace planning_common {

const double PI = 3.1415926;

/**
 * @brief 时间戳结构
 */
struct TimeStamp {
    double sec;
    
    TimeStamp() : sec(0.0) {}
    TimeStamp(double s) : sec(s) {}
    
    static TimeStamp now() {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        double seconds = std::chrono::duration<double>(duration).count();
        return TimeStamp(seconds);
    }
    
    double toSec() const { return sec; }
};

/**
 * @brief 消息头结构
 */
struct Header {
    TimeStamp stamp;
    std::string frame_id;
    
    Header() : frame_id("") {}
    
    void setCurrentTime() {
        stamp = TimeStamp::now();
    }
};

/**
 * @brief 3D点结构
 */
struct Point {
    double x, y, z;
    
    Point() : x(0.0), y(0.0), z(0.0) {}
    Point(double _x, double _y, double _z) : x(_x), y(_y), z(_z) {}
};

/**
 * @brief Vector3数据结构
 */
struct Vector3 {
    double x, y, z;
    
    Vector3() : x(0.0), y(0.0), z(0.0) {}
    Vector3(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
    
    Vector3& operator=(const Vector3& other) {
        x = other.x;
        y = other.y;
        z = other.z;
        return *this;
    }
    
    bool operator==(const Vector3& other) const {
        return (x == other.x) && (y == other.y) && (z == other.z);
    }
    
    bool operator!=(const Vector3& other) const {
        return !(*this == other);
    }
};

/**
 * @brief 四元数结构
 */
struct Quaternion {
    double x, y, z, w;
    
    Quaternion() : x(0.0), y(0.0), z(0.0), w(1.0) {}
    Quaternion(double _x, double _y, double _z, double _w) : x(_x), y(_y), z(_z), w(_w) {}
    
    // 从欧拉角创建四元数
    static Quaternion fromRPY(double roll, double pitch, double yaw) {
        Quaternion q;
        double cy = cos(yaw * 0.5);
        double sy = sin(yaw * 0.5);
        double cp = cos(pitch * 0.5);
        double sp = sin(pitch * 0.5);
        double cr = cos(roll * 0.5);
        double sr = sin(roll * 0.5);
        
        q.w = cr * cp * cy + sr * sp * sy;
        q.x = sr * cp * cy - cr * sp * sy;
        q.y = cr * sp * cy + sr * cp * sy;
        q.z = cr * cp * sy - sr * sp * cy;
        return q;
    }
    
    // 从Yaw角创建四元数
    static Quaternion fromYaw(double yaw) {
        return fromRPY(0.0, 0.0, yaw);
    }
    
    // 转换为欧拉角
    void toRPY(double& roll, double& pitch, double& yaw) const {
        // Roll (x-axis rotation)
        double sinr_cosp = 2 * (w * x + y * z);
        double cosr_cosp = 1 - 2 * (x * x + y * y);
        roll = atan2(sinr_cosp, cosr_cosp);
        
        // Pitch (y-axis rotation)
        double sinp = 2 * (w * y - z * x);
        if (abs(sinp) >= 1)
            pitch = copysign(PI / 2, sinp); // use 90 degrees if out of range
        else
            pitch = asin(sinp);
        
        // Yaw (z-axis rotation)
        double siny_cosp = 2 * (w * z + x * y);
        double cosy_cosp = 1 - 2 * (y * y + z * z);
        yaw = atan2(siny_cosp, cosy_cosp);
    }
    
    // 获取Yaw角
    double getYaw() const {
        double roll, pitch, yaw;
        toRPY(roll, pitch, yaw);
        return yaw;
    }
};

/**
 * @brief 位姿结构
 */
struct Pose {
    Point position;
    Quaternion orientation;

    Pose() {}
    Pose(const Point& pos, const Quaternion& orient)
        : position(pos), orientation(orient) {}
};

/**
 * @brief 带时间戳的位姿
 */
struct PoseStamped {
    double time = 0.0;
    Pose pose;
    
    PoseStamped() {}
    PoseStamped(double t, const Point& pos, const Quaternion& orient)
        : time(t), pose(pos, orient) {}
};

/**
 * @brief Twist结构
 */
struct TwistMsg {
    Vector3 linear;
    Vector3 angular;
    
    TwistMsg() {}
    TwistMsg(double linear_x, double angular_z) {
        linear.x = linear_x;
        angular.z = angular_z;
    }
    
    TwistMsg& operator=(const TwistMsg& other) {
        linear = other.linear;
        angular = other.angular;
        return *this;
    }
    
    bool operator==(const TwistMsg& other) const {
        return (linear == other.linear) && (angular == other.angular);
    }
    
    bool operator!=(const TwistMsg& other) const {
        return !(*this == other);
    }
};

/**
 * @brief Odometry数据结构
 */
struct OdometryData {
    struct Header {
        double stamp;
        std::string frame_id;
        Header() : stamp(0.0), frame_id("odom") {}
    } header;
    
    struct PoseWithCovariance {
        struct Pose {
            Vector3 position;
            planning_common::Quaternion orientation;
        } pose;
        std::vector<double> covariance; // 6x6 covariance matrix
    } pose;
    
    struct TwistWithCovariance {
        TwistMsg twist;
        std::vector<double> covariance; // 6x6 covariance matrix
    } twist;
    
    OdometryData() {
        pose.covariance.resize(36, 0.0);
        twist.covariance.resize(36, 0.0);
    }
};

/**
 * @brief 路径数据结构（兼容 communication::PathData）
 */
struct PathData {
    Header header; // 路径头部信息
    std::vector<PoseStamped> poses_; // 路径点

    PathData() {}
    void resize(size_t size) { poses_.resize(size); }
    size_t size() const { return poses_.size(); }
    bool empty() const { return poses_.empty(); }
    void clear() { poses_.clear(); }
};

struct PoseArray{
    Header header;
    std::vector<Pose> poses;
    PoseArray() {}
};
/**
 * @brief 速度命令结构
 */
struct TwistData {
    Vector3 linear;
    Vector3 angular;
    
    TwistData() : linear{0,0,0}, angular{0,0,0} {}
    
    TwistData(double vx, double vy, double vz, double wx, double wy, double wz) {
        linear.x = vx;
        linear.y = vy;
        linear.z = vz;
        angular.x = wx;
        angular.y = wy;
        angular.z = wz;
    }
    
    void setZero() {
        linear.x = linear.y = linear.z = 0.0;
        angular.x = angular.y = angular.z = 0.0;
    }
    
    bool isZero() const {
        return (fabs(linear.x) < 1e-6 && fabs(linear.y) < 1e-6 && fabs(linear.z) < 1e-6 &&
                fabs(angular.x) < 1e-6 && fabs(angular.y) < 1e-6 && fabs(angular.z) < 1e-6);
    }
};

/**
 * @brief 布尔消息
 */
struct BoolMsg {
    bool data;
    
    BoolMsg() : data(false) {}
    BoolMsg(bool value) : data(value) {}
};

/**
 * @brief 整数消息
 */
struct Int8Msg {
    int8_t data;

    Int8Msg() : data(0) {}
    Int8Msg(int8_t value) : data(value) {}
};

/**
 * @brief 32位点结构 (用于PointCloud)
 */
struct Point32 {
    float x, y, z;

    Point32() : x(0.0f), y(0.0f), z(0.0f) {}
    Point32(float _x, float _y, float _z) : x(_x), y(_y), z(_z) {}
};

/**
 * @brief 多边形结构
 */
struct Polygon {
    std::vector<Point32> points;

    Polygon() {}
};

/**
 * @brief 带时间戳的多边形
 */
struct PolygonStamped {
    Header header;
    Polygon polygon;

    PolygonStamped() {}
};

/**
 * @brief 颜色RGBA结构
 */
struct ColorRGBA {
    float r, g, b, a;

    ColorRGBA() : r(0.0f), g(0.0f), b(0.0f), a(1.0f) {}
    ColorRGBA(float _r, float _g, float _b, float _a) : r(_r), g(_g), b(_b), a(_a) {}
};

/**
 * @brief 通道数据结构 (用于PointCloud)
 */
struct ChannelFloat32 {
    std::string name;
    std::vector<float> values;

    ChannelFloat32() {}
    ChannelFloat32(const std::string& _name) : name(_name) {}
};

/**
 * @brief 点云结构
 */
struct PointCloud {
    Header header;
    std::vector<Point32> points;
    std::vector<ChannelFloat32> channels;

    PointCloud() {}
};

/**
 * @brief 占用栅格地图信息
 */
struct MapMetaData {
    double resolution;      // 地图分辨率 (m/cell)
    uint32_t width;         // 地图宽度 (cells)
    uint32_t height;        // 地图高度 (cells)
    Pose origin;            // 地图原点位姿

    MapMetaData() : resolution(0.05), width(0), height(0) {}
};

/**
 * @brief 占用栅格地图
 */
struct OccupancyGrid {
    Header header;
    MapMetaData info;
    std::vector<int8_t> data;  // 占用概率 [0,100], -1表示未知

    OccupancyGrid() {}
};

/**
 * @brief 占用栅格地图更新
 */
struct OccupancyGridUpdate {
    Header header;
    int32_t x, y;           // 更新区域左下角
    uint32_t width, height; // 更新区域尺寸
    std::vector<int8_t> data;

    OccupancyGridUpdate() : x(0), y(0), width(0), height(0) {}
};

/**
 * @brief 体素栅格消息 (用于costmap_2d)
 */
struct VoxelGrid {
    Header header;
    std::vector<uint32_t> data;
    uint32_t size_x, size_y, size_z;
    double origin_x, origin_y, origin_z;
    double resolution;

    VoxelGrid() : size_x(0), size_y(0), size_z(0),
                  origin_x(0.0), origin_y(0.0), origin_z(0.0),
                  resolution(0.05) {}
};

/**
 * @brief 变换结构
 */
struct Transform {
    Vector3 translation;
    Quaternion rotation;

    Transform() {}
    Transform(const Vector3& trans, const Quaternion& rot)
        : translation(trans), rotation(rot) {}
};

/**
 * @brief 带时间戳的变换
 */
struct TransformStamped {
    Header header;
    std::string child_frame_id;
    Transform transform;

    TransformStamped() {}
};

/**
 * @brief 带时间戳的Twist
 */
struct TwistStamped {
    Header header;
    TwistMsg twist;

    TwistStamped() {}
};

/**
 * @brief 带协方差的Twist
 */
struct TwistWithCovariance {
    TwistMsg twist;
    std::vector<double> covariance; // 6x6协方差矩阵

    TwistWithCovariance() {
        covariance.resize(36, 0.0);
    }
};

/**
 * @brief 带协方差的位姿
 */
struct PoseWithCovariance {
    Pose pose;
    std::vector<double> covariance; // 6x6协方差矩阵

    PoseWithCovariance() {
        covariance.resize(36, 0.0);
    }
};

/**
 * @brief 里程计消息
 */
struct Odometry {
    Header header;
    std::string child_frame_id;
    PoseWithCovariance pose;
    TwistWithCovariance twist;

    Odometry() : child_frame_id("base_link") {}
};

/**
 * @brief 路径消息 (nav_msgs::Path)
 */
struct Path {
    Header header;
    std::vector<PoseStamped> poses;

    Path() {}

    void clear() { poses.clear(); }
    size_t size() const { return poses.size(); }
    bool empty() const { return poses.empty(); }
    void resize(size_t size) { poses.resize(size); }
};

} // namespace planning_common

#endif // COMMON_TYPES_H