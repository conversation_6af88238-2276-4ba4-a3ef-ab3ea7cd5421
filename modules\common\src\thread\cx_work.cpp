#include "cx_work.h"
namespace common_lib {
CXWork::CXWork() {
  thread_proc_ = NULL;
  context_ptr_ = NULL;
}

#ifdef WIN32
CXWork::CXWork(ThreadProc *pfnThreadProc, LPVOID const pContext) {
  m_pfnThreadProc = pfnThreadProc;
  m_pContext = pContext;
}
#endif  // Win32

CXWork::~CXWork() {}

cx_int CXWork::SetThreadProc(ThreadProc *pfnThreadProc) {
  thread_proc_ = pfnThreadProc;

  return 0;
}

cx_int CXWork::SetThreadContext(LPVOID pContext) {
  context_ptr_ = pContext;

  return 0;
}

ThreadProc *CXWork::GetThreadProc() {
  return thread_proc_;
}

LPVOID CXWork::GetContext() {
  return context_ptr_;
}

cx_int CXWork::SetName(cx_string strName) {
  name_ = strName;

  return 0;
}

const cx_string &CXWork::GetName() const {
  return name_;
}

cx_int CXWork::Run() {
  ASSERT(thread_proc_);
  if (NULL == thread_proc_) {
    return -1;
  }

#ifdef WIN32
  (*m_pfnThreadProc)(NULL, context_ptr_);
#else
  (*thread_proc_)(context_ptr_);
#endif

  // delete this;

  return 0;
}
}  // namespace common_lib