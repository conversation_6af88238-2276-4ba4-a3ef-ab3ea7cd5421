#ifndef IMAGE_SEGMENT__HH__H
#define IMAGE_SEGMENT__HH__H

#include <string>
#include <memory>
#include "image_segment/image_segment_impl.h"

namespace perception{

class ImageSegmentImpl;


class ImageSegment{
public:
    ImageSegment();
    virtual ~ImageSegment();

    virtual bool Init(std::string model_path);
    virtual bool Inference(cv::Mat src,float box_thresh,float nms_thresh,SegmentDetectResult& out);

    cv::Mat DebugImg(cv::Mat src, const SegmentDetectResult& segment);

protected:
    std::unique_ptr<ImageSegmentImpl>         image_segment_impl_;
};

}

#endif