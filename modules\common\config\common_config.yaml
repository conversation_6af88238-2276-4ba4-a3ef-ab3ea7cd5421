module: common
version: v1.0


sensor: 
  color_image_topic:        /camera/color/image_raw             #彩色相机图像话题
  color_camera_info_topic:  /camera/color/camera_info           #彩色相机内参话题
  depth_image_topic:        /camera/depth/image_rect_raw        #深度相机图像话题
  depth_camera_info_topic:  /camera/depth/camera_info           #深度相机内参话题
  depth_to_color_topic:     /camera/extrinsics/depth_to_color   #深度相机到彩色相机外参变换话题

  lidar_topic:  /lslidar_point_cloud
  imu_topic:    /imu
  gnss_topic:   /chattgps
  leg_topic:    /leg_odom

transform:

  extrinsic_T: [0.46, -0.06, 0.01]   #lidar在imu坐标系中的坐标
  extrinsic_R: [ 1, 0, 0,
                  0, 1, 0,
                  0, 0, 1]
  rtk2Lidar_T: [-0.05, -0.05, 0.15] #rtk天线在imu坐标系中的坐标


  color_to_lidar_q: [-0.55854,0.43934,-0.43418,0.5536]      #color相机在雷达坐标系下的朝向(四元素 w x y z)
  color_to_lidar_t: [0.06464,0.06732,-0.1364]               #color相机在雷达坐标系下的平移

node_output:
  perception_output_topic: /perception/terrain_pointcloud   #perception节点输出结果话题