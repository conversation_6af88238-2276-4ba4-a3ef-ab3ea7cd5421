#include<iostream>
//#include <costmap_2d/costmap_2d_ros.h>
//#include <costmap_2d/costmap_2d.h>
#include <navigation/navigation.h>
//using namespace std;

int main(){

    // 下面通过模块costmap_2d构建了全局代价地图对象，并通过其成员函数pause()暂停运行，将在必要的功能模块初始化结束之后通过成员接口start()开启。
   costmap_2d::Costmap2DROS* planner_costmap_ros_ = new costmap_2d::Costmap2DROS("global_costmap");
   
   // 验证costmap初始化
   std::cout << "=== Costmap功能验证 ===" << std::endl;
   
   // 获取costmap对象
   costmap_2d::Costmap2D* costmap = planner_costmap_ros_->getCostmap();
   if (costmap) {
       std::cout << "✅ Costmap初始化成功" << std::endl;
       std::cout << "地图尺寸: " << costmap->getSizeInCellsX() << "x" << costmap->getSizeInCellsY() << std::endl;
       std::cout << "分辨率: " << costmap->getResolution() << " m/cell" << std::endl;
       
       // 检查地图数据
       unsigned char* char_map = costmap->getCharMap();
       if (char_map) {
           std::cout << "✅ 地图数据获取成功" << std::endl;
           
           // 统计不同类型的单元格
           int free_cells = 0, occupied_cells = 0, unknown_cells = 0;
           int total_cells = costmap->getSizeInCellsX() * costmap->getSizeInCellsY();
           
           // 添加调试信息：检查前100个单元格的值
           std::cout << "前1700个单元格的值:" << std::endl;
           for (int i = 0; i < std::min(1700, total_cells); i++) {
               std::cout << (int)char_map[i] << " ";
               if ((i + 1) % 20 == 0) std::cout << std::endl;
           }
           std::cout << std::endl;
           
           for (int i = 0; i < total_cells; i++) {
               if (char_map[i] == costmap_2d::FREE_SPACE) free_cells++;
               else if (char_map[i] == costmap_2d::LETHAL_OBSTACLE) occupied_cells++;
               else if (char_map[i] == costmap_2d::NO_INFORMATION) unknown_cells++;
           }
           
           std::cout << "地图统计:" << std::endl;
           std::cout << "  空闲区域: " << free_cells << " 单元格 (" << (free_cells*100.0/total_cells) << "%)" << std::endl;
           std::cout << "  障碍区域: " << occupied_cells << " 单元格 (" << (occupied_cells*100.0/total_cells) << "%)" << std::endl;
           std::cout << "  未知区域: " << unknown_cells << " 单元格 (" << (unknown_cells*100.0/total_cells) << "%)" << std::endl;
       } else {
           std::cout << "❌ 地图数据获取失败" << std::endl;
       }
   } else {
       std::cout << "❌ Costmap初始化失败" << std::endl;
   }
   
   navigation_system::navigation *navigation_  = new navigation_system::navigation(planner_costmap_ros_);
   navigation_->start();

}




