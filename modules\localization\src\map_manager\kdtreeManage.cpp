#include "kdtreeManage.h"
#include "cx_thread_pool.h"
#include "cx_auto_mutex.h"

KdtreeManage* GetSingleton4KdtreeManage()
{
    static CXSingleton<KdtreeManage> s_KdtreeManage;
    return s_KdtreeManage.GetSingletonInstance();
}

KdtreeManage::KdtreeManage()
    : m_Config(common_lib::YamlConfig::GetInstance()),
    m_pThread(nullptr),
    m_istatus_tree1(0),
    m_istatus_tree2(0),
    m_ilast_ikdtree(1),
    m_bflag_ikdtree_initial(false)
{
    Initialize();
}

KdtreeManage::~KdtreeManage()
{

}

void KdtreeManage::Initialize()
{
    m_kdtree1.reset(new pcl::KdTreeFLANN<PointType>());
    m_kdtree2.reset(new pcl::KdTreeFLANN<PointType>());
    m_kdtree.reset(new pcl::KdTreeFLANN<PointType>());
    m_kdtree_last.reset(new pcl::KdTreeFLANN<PointType>());

    string sMapPath = m_Config.GetParam<std::string>("loadmappath",
                                                    "/mnt/d/work/01_code/jianmi/20250319/na_localization_lslidar/src/na_localization/");
    double dFilterSizeMapMin = m_Config.GetParam<double>("filter_size_map", 0.5);
    InitMapManage(dFilterSizeMapMin, sMapPath);
    Start();
}

void KdtreeManage::Start()
{
    CXWork::SetName("KdtreeManage");
    CXWork::SetThreadProc(KdtreeManage::MainLoop);
    CXWork::SetThreadContext(this);
    GetSingleton4ThreadPool()->ExecuteTask(this, m_pThread);
}

#ifdef WIN32
void KdtreeManage::MainLoop(PTP_CALLBACK_INSTANCE Instance, PVOID pContext)
#else
void* KdtreeManage::MainLoop(void *pContext)
#endif
{
    ASSERT(pContext);

    if (NULL == pContext)
    {
#ifdef WIN32
        return;
#else
        return NULL;
#endif // WIN32
    }

    KdtreeManage* pkdm = static_cast<KdtreeManage*>(pContext);

    while (pkdm && (NULL != pkdm->m_pThread) && (!pkdm->m_pThread->IsTerminate()))
    {
        pkdm->Run();
        SleepMS(5);
    }

#ifdef WIN32
        return;
#else
        return NULL;
#endif // WIN32
}

void KdtreeManage::InitMapManage(double dFilterSizeMapMin, string sMapPath)
{
    m_mapManagement.set_ds_size(dFilterSizeMapMin);
    m_mapManagement.set_input_PCD(sMapPath);
    m_mapManagement.voxel_process(); 
}

void KdtreeManage::SetCurPoint(state_ikfom &CurPos)
{
    CXAutoMutex amx(m_mtxTree);
    m_state_point = CurPos;
    m_bPtUpdate = true;
    if(m_bflag_ikdtree_initial == false)
    {
        m_mapManagement.get_map(m_state_point.pos[0], m_state_point.pos[1]);
        m_bflag_ikdtree_initial = true;
        m_kdtree1->setInputCloud(m_mapManagement.pointcloud_output);
        m_kdtree2->setInputCloud(m_mapManagement.pointcloud_output);
        printf("ikdtree初始化完成\n");
    }
}

void KdtreeManage::UpdateKdTree()
{
    if (m_bflag_ikdtree_initial)
    {
        //m_bPtUpdate = false;
        bool bgetmap = m_mapManagement.get_map(m_state_point.pos[0], m_state_point.pos[1]);
        // cout << "UpdateKdTree: m_state_point.pos[0] = " << m_state_point.pos[0]
        //      << ", m_state_point.pos[1] = " << m_state_point.pos[1] 
        //      <<", m_mapManagement.get_map "<<bgetmap<<endl;
        if(bgetmap)
        {
            CXAutoMutex amx(m_mtxTree);
            if (m_istatus_tree1 == 0 && m_ilast_ikdtree == 2)
            {
                m_istatus_tree1 = 1;
                pcl::PointCloud<PointType>::Ptr cloud_copy;
                cloud_copy.reset(new pcl::PointCloud<PointType>());
                *cloud_copy = *(m_mapManagement.pointcloud_output);
                m_kdtree1->setInputCloud(cloud_copy);
                m_istatus_tree1 = 0;
                m_ilast_ikdtree = 1;
                cout<<"UpdateKdTree1" << endl;
            }
            else if (m_istatus_tree2 == 0 && m_ilast_ikdtree == 1)
            {
                m_istatus_tree2 = 1;
                pcl::PointCloud<PointType>::Ptr cloud_copy;
                cloud_copy.reset(new pcl::PointCloud<PointType>());
                *cloud_copy = *(m_mapManagement.pointcloud_output);
                m_kdtree2->setInputCloud(cloud_copy);
                m_istatus_tree2 = 0;
                m_ilast_ikdtree = 2;
                cout<<"UpdateKdTree2" << endl;
            }
            else
            {
                cout<<"UpdateKdTree Error!"<<endl;
            }
        }
        // cout << "UpdateKdTree: m_istatus_tree1 = " << m_istatus_tree1
        //      << ", m_istatus_tree2 = " << m_istatus_tree2
        //      << ", m_ilast_ikdtree = " << m_ilast_ikdtree << endl;
    }
}

pcl::KdTreeFLANN<PointType>::Ptr KdtreeManage::getKdTree()
{
    // cout<< "getKdTree: m_istatus_tree1 = " << m_istatus_tree1
    //      << ", m_istatus_tree2 = " << m_istatus_tree2
    //      << ", m_ilast_ikdtree = " << m_ilast_ikdtree << endl;  
    CXAutoMutex amx(m_mtxTree);
    if (m_istatus_tree1 == 0 && m_ilast_ikdtree == 1)
    {
        m_istatus_tree1 = 2;
        m_kdtree = m_kdtree1;
        m_kdtree_last = m_kdtree1; // 保存上一次的kdtree
        m_istatus_tree1 = 0;
    }
    else if (m_istatus_tree2 == 0 && m_ilast_ikdtree == 2)
    {
        m_istatus_tree2 = 2;
        m_kdtree = m_kdtree2;
        m_kdtree_last = m_kdtree2; // 保存上一次的kdtree
        m_istatus_tree2 = 0;
    }
    else
    {
        //cout << "getKdTree Error!" << endl;
        m_kdtree = m_kdtree_last; // 如果没有可用的kdtree，返回上一次的kdtree
    }
    return m_kdtree;
}

cx_int KdtreeManage::Run()
{
    // Main processing loop
    UpdateKdTree();
    return 0;
}