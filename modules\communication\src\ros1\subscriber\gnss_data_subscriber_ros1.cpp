#include "subscriber/gnss_data_subscriber_ros1.h"


#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1{

GnssDataSubscriberRos1::GnssDataSubscriberRos1(ros::NodeHandle &nh, const std::string &gnss_topic, size_t max_buffer_size)
            : GnssDataSubscriberBase(gnss_topic, max_buffer_size), nh_(nh) 
{
            subscriber_ = nh_.subscribe(gnss_topic, max_buffer_size, &GnssDataSubscriberRos1::GnssDataCallbackRos1, this);
}

//消息回调函数
void GnssDataSubscriberRos1::GnssDataCallbackRos1(const sensor_msgs::NavSatFix::ConstPtr &gnss_msg) 
{
  //消息转换
  GNSSData gnss_data;
  gnss_data.time = gnss_msg->header.stamp.toSec();
  gnss_data.latitude = gnss_msg->latitude;
  gnss_data.longitude = gnss_msg->longitude;
  gnss_data.altitude = gnss_msg->altitude;
  // gnss_data.roll = 0.0;  // Placeholder, as NavSatFix does not provide roll
  // gnss_data.pitch = 0.0; // Placeholder, as NavSatFix does not provide pitch
  // gnss_data.yaw = 0.0;   // Placeholder, as NavSatFix does not provide yaw
  // gnss_data.vel_x = 0.0; // Placeholder, as NavSatFix does not provide velocity
  // gnss_data.vel_y = 0.0; // Placeholder, as NavSatFix does not provide velocity
  // gnss_data.vel_z = 0.0; // Placeholder, as NavSatFix does not provide velocity
  gnss_data.rtk_status = gnss_msg->status.status; // Placeholder, as NavSatFix does not provide RTK status
  
  // double pose_std = 0.05;//固定解下的标准差
  // if(gnss_msg->status.status == 5)
  // {
  //     pose_std = 0.5;
  // }
  // else if(gnss_msg->status.status != 4)
  // {
  //     pose_std = 20.0;
  // }
  // double posecov = pose_std * pose_std;
  // gnss_data.llh_std[0] = posecov; // Placeholder, as NavSatFix does not provide LLH standard deviation
  // gnss_data.llh_std[1] = posecov; // Placeholder, as NavSatFix does not provide LLH standard deviation
  // gnss_data.llh_std[2] = 2.0 * posecov; // Placeholder, as NavSatFix does not provide LLH standard deviation
  
  // 将转换后的GNSS数据存入缓冲区
  std::lock_guard<std::mutex> lock(buffer_mutex_);
  data_buffer_.push_back(gnss_data);
  // Check if the buffer exceeds the maximum size 
  if (data_buffer_.size() > max_buffer_size_) {
    data_buffer_.pop_front(); // Remove the oldest data if buffer is full
  }
}

}   // namespace communication::ros1{

#endif
    