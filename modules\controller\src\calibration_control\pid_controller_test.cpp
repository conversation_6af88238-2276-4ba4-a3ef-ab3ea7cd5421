#include <gtest/gtest.h>
#include "../include/pid_controller.hpp"
#include <chrono>
#include <thread>

using namespace calibration;

class PIDControllerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建PID控制器实例，使用合理的参数
        pid_ = std::make_unique<PIDController>(1.0, 0.1, 0.01);
    }

    void TearDown() override {
        pid_.reset();
    }

    std::unique_ptr<PIDController> pid_;
};

// 测试基本功能
TEST_F(PIDControllerTest, BasicFunctionality) {
    const double target = 10.0;
    const double dt = 0.1;
    double current = 0.0;
    double output;

    // 模拟PID控制过程
    for (int i = 0; i < 100; ++i) {
        double error = target - current;
        output = pid_->compute(error, dt);
        current += output * dt;
    }

    // 验证最终结果是否接近目标值
    EXPECT_NEAR(current, target, 0.1);
}

// 测试参数设置
TEST_F(PIDControllerTest, ParameterSetting) {
    // 设置新的PID参数
    pid_->setParameters(2.0, 0.2, 0.02);
    
    // 验证参数是否正确设置
    const double target = 10.0;
    const double dt = 0.1;
    double current = 0.0;
    double output;

    // 模拟PID控制过程
    for (int i = 0; i < 100; ++i) {
        double error = target - current;
        output = pid_->compute(error, dt);
        current += output * dt;
    }

    // 验证最终结果是否接近目标值
    EXPECT_NEAR(current, target, 0.1);
}

// 测试输出限制
TEST_F(PIDControllerTest, OutputLimits) {
    // 设置输出限制
    pid_->setOutputLimits(-1.0, 1.0);
    
    const double target = 10.0;
    const double dt = 0.1;
    double current = 0.0;
    double output;

    // 模拟PID控制过程
    for (int i = 0; i < 100; ++i) {
        double error = target - current;
        output = pid_->compute(error, dt);
        // 验证输出是否在限制范围内
        EXPECT_GE(output, -1.0);
        EXPECT_LE(output, 1.0);
        current += output * dt;
    }
}

// 测试积分限制
TEST_F(PIDControllerTest, IntegralLimits) {
    // 设置积分限制
    pid_->setIntegralLimits(-5.0, 5.0);
    
    const double target = 10.0;
    const double dt = 0.1;
    double current = 0.0;
    double output;

    // 模拟PID控制过程
    for (int i = 0; i < 100; ++i) {
        double error = target - current;
        output = pid_->compute(error, dt);
        // 验证积分项是否在限制范围内
        EXPECT_GE(pid_->getIntegral(), -5.0);
        EXPECT_LE(pid_->getIntegral(), 5.0);
        current += output * dt;
    }
}

// 测试重置功能
TEST_F(PIDControllerTest, ResetFunctionality) {
    const double target = 10.0;
    const double dt = 0.1;
    double current = 0.0;
    double output;

    // 运行一段时间
    for (int i = 0; i < 50; ++i) {
        double error = target - current;
        output = pid_->compute(error, dt);
        current += output * dt;
    }

    // 重置控制器
    pid_->reset();

    // 验证重置后的状态
    EXPECT_DOUBLE_EQ(pid_->getIntegral(), 0.0);
    EXPECT_DOUBLE_EQ(pid_->getLastError(), 0.0);

    // 继续运行
    for (int i = 0; i < 50; ++i) {
        double error = target - current;
        output = pid_->compute(error, dt);
        current += output * dt;
    }

    // 验证最终结果
    EXPECT_NEAR(current, target, 0.1);
}

// 测试异常处理
TEST_F(PIDControllerTest, ExceptionHandling) {
    // 测试负的PID参数
    EXPECT_THROW(pid_->setParameters(-1.0, 0.1, 0.01), std::invalid_argument);
    
    // 测试负的时间步长
    EXPECT_THROW(pid_->compute(1.0, -0.1), std::invalid_argument);
    
    // 测试无效的输出限制
    EXPECT_THROW(pid_->setOutputLimits(1.0, -1.0), std::invalid_argument);
    
    // 测试无效的积分限制
    EXPECT_THROW(pid_->setIntegralLimits(1.0, -1.0), std::invalid_argument);
}

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
} 