<?xml version="1.0"?>
<launch>
  <!-- Controller Module Simple Launch File -->
  <!-- 使用绝对路径启动calibration_node和velocity_smoother_node -->
  
  <!-- 硬编码路径 -->
  <arg name="config_dir" default="/home/<USER>/modules/controller/config" />
  <arg name="rviz_config" default="/home/<USER>/modules/controller/rviz/controller.rviz" />
  <arg name="use_rviz" default="true" />
  
  <!-- 加载配置文件 -->
  <rosparam command="load" file="$(arg config_dir)/calibration.yaml" />
  <rosparam command="load" file="$(arg config_dir)/velocity_smoother_config.yaml" />
  
  <!-- 启动calibration_node -->
  <node name="calibration_node" pkg="controller" type="calibration_node" output="screen">
    <param name="config_file" value="$(arg config_dir)/calibration.yaml" />
    <param name="communication_config" value="$(arg config_dir)/communication_config.yaml" />
    
    <!-- 重映射话题 -->
    <remap from="/odometry" to="/Odometry" />
    <remap from="/goal" to="/move_base_simple/goal" />
    <remap from="/web_goal" to="/web_goal" />
    <remap from="/mode" to="/calibration_mode" />
    <remap from="/cmd_vel" to="/cmd_vel_raw" />
    <remap from="/stop" to="/emergency_stop" />
    <remap from="/inner_stop" to="/inner_emergency_stop" />
    <remap from="/calibration_mode" to="/calibration_mode_status" />
  </node>
  
  <!-- 启动velocity_smoother_node -->
  <node name="velocity_smoother_node" pkg="controller" type="velocity_smoother_node" output="screen">
    <param name="config_file" value="$(arg config_dir)/velocity_smoother_config.yaml" />
    <param name="communication_config" value="$(arg config_dir)/communication_config.yaml" />
    
    <!-- 重映射话题 -->
    <remap from="/twist/data" to="/twist/data" />
    <remap from="/cmd_vel" to="/cmd_vel_smooth" />
    <remap from="/odometry" to="/Odometry" />
    
    <!-- 速度平滑器参数 -->
    <param name="speed_lim_v" value="2.0" />
    <param name="speed_lim_w" value="1.5" />
    <param name="accel_lim_v" value="1.0" />
    <param name="accel_lim_w" value="0.8" />
    <param name="decel_factor" value="2.0" />
    <param name="frequency" value="50.0" />
    <param name="quiet" value="false" />
    <param name="robot_feedback" value="0" />
  </node>
  
  <!-- 启动RViz可视化 -->
  <group if="$(arg use_rviz)">
    <node name="rviz" pkg="rviz" type="rviz" args="-d $(arg rviz_config)" output="screen" />
  </group>
  
  <!-- 启动静态TF发布器 -->
  <node pkg="tf2_ros" type="static_transform_publisher" name="base_link_to_map" 
        args="0 0 0 0 0 0 map base_link" />
  
  <!-- 启动TF监听器 -->
  <node pkg="tf2_ros" type="buffer_server" name="tf2_buffer_server" />
  
  <!-- 启动TF监听器 -->
  <node pkg="tf2_ros" type="transform_listener" name="tf2_listener" />
  
</launch> 