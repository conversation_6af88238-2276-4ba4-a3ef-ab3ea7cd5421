#ifndef CMMESSAGE_H
#define CMMESSAGE_H

#include "cx_pubhead.h"
namespace common_lib {
class CXMessage {
 public:
  CXMessage(cx_uint messageId);
  ~CXMessage();

 public:
  cx_uint m_messageId;
  cx_uint m_wParam;
  void *m_lParam;
  cx_uint64 m_time;

 public:
  CXMessage &operator=(const CXMessage &rightValue);
};

typedef std::deque<CXMessage *> CXMessageDeque;
}  // namespace common_lib

#endif  // CMMESSAGE_H
