<?xml version="1.0"?>
<launch>
  <!-- 加载 全局参数 -->
  <rosparam command="load" file="$(find communication_test)/config/communication_config.yaml" />

  <!-- 启动可视化 rivi 文件 -->
  <!-- <node name="rviz_planning" pkg="rviz" type="rviz" args="-d $(find planning)/src/ros_viewer/planning.rviz"/> -->

  <!-- 启动节点 communication_node -->
  <node pkg="communication_test" type="communication_test"  name="communication_test" output="screen" /> 
  <!-- respawn="false",  -->


</launch>