#include "common_types.h"
#include <iostream>

int main() {
    std::cout << "Testing common types..." << std::endl;
    
    // 测试TimeStamp
    planning_common::TimeStamp ts = planning_common::TimeStamp::now();
    std::cout << "TimeStamp: " << ts.toSec() << std::endl;
    
    // 测试Point
    planning_common::Point p(1.0, 2.0, 3.0);
    std::cout << "Point: (" << p.x << ", " << p.y << ", " << p.z << ")" << std::endl;
    
    // 测试Quaternion
    planning_common::Quaternion q = planning_common::Quaternion::fromYaw(1.57);
    std::cout << "Quaternion: (" << q.x << ", " << q.y << ", " << q.z << ", " << q.w << ")" << std::endl;
    
    // 测试Pose
    planning_common::Pose pose(p, q);
    std::cout << "Pose position: (" << pose.position.x << ", " << pose.position.y << ", " << pose.position.z << ")" << std::endl;
    
    // 测试PoseStamped
    planning_common::PoseStamped ps(ts.toSec(), p, q);
    std::cout << "PoseStamped time: " << ps.time << std::endl;
    
    // 测试PathData
    planning_common::PathData path;
    path.poses_.push_back(ps);
    std::cout << "PathData size: " << path.size() << std::endl;
    
    // 测试TwistData
    planning_common::TwistData twist(1.0, 0.0, 0.0, 0.0, 0.0, 0.5);
    std::cout << "TwistData linear: (" << twist.linear.x << ", " << twist.linear.y << ", " << twist.linear.z << ")" << std::endl;
    
    std::cout << "All tests passed!" << std::endl;
    return 0;
} 