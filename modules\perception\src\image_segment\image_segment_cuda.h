#ifndef __IMAGE_SEGMENT__WITH__CUDA__H
#define __IMAGE_SEGMENT__WITH__CUDA__H

#include "image_segment/image_segment_impl.h"
#include "image_segment/yolov8_cuda/logging.h"
#include <NvInfer.h>

namespace perception{


 
    
class ImageSegmentCuda : public ImageSegmentImpl{

    const static int   kMaxInputImageSize=4096;
    const static int   kGpuId=0;
    const static int   kBatchSize=1;
    const static int   kInputH = 640;
    const static int   kInputW = 640;
    const static int   kMaxNumOutputBbox = 1000;
    const static int   kOutputSegSize = 32 * (kInputH / 4) * (kInputW / 4);
    const static int   kNumberOfPoints = 17;

    typedef struct{
        //center_x center_y w h
        float bbox[4];
        float conf;  // bbox_conf * cls_conf
        float class_id;
        float mask[32];
        float keypoints[kNumberOfPoints * 3];  // keypoints array with dynamic size based on kNumberOfPoints
        float angle;                           // obb angle
    }Detection;

    const static int   kOutputSize = kMaxNumOutputBbox * sizeof(Detection) / sizeof(float) + 1;

    
public:
    ImageSegmentCuda();
    virtual ~ImageSegmentCuda();
    virtual bool Init(const std::string& model_path);
    virtual bool Inference(cv::Mat src,float box_thresh,float nms_thresh,SegmentDetectResult& out);

protected:

    float*  cpu_to_cuda(cv::Mat image);
    void    prepare_buffer();
    void    release();

    
protected:
    nvinfer1::IRuntime*             runtime_ = nullptr;
    nvinfer1::ICudaEngine*          engine_ = nullptr;
    nvinfer1::IExecutionContext*    context_ = nullptr;
    cudaStream_t                    stream_;

    float*                          device_buffers_[3];     // input_buffer_device,output_buffer_device,output_seg_buffer_device
    float*                          output_buffer_host_ = nullptr;
    float*                          output_seg_buffer_host_ = nullptr;
    int                             model_bboxes_;

    Logger                          logger_;

};

}

#endif