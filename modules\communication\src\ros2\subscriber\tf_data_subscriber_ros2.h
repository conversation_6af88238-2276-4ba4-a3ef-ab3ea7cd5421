#pragma once
#include <rclcpp/rclcpp.hpp>
#include <rclcpp/duration.hpp>
#include <tf2_ros/buffer.h>
#include <tf2_ros/transform_listener.h>
#include <geometry_msgs/msg/transform_stamped.hpp>

#include "ros2/core/subscriber_base_ros2.h"
#include "data_types/pose_data.h"

namespace communication::ros2
{

    class TFDataSubscriberRos2 : public TFDataSubscriberBase
    {
    public:
        TFDataSubscriberRos2(rclcpp::Node::SharedPtr node,
                             const std::string &target_frame,
                             const std::string &source_frame)
            : TFDataSubscriberBase(source_frame + "_" + target_frame + "_tf", 1), node_(node),
              tf_buffer_(node_->get_clock()), tf_listener_(tf_buffer_), target_frame_(target_frame), source_frame_(source_frame)
        {
        }
        ~TFDataSubscriberRos2() = default;

    protected:
        virtual PoseData GetBufferFront() override
        {
            PoseData data;
            try
            {
                auto transform = tf_buffer_.lookupTransform(
                    target_frame_, source_frame_, tf2::TimePointZero, tf2::durationFromSec(10.0));

                data.time = rclcpp::Time(transform.header.stamp).seconds();
                data.position[0] = transform.transform.translation.x;
                data.position[1] = transform.transform.translation.y;
                data.position[2] = transform.transform.translation.z;
                data.orientation[0] = transform.transform.rotation.x;
                data.orientation[1] = transform.transform.rotation.y;
                data.orientation[2] = transform.transform.rotation.z;
                data.orientation[3] = transform.transform.rotation.w;
                return data;
            }
            catch (tf2::TransformException &ex)
            {
                RCLCPP_WARN(node_->get_logger(), "%s", ex.what());
                return data;
            }
        }

    private:
        rclcpp::Node::SharedPtr node_;
        tf2_ros::Buffer tf_buffer_;
        tf2_ros::TransformListener tf_listener_;
        std::string target_frame_;
        std::string source_frame_;
    };

} // namespace communication::ros2
