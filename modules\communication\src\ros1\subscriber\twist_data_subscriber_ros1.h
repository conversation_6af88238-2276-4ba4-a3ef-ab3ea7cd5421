/**
 * @file twist_data_subscriber_ros1.h
 * @brief This file contains the implementation of a ROS1 subscriber for twist data.
 * It subscribes to a ROS topic that publishes geometry_msgs::Twist messages and converts them into a custom TwistData format.
 * The subscriber handles the incoming messages and stores the twist data in a thread-safe manner.
 * It inherits from the TwistDataSubscriberBase class.
 */
#pragma once

#include <ros/ros.h>
#include <geometry_msgs/Twist.h>

#include "data_types/twist_data.h"
#include "subscriber_base.h"

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1 {

class TwistDataSubscriberRos1 : public TwistDataSubscriberBase {
public:
    TwistDataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size = 10);
    virtual ~TwistDataSubscriberRos1() = default;

private:
    ros::NodeHandle &nh_;
    ros::Subscriber subscriber_;
    void TwistDataCallbackRos1(const geometry_msgs::Twist::ConstPtr &msg);
};

}// namespace communication::ros1

#endif // COMMUNICATION_TYPE == ROS1
