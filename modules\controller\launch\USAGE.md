# Controller Module 启动说明

## 问题说明

由于本项目不是标准的ROS工作空间，而是使用自定义构建系统，因此无法使用标准的`roslaunch controller controller.launch`命令。

## 解决方案

### 方案1: 使用Bash脚本启动（推荐）

```bash
# 进入controller目录
cd modules/controller

# 运行启动脚本
./scripts/start_controller.sh
```

这个脚本会：
- 自动设置正确的环境变量
- 检查可执行文件和配置文件是否存在
- 启动calibration_node和velocity_smoother_node
- 提供进程管理（Ctrl+C停止所有节点）

### 方案2: 手动启动节点

```bash
# 设置环境变量
export LD_LIBRARY_PATH="/home/<USER>/modules/controller/lib:/home/<USER>/modules/common/lib:/home/<USER>/modules/communication/lib:$LD_LIBRARY_PATH"

# 进入bin目录
cd modules/controller/bin

# 启动calibration_node
./calibration_node ../config/calibration.yaml &

# 启动velocity_smoother_node
./velocity_smoother_node ../config/velocity_smoother_config.yaml &
```

### 方案3: 使用绝对路径的launch文件

```bash
# 使用绝对路径启动
roslaunch /home/<USER>/modules/controller/launch/controller_simple.launch
```

### 方案4: 设置ROS包路径

```bash
# 临时添加包路径到ROS_PACKAGE_PATH
export ROS_PACKAGE_PATH="/home/<USER>/modules/controller:$ROS_PACKAGE_PATH"

# 然后可以使用标准命令
roslaunch controller controller.launch
```

## 验证节点是否启动成功

### 检查进程
```bash
# 查看是否有相关进程
ps aux | grep -E "(calibration_node|velocity_smoother_node)"
```

### 检查话题
```bash
# 列出所有话题
rostopic list

# 查看特定话题
rostopic echo /cmd_vel_smooth
rostopic echo /Odometry
```

### 检查节点
```bash
# 列出所有节点
rosnode list

# 查看节点信息
rosnode info /calibration_node
rosnode info /velocity_smoother_node
```

## 故障排除

### 1. 找不到可执行文件
```bash
# 检查文件是否存在
ls -la modules/controller/bin/
```

### 2. 找不到配置文件
```bash
# 检查配置文件是否存在
ls -la modules/controller/config/
```

### 3. 库文件路径问题
```bash
# 检查库文件是否存在
ls -la modules/controller/lib/
ls -la modules/common/lib/
ls -la modules/communication/lib/
```

### 4. 权限问题
```bash
# 给脚本添加执行权限
chmod +x modules/controller/scripts/start_controller.sh
```

## 推荐使用方式

**推荐使用方案1（Bash脚本）**，因为：
- 自动处理环境变量
- 提供错误检查
- 进程管理更简单
- 不依赖ROS包系统

## 示例完整流程

```bash
# 1. 编译项目
cd /home/<USER>
./build.sh --p x86_64 --b release --c ros1 --n cpu

# 2. 启动controller节点
cd modules/controller
./scripts/start_controller.sh

# 3. 在另一个终端验证
rostopic list
rosnode list
``` 