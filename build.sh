#!/bin/bash
# Support platform: x86_64
# Build type: debug; release
# Communication type: ros1

#默认值

platform=x86_64
build_type=release
commnunication_type=ros1
neural_type=cpu
core_count=$(nproc)

platform_list=(
  x86_64
)

build_type_list=(
  debug
  release
)

communication_type_list=(
  ros1
  ros2
)

neural_type_list=(
  rknn
  cuda
  openvino
  cpu
)
function print_platform_and_build_type {
  echo "Supported platform: ${platform_list[@]}"
  echo "Supported build type: ${build_type_list[@]}"
  echo "Supported communication type: ${communication_type_list[@]}"
  echo "Supported neurak type: ${neural_type_list[@]}"
}

function check_platform() {
  found=1
  for item in "${platform_list[@]}"; do
    if [ "${item}" == "$1" ]; then
      found=0
      break
    fi
  done

  echo $found
}

function check_build_type() {
  found=1
  for item in "${build_type_list[@]}"; do
    if [ "${item}" == "$1" ]; then
      found=0
      break
    fi
  done
  echo $found
}

function check_communication_type() {
  found=1
  for item in "${communication_type_list[@]}"; do
    if [ "${item}" == "$1" ]; then
      found=0
      break
    fi
  done

  echo $found 
}

function check_neural_type() {
  found=1
  for item in "${neural_type_list[@]}"; do
    if [ "${item}" == "$1" ]; then
      found=0
      break
    fi
  done

  echo $found 
}


while [ $# -gt 0 ]; do
    case "$1" in
        --p)
            if [ $(check_platform $2) -eq 1 ]; then
              echo "Platform: ${platform} is not supported!"
              print_platform_and_build_type
              exit 1
            fi
            platform="$2"
            shift 2 # 跳过 --name 和它的值
            ;;
        --b)
            if [ $(check_build_type $2) -eq 1 ]; then
              echo "Build type: ${build_type} is not supported!"
              print_platform_and_build_type
              exit 1
            fi
            build_type="$2"
            shift 2
            ;;
        --c)
            if [ $(check_communication_type $commnunication_type) -eq 1 ]; then
              echo "Communication type: ${commnunication_type} is not supported!"
              print_platform_and_build_type
              exit 1
            fi
            commnunication_type="$2"
            shift 2
            ;;
        --n)
            if [ $(check_neural_type $neural_type) -eq 1 ]; then
              echo "Neural type: ${neural_type} is not supported!"
              print_platform_and_build_type
              exit 1
            fi
            neural_type="$2"
            shift 2
            ;;
        -j[0-9]*)
            # 提取 -jn 中的数字
            core_count=${1#-j}
            # 验证 job_count 是否为正整数
            if ! [[ "$core_count" =~ ^[0-9]+$ ]] || [ "$core_count" -eq 0 ]; then
                echo "错误：-j 后必须跟一个正整数"
                exit 1
            fi
            shift
            ;;
        --h)
            echo "Usage: $0 [--p <platform>] [--b <build_type>] [--c <communication_type>] [--n <neural_type>]"
            echo "Supported platforms           (--p) :   x86_64(default)"
            echo "Supported build types         (--b) :   debug, release(default)"
            echo "Supported communication types (--c) :   ros1(default), ros2"
            echo "Supported neural types        (--n) :   rknn, cuda, openvino, cpu(default)"
            echo "Example                             :   $0 --p x86_64 --b release --c ros1 --n cpu"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done



current_dir=$(cd `dirname $0`; pwd)
echo "Current directory: $current_dir"


echo "Platform: ${platform}"
echo "Build type: ${build_type}"
echo "Commnuication type: ${commnunication_type}"
echo "neural type: ${neural_type}"

if [ $(check_platform $platform) -eq 1 ]; then
  echo "Platform: ${platform} is not supported!"
  print_platform_and_build_type
  exit 1
fi

if [ $(check_build_type $build_type) -eq 1 ]; then
  echo "Build type: ${build_type} is not supported!"
  print_platform_and_build_type
  exit 1
fi

if [ $(check_communication_type $commnunication_type) -eq 1 ]; then
  echo "Communication type: ${commnunication_type} is not supported!"
  print_platform_and_build_type
  exit 1
fi

if [ $(check_neural_type $neural_type) -eq 1 ]; then
  echo "Neural type: ${neural_type} is not supported!"
  print_platform_and_build_type
  exit 1
fi

build_dir="${current_dir}/build_${platform}_${build_type}_${commnunication_type}_${neural_type}"

if [ ! -d $build_dir ]; then
  mkdir $build_dir
fi

if [ "${commnunication_type}" == "ros1" ];then
  commnunication_type=ROS1
fi
if [ "${commnunication_type}" == "ros2" ];then
  commnunication_type=ROS2
fi

cd $build_dir 

cmake -DCMAKE_BUILD_TYPE=$build_type -DCOMMUNICATION_TYPE=${commnunication_type} -DNEURAL_TYPE=${neural_type} .. 
if [ $? -ne 0 ]; then
  exit 1
fi

echo "cpu core: ${core_count}  make -j${core_count}"

make -j"$core_count"

if [ $? -ne 0 ]; then
  exit 1
fi
