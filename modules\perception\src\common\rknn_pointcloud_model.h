// rknn_model.h
#pragma once

#include "rknn_api.h"
#include <vector>
#include <string>
#include <opencv2/opencv.hpp>

namespace perception{

typedef struct {
    rknn_context ctx;
    rknn_input_output_num io_num;
    rknn_tensor_attr* input_attrs;
    rknn_tensor_attr* output_attrs;
    bool is_quant;
    int model_width;
    int model_height;
} rknn_app_context_t;

class RKNNPointCloudModel {
    public:
        explicit RKNNPointCloudModel();
        ~RKNNPointCloudModel();
    
        // 初始化模型
        bool init(const std::string& model_path);
    
        // 推理接口：输入 depth_map (uint16) 和 camera_params [fx, fy, cx, cy]
        bool infer(const cv::Mat& depth_map, const float* camera_params, std::vector<float>& cloud_data);
    
    private:
        bool init_;
        rknn_app_context_t* app_ctx_;
    
        static unsigned char* load_model(const std::string& path, int* model_len);
        static void dump_tensor_attr(rknn_tensor_attr* attr);
    };

}