#ifndef __DEPTH_POINTCLOUD_CONVERT_IMPL__H_
#define __DEPTH_POINTCLOUD_CONVERT_IMPL__H_

#include "common/utils.h"

namespace perception{


class DepthPointCloudConvertImpl{


public:
    DepthPointCloudConvertImpl():init_(false){
        
    };
    virtual ~DepthPointCloudConvertImpl(){};

    virtual bool Init(const DepthPointCloudConvertParameter& parameter ){
        parameter_=parameter;
        init_=true;
        return true;
    }
    virtual bool Convert(cv::Mat depth,PointCloudT::Ptr& cloud)=0;
protected:

    bool                                init_;
    DepthPointCloudConvertParameter     parameter_;

};


}


#endif