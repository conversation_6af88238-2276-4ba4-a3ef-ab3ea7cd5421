cmake_minimum_required(VERSION 3.10)
project(costmap_2d)

# 查找Eigen3库
find_package(Eigen3 REQUIRED)
if(NOT EIGEN3_FOUND)
    message(FATAL_ERROR "Eigen3 not found. Please install Eigen3.")
endif()

# 查找catkin包
find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  nav_msgs
  sensor_msgs
  geometry_msgs
  tf
  tf2
  tf2_ros
  tf2_geometry_msgs
  tf2_sensor_msgs
  message_filters
  laser_geometry
  dynamic_reconfigure
  pluginlib
)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../include
    ${EIGEN3_INCLUDE_DIRS}
    ${catkin_INCLUDE_DIRS}
    ${Boost_INCLUDE_DIRS}
)

# 库源文件（不包含main函数的文件）
file(GLOB COSTMAP_LIB_SOURCES 
    ${CMAKE_CURRENT_SOURCE_DIR}/costmap_2d.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/costmap_2d_publisher.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/costmap_2d_ros.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/costmap_layer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/costmap_math.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/footprint.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/observation_buffer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/layered_costmap.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/layer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/array_parser.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/layers/*.cpp
)

# 创建库
add_library(costmap_2d ${COSTMAP_LIB_SOURCES})

# 添加编译定义
target_compile_definitions(costmap_2d PRIVATE HAVE_SYS_TIME_H=1)

# 链接依赖
target_link_libraries(costmap_2d
    ${catkin_LIBRARIES}
    ${Boost_LIBRARIES}
    ${EIGEN3_LIBRARIES}
    voxel_grid  # 链接 voxel_grid 库
)

# 创建独立的可执行文件
# 1. costmap_2d_node
add_executable(costmap_2d_node ${CMAKE_CURRENT_SOURCE_DIR}/costmap_2d_node.cpp)
target_link_libraries(costmap_2d_node
    costmap_2d
    ${catkin_LIBRARIES}
)

# 2. costmap_2d_cloud
add_executable(costmap_2d_cloud ${CMAKE_CURRENT_SOURCE_DIR}/costmap_2d_cloud.cpp)
target_link_libraries(costmap_2d_cloud
    costmap_2d
    ${catkin_LIBRARIES}
)

# 3. costmap_2d_markers
add_executable(costmap_2d_markers ${CMAKE_CURRENT_SOURCE_DIR}/costmap_2d_markers.cpp)
target_link_libraries(costmap_2d_markers
    costmap_2d
    ${catkin_LIBRARIES}
)