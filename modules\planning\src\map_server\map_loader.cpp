#include "map_loader.h"
#include "image_loader.h"
#include <yaml-cpp/yaml.h>
#include <fstream>

MapLoader::MapLoader(const std::string& yaml_file, OccupancyGrid& map) {
  YAML::Node doc = YAML::LoadFile(yaml_file);
  std::string image_file = doc["image"].as<std::string>();
  double resolution = doc["resolution"].as<double>();
  std::vector<double> origin = doc["origin"].as<std::vector<double>>();
  double occupied_thresh = doc["occupied_thresh"].as<double>();
  double free_thresh = doc["free_thresh"].as<double>();

  ImageLoader loader(image_file, occupied_thresh, free_thresh);

  map.info.resolution = resolution;
  map.info.width = loader.getWidth();
  map.info.height = loader.getHeight();
  map.info.origin_position.x = origin[0];
  map.info.origin_position.y = origin[1];
  map.info.origin_position.z = 0.0;
  map.info.origin_orientation.x = 0.0;
  map.info.origin_orientation.y = 0.0;
  map.info.origin_orientation.z = 0.0;
  map.info.origin_orientation.w = 1.0;
  map.data = loader.getData();
}