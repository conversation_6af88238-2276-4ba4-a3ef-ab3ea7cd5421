#ifndef POINT_PUBLISH_H
#define POINT_PUBLISH_H

#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include <Eigen/Dense>
#include <Eigen/Geometry>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <fstream>

// 包含公共结构体定义
#include "common_types.h"

namespace point_publish {

using planning_common::TimeStamp;
using planning_common::Header;
using planning_common::Point;
using planning_common::Vector3;
using planning_common::Quaternion;
using planning_common::Pose;
using planning_common::PoseStamped;
using planning_common::TwistMsg;
using planning_common::OdometryData;
using planning_common::PathData;
using planning_common::TwistData;
using planning_common::BoolMsg;
using planning_common::Int8Msg;

/**
 * @brief 点发布器类
 */
class PointPublisher {
public:
    PointPublisher(const std::string& name);
    ~PointPublisher();
    
    // 初始化和控制
    bool init();
    void start();
    void stop();
    
    // 输入函数 (替换ROS订阅)
    void inputGoal(const point_publish::PoseStamped& goal);
    void inputWebGoal(const point_publish::PoseStamped& webgoal);
    
    // 回调函数设置 (替换ROS发布)
    
    void setWebGoalTargetCallback(std::function<void(const std::shared_ptr<point_publish::PoseStamped>&)> callback);
    
    // 状态查询
    bool isInitialized() const;
    bool isRunning() const;
    
    // 配置管理
    void printStatus() const;
    


private:
    // 成员变量
    std::string name_;
    bool initialized_;
    std::atomic<bool> running_;
    std::thread worker_thread_;
    std::mutex data_mutex_;
    
    // 回调函数
   std::function<void(const std::shared_ptr<point_publish::PoseStamped>&)> web_goal_callback_;
    
    // 原有的处理函数 (完全保留实现)
    void goalHandler(const std::shared_ptr<const point_publish::PoseStamped>& goal);
    void webgoalHandler(const std::shared_ptr<const point_publish::PoseStamped>& webgoal);
    
    // 内部函数
    void controlLoop();
    

};
} // namespace point_publish
#endif // POINT_PUBLISH_H
