#include "point_publish.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <type_traits>
#include "communication.h"


using namespace point_publish;
using namespace communication;

static bool running_flag = true;

std::unique_ptr<PointPublisher> point_publisher;
std::shared_ptr<PoseDataSubscriberBase> goal_subscriber_ptr;
std::shared_ptr<PoseDataSubscriberBase> web_goal_subscriber_ptr;



void DataReceiveThread() {
  while (running_flag) {
    if (!web_goal_subscriber_ptr->IsBufferEmpty()) {
      auto web_goal_data = web_goal_subscriber_ptr->GetBuffer();
      PoseStamped msg;
      msg.pose.position.x = web_goal_data.front().position[0];
      msg.pose.position.y = web_goal_data.front().position[1];
      msg.pose.position.z = web_goal_data.front().position[2];
      const auto& q = web_goal_data.front().orientation;
      msg.pose.orientation.x = q[0];
      msg.pose.orientation.y = q[1];
      msg.pose.orientation.z = q[2];
      msg.pose.orientation.w = q[3];
      std::cout<<"get web goal"<<std::endl;
      point_publisher->inputWebGoal(msg);
    }
    // 接收 /goal 话题的消息并传递给 PointPublisher
    if (!goal_subscriber_ptr->IsBufferEmpty()) {
      auto goal_data = goal_subscriber_ptr->GetBuffer();
      PoseStamped msg;
      msg.pose.position.x = goal_data.front().position[0];
      msg.pose.position.y = goal_data.front().position[1];
      msg.pose.position.z = goal_data.front().position[2];
      const auto& q = goal_data.front().orientation;
      msg.pose.orientation.x = q[0];
      msg.pose.orientation.y = q[1];
      msg.pose.orientation.z = q[2];
      msg.pose.orientation.w = q[3];
      std::cout<<"get goal"<<std::endl;
      point_publisher->inputGoal(msg);
    }
  }
}

void DataSendThread() {
  
}

/**
 * @brief 点发布节点 - 基础节点创建和初始化
 */
int main(int argc, char** argv) {
    std::cout << "=== PointPublisher Node ===" << std::endl;
    
    try {

        auto communication_ptr = std::make_shared<communication::Communication>("point_publish");
        if (!communication_ptr->Initialize("config/communication_config.yaml")) {
            std::cerr << " 通信模块初始化失败" << std::endl;
            return -1;
        }
        point_publisher = std::make_unique<PointPublisher>("point_publish_node");
 
        goal_subscriber_ptr = communication_ptr->CreatePoseDataSubscriber("/goal");
        web_goal_subscriber_ptr = communication_ptr->CreatePoseDataSubscriber("/web_goal_pose");
     
        // 设置回调函数    
        point_publisher->setWebGoalTargetCallback([](const std::shared_ptr<PoseStamped>& msg) {});
        // 初始化
        std::cout << "\n 初始化PointPublisher..." << std::endl;
        if (!point_publisher->init()) {
            std::cerr << " PointPublisher初始化失败" << std::endl;
            return -1;
        }
        
        // 显示状态
        //point_publisher.printStatus();
        std::thread data_receive_thread(DataReceiveThread);
        data_receive_thread.detach();
        // 发布目标点
   
  
        
        // 启动点发布器
        std::cout << "\n 启动点发布器..." << std::endl;
        point_publisher->start();
        communication_ptr->Run();
        
        std::cout << "\n 点发布节点启动完成" << std::endl;
        
        
    } catch (const std::exception& e) {
        std::cerr << " 程序执行出错: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
