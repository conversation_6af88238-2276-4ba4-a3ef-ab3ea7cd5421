#pragma once

#if COMMUNICATION_TYPE == ROS1

#include <ros/ros.h>
// #include <deque>
// #include <mutex>
#include <sensor_msgs/NavSatFix.h>

// #include "data_types/gnss_data.h"
#include "subscriber_base.h"

namespace communication::ros1{

class GnssDataSubscriberRos1 :  public GnssDataSubscriberBase{
    public:
        GnssDataSubscriberRos1(ros::NodeHandle &nh, const std::string &gnss_topic, size_t max_buffer_size = 10);

        ~GnssDataSubscriberRos1() = default;

        void GnssDataCallbackRos1(const sensor_msgs::NavSatFix::ConstPtr &gnss_msg);

    private:
        ros::NodeHandle& nh_;
        ros::Subscriber subscriber_;
    };

} // namespace communication::ros1{

#endif