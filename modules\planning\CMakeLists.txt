# perception/CMakeLists.txt
cmake_minimum_required(VERSION 3.10.2)
project(planning)

# 全局设置库文件输出目录
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)

#set(CMAKE_BUILD_TYPE "Release")
set(CMAKE_BUILD_TYPE "Debug")
set(CMAKE_CXX_STANDARD 14)
#set(CMAKE_CXX_FLAGS "-std=c++17 -march=native")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall")

# 添加 costmap_2d 和 voxel_grid 的编译
add_subdirectory(src/voxel_grid)
add_subdirectory(src/costmap_2d)

# 查找PCL库
find_package(PCL 1.8 REQUIRED)
include_directories(${PCL_INCLUDE_DIRS})
add_definitions(${PCL_DEFINITIONS})

# 查找yaml-cpp库
find_package(yaml-cpp REQUIRED)
find_package(catkin REQUIRED COMPONENTS
  roscpp
  nav_msgs
  std_msgs
  cv_bridge
  image_transport
)

find_package(OpenCV REQUIRED)
find_package(Boost REQUIRED COMPONENTS system thread)
find_package(Eigen3 REQUIRED)


include_directories(${YAML_CPP_INCLUDE_DIR})

include_directories(
    include
    include/rrt_star_global_planner
    include/point_publish
    include/global_traj_generate
    include/obstacle_stop
    include/local_planner
    include/path_follower
    include/map_server
    include/common
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/include/costmap_2d
    ${CMAKE_CURRENT_SOURCE_DIR}/include/voxel_grid
    ${CMAKE_CURRENT_SOURCE_DIR}/../communication/include 
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include
    ${Boost_INCLUDE_DIRS}
    ${EIGEN3_INCLUDE_DIRS}
    ${OpenCV_INCLUDE_DIRS}
)

add_library(${PROJECT_NAME}_core
  src/planning.cpp
  src/rrt_star_global_planner/rrt_star_global_planner.cpp
  src/point_publish/point_publish.cpp
  src/global_traj_generate/global_traj_generate.cpp
  src/obstacle_stop/obstacle_stop.cpp
  src/local_planner/local_planner.cpp
  src/path_follower/path_follower.cpp
)

target_link_libraries(${PROJECT_NAME}_core
    ${COMMON_LIB}
    ${COMMUNICATION_CORE}
    Threads::Threads
    costmap_2d
    voxel_grid
    ${PCL_LIBRARIES}
)

add_executable(${PROJECT_NAME}_node
  src/planning_node.cpp
)

# add_executable(rrt_star_example
#   src/rrt_star_global_planner/rrt_start_Example.cpp
# )

# target_link_libraries(rrt_star_example
#                     ${COMMON_LIB}
#                     ${COMMUNICATION_CORE}
#                     ${PROJECT_NAME}_core
#                     Threads::Threads
# )

# 添加 RRT* 全局规划器节点可执行文件
add_executable(rrt_star_global_planner_node
  src/rrt_star_global_planner/rrt_star_global_planner_node.cpp
)

add_executable(point_publish_node
  src/point_publish/point_publish_node.cpp
)

#add_executable(point_publish_Example
#  src/point_publish/point_publish_Example.cpp
#)

add_executable(global_traj_generate_node
  src/global_traj_generate/global_traj_generate_node.cpp
)

add_executable(obstacle_stop_node
  src/obstacle_stop/obstacle_stop_node.cpp
)

add_executable(obstacle_stop_Example
  src/obstacle_stop/obstacle_stop_Example.cpp
)

add_executable(local_planner_node
  src/local_planner/local_planner_node.cpp
)

# 添加路径跟随器可执行文件
add_executable(path_follower_node
  src/path_follower/path_follower_node.cpp
  src/path_follower/path_follower.cpp
)



# 添加 map_server 可执行文件
add_executable(map_server_node
  src/map_server/map_server.cpp
  src/map_server/image_loader.cpp
  src/map_server/map_loader.cpp
)

# 设置RPATH
set(CMAKE_SKIP_BUILD_RPATH FALSE)
set(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
set(CMAKE_INSTALL_RPATH "${CMAKE_CURRENT_SOURCE_DIR}/../common/lib:${CMAKE_CURRENT_SOURCE_DIR}/../communication/lib")

# 查找pthread库
find_package(Threads REQUIRED)

# 查找库文件
find_library(COMMON_LIB common_lib
    PATHS
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/lib
    NO_DEFAULT_PATH
)

find_library(COMMUNICATION_CORE communication_core
    PATHS
    ${CMAKE_CURRENT_SOURCE_DIR}/../communication/lib
    NO_DEFAULT_PATH
)

if(NOT COMMON_LIB)
    message(FATAL_ERROR "common_lib not found")
endif()

if(NOT COMMUNICATION_CORE)
    message(FATAL_ERROR "communication_core not found")
endif()

target_link_libraries(${PROJECT_NAME}_node 
                    ${COMMON_LIB}
                    ${COMMUNICATION_CORE}
                    ${PROJECT_NAME}_core   
                    Threads::Threads
                    costmap_2d  # 添加 costmap_2d 库
                    voxel_grid  # 添加 voxel_grid 库

)

# target_link_libraries(rrt_star_example
#                     ${COMMON_LIB}
#                     ${COMMUNICATION_CORE}
#                     ${PROJECT_NAME}_core
#                     Threads::Threads
# )

# 链接 RRT* 全局规划器节点库
target_link_libraries(rrt_star_global_planner_node
                    ${COMMON_LIB}
                    ${COMMUNICATION_CORE}
                    ${PROJECT_NAME}_core
                    Threads::Threads
                    costmap_2d
                    voxel_grid
                    yaml-cpp
)

target_link_libraries(point_publish_node
                    ${COMMON_LIB}
                    ${COMMUNICATION_CORE}
                    ${PROJECT_NAME}_core
                    Threads::Threads
)



target_link_libraries(global_traj_generate_node
                    ${COMMON_LIB}
                    ${COMMUNICATION_CORE}
                    ${PROJECT_NAME}_core
                    Threads::Threads
                    ${PCL_LIBRARIES}
)

target_link_libraries(obstacle_stop_node
                    ${COMMON_LIB}
                    ${COMMUNICATION_CORE}
                    ${PROJECT_NAME}_core
                    Threads::Threads
                    ${PCL_LIBRARIES}
)

target_link_libraries(obstacle_stop_Example
                    ${PROJECT_NAME}_core
                    Threads::Threads
                    ${PCL_LIBRARIES}
)

target_link_libraries(local_planner_node
                    ${COMMON_LIB}
                    ${COMMUNICATION_CORE}
                    ${PROJECT_NAME}_core
                    Threads::Threads
                    ${PCL_LIBRARIES}
                    yaml-cpp
)

target_link_libraries(map_server_node
                   ${COMMON_LIB}
                   ${COMMUNICATION_CORE}
                   ${PROJECT_NAME}_core
                   Threads::Threads
                   ${OpenCV_LIBRARIES}


)


target_link_libraries(path_follower_node
  ${COMMON_LIB}
  ${COMMUNICATION_CORE}
  ${PROJECT_NAME}_core
  ${catkin_LIBRARIES}
  ${PCL_LIBRARIES}
  yaml-cpp
)


target_include_directories(path_follower_node PUBLIC
  ${catkin_INCLUDE_DIRS}
  ${PCL_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 设置 RRT* 全局规划器节点的头文件路径
target_include_directories(rrt_star_global_planner_node PUBLIC
  ${catkin_INCLUDE_DIRS}
  ${PCL_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${CMAKE_CURRENT_SOURCE_DIR}/include/rrt_star_global_planner
)

install(TARGETS path_follower_node
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

# 安装 RRT* 全局规划器节点
install(TARGETS rrt_star_global_planner_node
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

target_link_libraries(${PROJECT_NAME}_core
                    ${PCL_LIBRARIES}
                    yaml-cpp
)

target_include_directories(map_server_node PUBLIC
  ${OpenCV_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 添加测试可执行文件
add_executable(test_common_types
  include/common/test_common_types.cpp
)

target_link_libraries(test_common_types
    Threads::Threads
)


