/**
 * 
 */

#pragma once

#include "data_types/decision_output.h"
#include "data_types/environment_context.h"
#include "data_types/params.h"
#include "state_machine.h"
#include "decision_comm.h"

namespace decision{

class Decision{
public:
    Decision();
    ~Decision();

    bool Initialize(const std::string &config_file_path);
    void Run();
    void ChangeState(std::shared_ptr<State> new_state);
    
    // 环境分析方法
    void AnalyzeStaticObstacles();
    void AnalyzeDynamicObstacles();
    void AnalyzePathFeasibility();
    void AnalyzeTerrainFeatures();
    
    // 获取方法
    const Params& GetParams() const { return params_; }
    const EnvironmentContext& GetContext() const { return context_; }
    
private:
    bool ReadParams(const std::string &config_file_path);

    // ROS回调函数
    // void obstaclesCallback(const sensor_msgs::PointCloud2ConstPtr& msg);
    // void globalPathCallback(const nav_msgs::PathConstPtr& msg);
    // void trajectoriesCallback(const geometry_msgs::PoseArrayConstPtr& msg);
    // void localizationCallback(const nav_msgs::OdometryConstPtr& msg);
    // void imuCallback(const sensor_msgs::ImuConstPtr& msg);
    bool GetObstaclesData();
    bool SubscribGlobalPathData();
    bool GetTrajectoriesData();
    bool SubscribLocalizationData();
    bool SubscribImuData();

    // 点云处理
    void PreprocessPointCloud(pcl::PointCloud<pcl::PointXYZI>::Ptr& cloud);
    Eigen::Vector2f FitWallDirection(const std::vector<pcl::PointXYZI>& points);
    
    // 环境检测
    void DetectCorridorEnvironment();
    void DetectSlope();
    void DetectStairs();
    void DetectRoughTerrain();
    
    // 动态参数回调
    // void reconfigureCallback(dog_decision::DecisionConfig &config, uint32_t level);

    std::shared_ptr<DecisionComm> decision_comm_ptr;

    // 状态机
    std::shared_ptr<State> current_state_;
    
    // 环境上下文
    EnvironmentContext context_;
    
    // 参数
    Params params_;
    
    // 动态参数服务器
    // dynamic_reconfigure::Server<dog_decision::DecisionConfig> dyn_reconf_server_;
};


};// namespace decision{

