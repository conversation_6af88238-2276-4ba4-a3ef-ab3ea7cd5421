#include "rrt_star_global_planner.h"
#include <iostream>
#include <iomanip>

using namespace RRTstar_planner;

/**
 * @brief 创建简单的测试地图
 */
Costmap2D createTestMap() {
    // 创建100x100的测试地图，分辨率0.1m/pixel
    Costmap2D map(100, 100, 0.1, -5.0, -5.0);
    
    // 添加外围边界
    for (int x = 0; x < 100; ++x) {
        map.setCost(x, 0, 255);
        map.setCost(x, 99, 255);
    }
    for (int y = 0; y < 100; ++y) {
        map.setCost(0, y, 255);
        map.setCost(99, y, 255);
    }
    
    // 添加中央障碍物
    for (int x = 40; x < 60; ++x) {
        for (int y = 40; y < 60; ++y) {
            map.setCost(x, y, 255);
        }
    }
    
    // 添加L型障碍物
    for (int x = 20; x < 40; ++x) {
        for (int y = 20; y < 25; ++y) {
            map.setCost(x, y, 255);
        }
    }
    for (int x = 20; x < 25; ++x) {
        for (int y = 20; y < 40; ++y) {
            map.setCost(x, y, 255);
        }
    }
    
    // 添加一些随机障碍物
    for (int x = 70; x < 90; ++x) {
        for (int y = 60; y < 80; ++y) {
            if ((x + y) % 5 == 0) {
                map.setCost(x, y, 255);
            }
        }
    }
    
    return map;
}

/**
 * @brief 可视化地图到控制台
 */
void visualizeMap(const Costmap2D& map, const std::vector<PoseStamped>& path = {}) {
    std::cout << "\n地图可视化 (█=障碍物, ·=路径, 空格=自由空间):" << std::endl;
    
    // 创建路径标记
    std::vector<std::vector<bool>> path_marks(map.height, std::vector<bool>(map.width, false));
    for (const auto& pose : path) {
        int mx, my;
        if (map.worldToMap(pose.pose.position.x, pose.pose.position.y, mx, my)) {
            path_marks[my][mx] = true;
        }
    }
    
    // 只显示地图的一部分以适应控制台
    int display_width = std::min(80, map.width);
    int display_height = std::min(30, map.height);
    
    for (int y = display_height - 1; y >= 0; --y) {
        for (int x = 0; x < display_width; ++x) {
            if (map.getCost(x, y) > 50) {
                std::cout << "█";  // 障碍物
            } else if (path_marks[y][x]) {
                std::cout << "·";  // 路径
            } else {
                std::cout << " ";  // 自由空间
            }
        }
        std::cout << std::endl;
    }
}

/**
 * @brief 计算路径统计信息
 */
void printPathStatistics(const std::vector<PoseStamped>& path) {
    if (path.empty()) {
        std::cout << "路径为空" << std::endl;
        return;
    }
    
    double total_length = 0.0;
    double max_segment = 0.0;
    double min_segment = std::numeric_limits<double>::max();
    
    for (size_t i = 1; i < path.size(); ++i) {
        double dx = path[i].pose.position.x - path[i-1].pose.position.x;
        double dy = path[i].pose.position.y - path[i-1].pose.position.y;
        double segment_length = sqrt(dx * dx + dy * dy);
        
        total_length += segment_length;
        max_segment = std::max(max_segment, segment_length);
        min_segment = std::min(min_segment, segment_length);
    }
    
    std::cout << "📊 路径统计信息:" << std::endl;
    std::cout << "  路径点数: " << path.size() << std::endl;
    std::cout << "  总长度: " << std::fixed << std::setprecision(3) << total_length << " m" << std::endl;
    std::cout << "  平均段长: " << total_length / (path.size() - 1) << " m" << std::endl;
    std::cout << "  最大段长: " << max_segment << " m" << std::endl;
    std::cout << "  最小段长: " << min_segment << " m" << std::endl;
    
    // 计算直线距离和路径效率
    if (path.size() >= 2) {
        double dx = path.back().pose.position.x - path[0].pose.position.x;
        double dy = path.back().pose.position.y - path[0].pose.position.y;
        double straight_distance = sqrt(dx * dx + dy * dy);
        double efficiency = (straight_distance / total_length) * 100.0;
        
        std::cout << "  直线距离: " << straight_distance << " m" << std::endl;
        std::cout << "  路径效率: " << efficiency << "%" << std::endl;
    }
}

/**
 * @brief 主程序
 */
int main(int argc, char** argv) {
    std::cout << "=== RRTstarGlobalPlannerNode ===" << std::endl;
    
    try {
        // 创建规划器
        RRTstarPlanner planner;
        
        // 创建测试地图
        std::cout << "\n🗺️ 创建测试地图..." << std::endl;
        Costmap2D test_map = createTestMap();
        
        // 初始化规划器
        std::cout << "\n🔧 初始化规划器..." << std::endl;
        planner.initialize("rrt_star_planner", &test_map);
        
        // 设置参数
        planner.setMaxNodesNum(10000);
        planner.setPlanTimeout(5.0);
        planner.setSearchRadius(1.0);
        planner.setGoalRadius(0.3);
        planner.setPathPointSpacing(0.2);
        
        // 设置回调函数
        planner.setPathPublishCallback([](const std::vector<PoseStamped>& path) {
            std::cout << "📍 接收到新路径，包含 " << path.size() << " 个点" << std::endl;
        });
        
        planner.setMarkerPublishCallback([](const Marker& marker) {
            std::cout << "🎨 接收到可视化标记，ID: " << marker.id 
                      << ", 点数: " << marker.points.size() << std::endl;
        });
        
        planner.setAccessablePublishCallback([](bool accessible) {
            if (accessible) {
                std::cout << "✅ 目标可达" << std::endl;
            } else {
                std::cout << "❌ 目标不可达" << std::endl;
            }
        });
        
        // 显示初始地图
        visualizeMap(test_map);
        
        // 定义测试场景
        std::vector<std::pair<PoseStamped, PoseStamped>> test_scenarios = {
            // 简单路径
            {PoseStamped(-4.0, -4.0, 0.0), PoseStamped(4.0, 4.0, M_PI/4)},
            
            // 绕过中央障碍物
            {PoseStamped(-3.0, 0.0, 0.0), PoseStamped(3.0, 0.0, M_PI)},
            
            // 穿越L型障碍物区域
            {PoseStamped(-2.0, -1.0, 0.0), PoseStamped(-1.0, 1.5, M_PI/2)},
            
            // 复杂路径
            {PoseStamped(-4.0, 3.0, 0.0), PoseStamped(3.0, -3.0, -M_PI/2)},
            
            // 长距离路径
            {PoseStamped(-4.5, -4.5, 0.0), PoseStamped(4.5, 4.5, M_PI/4)}
        };
        
        // 执行测试场景
        std::cout << "\n🎯 开始执行测试场景..." << std::endl;
        
        for (size_t i = 0; i < test_scenarios.size(); ++i) {
            std::cout << "\n--- 场景 " << (i + 1) << " ---" << std::endl;
            
            const auto& start = test_scenarios[i].first;
            const auto& goal = test_scenarios[i].second;
            
            std::cout << "起点: (" << start.pose.position.x << ", " << start.pose.position.y << ")" << std::endl;
            std::cout << "目标: (" << goal.pose.position.x << ", " << goal.pose.position.y << ")" << std::endl;
            
            // 计算直线距离
            double dx = goal.pose.position.x - start.pose.position.x;
            double dy = goal.pose.position.y - start.pose.position.y;
            double straight_distance = sqrt(dx * dx + dy * dy);
            std::cout << "直线距离: " << straight_distance << " m" << std::endl;
            
            // 执行路径规划
            std::vector<PoseStamped> plan;
            auto start_time = std::chrono::high_resolution_clock::now();
            
            bool success = planner.makePlan(start, goal, plan);
            
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            
            std::cout << "规划耗时: " << duration.count() << " ms" << std::endl;
            
            if (success) {
                std::cout << "✅ 规划成功" << std::endl;
                printPathStatistics(plan);
                
                // 显示带路径的地图
                std::cout << "\n带路径的地图:" << std::endl;
                visualizeMap(test_map, plan);
            } else {
                std::cout << "❌ 规划失败" << std::endl;
            }
            
            std::cout << std::endl;
        }
        
        // 参数调优测试
        std::cout << "\n🔧 参数调优测试..." << std::endl;
        
        struct ParameterSet {
            size_t max_nodes;
            double search_radius;
            double goal_radius;
            double timeout;
            std::string name;
        };
        
        std::vector<ParameterSet> parameter_sets = {
            {5000, 0.8, 0.2, 3.0, "快速参数"},
            {10000, 1.0, 0.3, 5.0, "标准参数"},
            {20000, 1.5, 0.4, 8.0, "精确参数"}
        };
        
        for (const auto& params : parameter_sets) {
            std::cout << "\n测试 " << params.name << ":" << std::endl;
            std::cout << "  最大节点: " << params.max_nodes << std::endl;
            std::cout << "  搜索半径: " << params.search_radius << " m" << std::endl;
            std::cout << "  目标半径: " << params.goal_radius << " m" << std::endl;
            std::cout << "  超时时间: " << params.timeout << " s" << std::endl;
            
            planner.setMaxNodesNum(params.max_nodes);
            planner.setSearchRadius(params.search_radius);
            planner.setGoalRadius(params.goal_radius);
            planner.setPlanTimeout(params.timeout);
            
            // 测试一个中等难度的场景
            std::vector<PoseStamped> test_plan;
            auto test_start_time = std::chrono::high_resolution_clock::now();
            
            bool test_success = planner.makePlan(test_scenarios[1].first, test_scenarios[1].second, test_plan);
            
            auto test_end_time = std::chrono::high_resolution_clock::now();
            auto test_duration = std::chrono::duration_cast<std::chrono::milliseconds>(test_end_time - test_start_time);
            
            if (test_success) {
                double test_length = 0.0;
                for (size_t j = 1; j < test_plan.size(); ++j) {
                    double dx = test_plan[j].pose.position.x - test_plan[j-1].pose.position.x;
                    double dy = test_plan[j].pose.position.y - test_plan[j-1].pose.position.y;
                    test_length += sqrt(dx * dx + dy * dy);
                }
                std::cout << "  结果: 成功，路径长度 " << test_length << " m，耗时 " 
                          << test_duration.count() << " ms" << std::endl;
            } else {
                std::cout << "  结果: 失败，耗时 " << test_duration.count() << " ms" << std::endl;
            }
        }
        
        std::cout << "\n✅ 演示完成" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序执行出错: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
