#include "cx_rwlock.h"
namespace common_lib {
CXRWLock::CXRWLock() {
  Initialize();
}

CXRWLock::~CXRWLock() {
  Destroy();
}

cx_int CXRWLock::Initialize() {
#ifdef WIN32
  InitializeSRWLock(&m_srwLock);
#else
  pthread_rwlock_init(&rwlock_, NULL);
#endif

  return 0;
}

cx_int CXRWLock::Destroy() {
#ifdef WIN32

#else
  pthread_rwlock_destroy(&rwlock_);
#endif

  return 0;
}

cx_int CXRWLock::LockR() {
#ifdef WIN32
  AcquireSRWLockShared(&m_srwLock);
#else
  pthread_rwlock_rdlock(&rwlock_);
#endif

  return 0;
}

cx_int CXRWLock::UnlockR() {
#ifdef WIN32
  ReleaseSRWLockShared(&m_srwLock);
#else
  pthread_rwlock_unlock(&rwlock_);
#endif

  return 0;
}

cx_int CXRWLock::LockW() {
#ifdef WIN32
  AcquireSRWLockExclusive(&m_srwLock);
#else
  pthread_rwlock_wrlock(&rwlock_);
#endif

  return 0;
}

cx_int CXRWLock::UnlockW() {
#ifdef WIN32
  ReleaseSRWLockExclusive(&m_srwLock);
#else
  pthread_rwlock_unlock(&rwlock_);
#endif

  return 0;
}
}  // namespace common_lib