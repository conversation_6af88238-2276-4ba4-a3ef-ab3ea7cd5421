#include <unistd.h> 
#include "common.h"
#include "yaml_config.h"
#include "cx_path.h"
using namespace common_lib;

void InitConfig(string &config_path)
{
    // Load config parameters.
    cx_char szBuf[MAX_PATH] = { 0 };
    readlink("/proc/self/exe", szBuf, MAX_PATH);

    std::string strExePath = szBuf;
    if(config_path.empty())
    {
        strExePath = strExePath.substr(0, strExePath.rfind(PATH_SEPARATOR));
        fprintf(stderr, ">>>> LOC debug >>>> strDataPath is empty, SetExePath: %s  \n", strExePath.c_str());
        SetExePath(strExePath.c_str());
    }
    cx_string strConfigFile;
    GetExePath(strConfigFile);
    strConfigFile += "/../config/localization_config.yaml";
    fprintf(stderr, ">>>> LOC debug >>>> SetConfigPath: %s  \n", strConfigFile.c_str());

    // 用相对路径加载配置
    common_lib::YamlConfig::GetInstance().LoadFile(strConfigFile);
    std::cout << "Config loaded from: " << strConfigFile << std::endl;
}
