#include "subscriber/imu_data_subscriber_ros1.h"

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1{

ImuDataSubscriberRos1::ImuDataSubscriberRos1(ros::NodeHandle &nh, const std::string &imu_topic, size_t max_buffer_size)
            : ImuDataSubscriberBase(imu_topic, max_buffer_size), nh_(nh) 
{
            subscriber_ = nh_.subscribe(imu_topic, max_buffer_size, &ImuDataSubscriberRos1::IMUDataCallbackRos1, this);
}

//消息回调函数
void ImuDataSubscriberRos1::IMUDataCallbackRos1(const sensor_msgs::ImuConstPtr &imu) 
{
  //消息转换
  IMUData imu_data;
  imu_data.time = imu->header.stamp.toSec();
  imu_data.linear_acceleration[0] = imu->linear_acceleration.x;
  imu_data.linear_acceleration[1] = imu->linear_acceleration.y;
  imu_data.linear_acceleration[2] = imu->linear_acceleration.z;
  imu_data.angular_velocity[0] = imu->angular_velocity.x;
  imu_data.angular_velocity[1] = imu->angular_velocity.y;
  imu_data.angular_velocity[2] = imu->angular_velocity.z;
  imu_data.orientation[0] = imu->orientation.x;
  imu_data.orientation[1] = imu->orientation.y;
  imu_data.orientation[2] = imu->orientation.z;
  imu_data.orientation[3] = imu->orientation.w;
  

  std::lock_guard<std::mutex> lock(buffer_mutex_);
  // 将转换后的IMU数据存入缓冲区
  data_buffer_.push_back(imu_data);
  // Check if the buffer exceeds the maximum size 
  if (data_buffer_.size() > max_buffer_size_) {
    data_buffer_.pop_front(); // Remove the oldest data if buffer is full
  }

}

}   // namespace communication::ros1{

#endif
    