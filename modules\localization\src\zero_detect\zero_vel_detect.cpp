/*
 * 零速检测算法实现
 */
#include <math.h>
#include <iostream>
#include "zero_vel_detect.h"

const float kZeroConfidenceThreshold = 0.8; //零速检测置信度阈值
const float kEarthGravity = 9.8;   //重力加速度

const int CIRCULAR_QUEUE_MAX_SIZE = 50;

CZeroVelDetect::CZeroVelDetect() 
{
	zero_vel_ = false;
	dq_gyro_.clear();
	dq_acc_.clear();

}

CZeroVelDetect::~CZeroVelDetect()
{

}

void CZeroVelDetect::Init(double gyro_std_threshold, double acc_std_threshold)
{
	dq_gyro_.clear();
	dq_acc_.clear();

	gyro_std_threshold_ = gyro_std_threshold;

	acc_std_threshold_ = acc_std_threshold;

	std::cout << "acc_std_threshold_:" << acc_std_threshold_ << "," << "gyro_std_threshold_:" << gyro_std_threshold_<<std::endl;
}

double CZeroVelDetect::CalVar(std::deque<double>& dq)
{
	if(dq.empty())
	{
		return 0.0;
	}
	double sum = 0.0;
    double sumSquared = 0.0;

    for (const auto& db : dq) 
    {
        sum += db;
        sumSquared += pow(db, 2);
    }

    double mean = sum / dq.size();
    double variance = sumSquared / dq.size() - pow(mean, 2);
    return variance;
}

char CZeroVelDetect::static_detect(const ImuData &imu_data)
{
	const int static_times_threshold = 30;
	const int move_times_threshold = 10;
	static int s_static_count = 0;
	static int s_move_count = 0;

	double accXYZ = sqrt(imu_data.acc[0] * imu_data.acc[0] + imu_data.acc[1] * imu_data.acc[1] + imu_data.acc[2] * imu_data.acc[2]);
	accXYZ -= kEarthGravity;
	double gyroXYZ = sqrt(imu_data.gyro[0] * imu_data.gyro[0] + imu_data.gyro[1] * imu_data.gyro[1]+ imu_data.gyro[2] * imu_data.gyro[2]);

	dq_gyro_.push_back(gyroXYZ);
	dq_acc_.push_back(accXYZ);

	if(dq_gyro_.size() > CIRCULAR_QUEUE_MAX_SIZE)
	{
		dq_gyro_.pop_front();
	}

	if(dq_acc_.size() > CIRCULAR_QUEUE_MAX_SIZE)
	{
		dq_acc_.pop_front();
	}

	if (fabs(imu_data.gyro[2]) > D2R(10.0))
	{
		s_static_count = 0;
		return 0;
	}
	
	if (dq_acc_.size() >= CIRCULAR_QUEUE_MAX_SIZE)
	{
		double accXYZ_var = CalVar(dq_acc_);
		double gyroXYZ_var = CalVar(dq_gyro_);
		// std::cout << "accXYZ_var:" << accXYZ_var << "," << "gyroXYZ_var:" << gyroXYZ_var<< "," << accXYZ <<std::endl;
		if(accXYZ_var < acc_std_threshold_ && gyroXYZ_var < gyro_std_threshold_)
		{
			s_move_count = 0;
			s_static_count++;
			if (s_static_count > static_times_threshold)
			{
				return 1;  //静止
			}
		}
		else
		{
			s_static_count = 0;
			s_move_count++;
			if (s_move_count > move_times_threshold)
			{
				return 0;//运动
			}
		}

	}

	return -1;
}

// 判断是否停止, 返回置信度,1为最高
bool CZeroVelDetect::stop_detect(const ImuData& imu_data)
{
	//通过imu判断
	char stop_by_imu = static_detect(imu_data);

	zero_vel_ = (1 == stop_by_imu);

	return zero_vel_;
}
