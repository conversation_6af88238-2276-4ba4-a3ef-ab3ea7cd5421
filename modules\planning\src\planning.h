#pragma once

#include <string>
#include "cx_types.h"
#include "common_config.h"
namespace planning
{

    class Planning
    {
    public:
        Planning(const std::string &config_file_path);
        ~Planning();

    private:
        void GetCommonConfig();

        void GetGlobalTrajGenerateConfig();
        void GetLocalPlannerConfig();
        void GetObstacleStopConfig();
        void GetPointPublishConfig();

        // global_traj_generate
        cx_int GetIndexIncrement(const cx_string &h1, const cx_string &h2);
        cx_int GetIndexNum(const cx_string &h1, const cx_string &h2);

        // obstacle_stop
        cx_double GetObstacleHeightThre(const cx_string &h1, const cx_string &h2);
        cx_double GetVehicleLength(const cx_string &h1, const cx_string &h2);
        cx_double GetVehicleWidth(const cx_string &h1, const cx_string &h2);
        cx_int GetObsnumThre(const cx_string &h1, const cx_string &h2);
        cx_double GetAdjacentRange(const cx_string &h1, const cx_string &h2);
        cx_double GetReplanTime(const cx_string &h1, const cx_string &h2);
        cx_double GetSensorOffsetX(const cx_string &h1, const cx_string &h2);
        cx_double GetSensorOffsetY(const cx_string &h1, const cx_string &h2);

        // point_publish
        cx_int GetPointId(const cx_string &h1, const cx_string &h2);
        cx_int GetPointInfo(const cx_string &h1, const cx_string &h2);
        cx_int GetGait(const cx_string &h1, const cx_string &h2);
        cx_int GetSpeed(const cx_string &h1, const cx_string &h2);
        cx_int GetManner(const cx_string &h1, const cx_string &h2);
        cx_int GetObsmode(const cx_string &h1, const cx_string &h2);
        cx_int GetNavmode(const cx_string &h1, const cx_string &h2);
    private:
        // common
        common_lib::CommonConfigInfo common_config_info_;

        // global_traj_generate
        cx_int index_increment_; // 向前预移动的航迹点索引增量 (原有默认值)
        cx_int index_num_;       // 向前预移动的点增量 (原有默认值)

        // obstacle_stop
        cx_double obstacle_height_thre_; // 障碍物高度阈值 (原有默认值)
        cx_double vehicle_length_;       // 载体长度 (原有默认值)
        cx_double vehicle_width_;        // 载体宽度 (原有默认值)
        cx_int obsnum_thre_;             // 障碍物数量阈值 (原有默认值)
        cx_double adjacent_range_;       // 邻近范围 (原有默认值)
        cx_double replan_time_;          // 重规划时间 (原有默认值)
        cx_double sensor_offset_x_;      // 传感器X偏移
        cx_double sensor_offset_y_;      // 传感器Y偏移

        // point_publish
        cx_int point_id_;   // 点位ID，用于标识不同的导航点
        cx_int point_info_; // 点位信息，附加的点位描述信息
        cx_int gait_;       // 步态模式，控制机器人的行走方式
        cx_int speed_;      // 速度等级，控制机器人的移动速度
        cx_int manner_;     // 行为方式，控制机器人的行为模式
        cx_int obsmode_;    // 避障模式，控制避障行为
        cx_int navmode_;    // 导航模式，控制导航策略
    };

} // namespace planning{
