/**
 * @file tf_data_publisher_ros1.h
 * @brief This file contains the implementation of a ROS1 publisher for transform data.
 * * It inherits from the TFDataPublisherBase class and provides methods to publish transform data in ROS1 format.
 * * The publisher uses the geometry_msgs::TransformStamped message type to represent transform data.
 * * * The class provides methods to convert transform data to the appropriate ROS message format and publish it on a specified topic.
 * * It also includes methods to get the number of subscribers to the topic.
 */
#pragma once
#include <ros/ros.h>
// #include <geometry_msgs/PoseWithCovarianceStamped.h>
// #include <geometry_msgs/TransformStamped.h>
#include <tf/transform_datatypes.h>
#include <tf/transform_broadcaster.h>

#include "publisher_base.h"
#include "data_types/pose_data.h"

namespace communication::ros1
{
    class TFDataPublisherRos1 : public TFDataPublisherBase
    {
    public:
        TFDataPublisherRos1(ros::NodeHandle &nh, const std::string &frame_id, const std::string &child_frame_id);

        ~TFDataPublisherRos1() = default;

    protected:
        virtual void PublishMsg() override
        {
            // br_.sendTransform(tf::StampedTransform(transform_, ros::Time().fromSec(data_.time), frame_id_, child_frame_id_));
            br_.sendTransform(dynamic_transform_);
        }

        virtual void ToMsg() override
        {
            // tf::Quaternion q;
            // transform_.setOrigin(tf::Vector3(data_.position[0],
            //                                   data_.position[1],
            //                                   data_.position[2]));
            // q.setX(data_.orientation[0]);
            // q.setY(data_.orientation[1]);
            // q.setZ(data_.orientation[2]);
            // q.setW(data_.orientation[3]);
            // transform_.setRotation(q);
            dynamic_transform_.header.stamp = ros::Time().fromSec(data_.time);
            dynamic_transform_.header.frame_id = frame_id_;
            dynamic_transform_.child_frame_id = child_frame_id_;
            dynamic_transform_.transform.translation.x = data_.position[0];
            dynamic_transform_.transform.translation.y = data_.position[1];
            dynamic_transform_.transform.translation.z = data_.position[2];
            dynamic_transform_.transform.rotation.x = data_.orientation[0];
            dynamic_transform_.transform.rotation.y = data_.orientation[1];
            dynamic_transform_.transform.rotation.z = data_.orientation[2];
            dynamic_transform_.transform.rotation.w = data_.orientation[3];
        }

        virtual int GetSubscriberCount() override
        {
            return 1; // publisher_.getNumSubscribers();
        }

    private:
        ros::NodeHandle &nh_;
        // ros::Publisher publisher_;
        tf::TransformBroadcaster br_;
        // tf::Transform transform_;
        geometry_msgs::TransformStamped dynamic_transform_;

        // geometry_msgs::TransformStamped msg_; // ROS message type for transform data
        std::string frame_id_;       // Frame ID for the transform data
        std::string child_frame_id_; // Child Frame ID for the transform data
    };
} // namespace communication::ros1
