<?xml version="1.0"?>
<launch>
  <!-- 加载 全局参数 -->
  <!-- <rosparam command="load" file="$(find decision)/config/decision_config.yaml" /> -->

  <arg name="show_rviz" default="true" />
  <!-- 启动可视化 rivi 文件 -->
  <node if="$(arg show_rviz)" name="rviz_decision" pkg="rviz" type="rviz" args="-d $(find decision)/rviz/decision.rviz"/>

  <!-- 启动节点 decision_node -->
  <node pkg="decision" type="decision_node"  name="decision" 
       args= "/mnt/e/project/24-DC200/git/cx-fusion-core-dc200/modules/decision/config/decision_config.yaml" output="screen" /> 
  <!-- respawn="false",  -->


</launch>