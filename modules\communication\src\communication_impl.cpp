#include "communication_impl.h"

namespace communication {

CommunicationImpl::CommunicationImpl(const std::string& module_name) : module_name_(module_name) {
   
}

CommunicationImpl::~CommunicationImpl() {
    // Destructor implementation
    // Clean up resources or perform any necessary shutdown procedures for communication
}

bool CommunicationImpl::Initialize(const CommunicationType type) {
    
    // Initialize the communication implementation based on the specified type
    type_ = type; // Store the communication type

    return true;
}


} // namespace communication

