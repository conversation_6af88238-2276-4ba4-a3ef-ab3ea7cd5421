#include "subscriber/lidar_data_subscriber_ros1.h"
#include <pcl_conversions/pcl_conversions.h>

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1{

LidarDataSubscriberRos1::LidarDataSubscriberRos1(ros::NodeHandle &nh, const std::string &lidar_topic, size_t max_buffer_size)
            : LidarDataSubscriberBase(lidar_topic, max_buffer_size), nh_(nh) 
{
            subscriber_ = nh_.subscribe(lidar_topic, max_buffer_size, &LidarDataSubscriberRos1::LidarDataCallbackRos1, this);
}

//lidar消息回调函数
void LidarDataSubscriberRos1::LidarDataCallbackRos1(const sensor_msgs::PointCloud2::ConstPtr &lidar_msg) 
{
 //将lidar消息转换为CloudData类型 
  static double s_last_time = 0.0; // 用于记录上一次处理的时间
  CloudXYZRIData cloud_data;
  cloud_data.time = lidar_msg->header.stamp.toSec();
  if(s_last_time > 0.0 && (cloud_data.time - s_last_time) > 0.15) {
    ROS_WARN("lidar time interval is abnormal, dt = %.3f.", cloud_data.time - s_last_time);
  }
  s_last_time = cloud_data.time;
  // 使用PCL库将sensor_msgs::PointCloud2转换为pcl::PointCloud<pcl::PointXYZI>
  pcl::fromROSMsg(*lidar_msg, *cloud_data.cloud_ptr);
  // 检查点云数据是否为空
  if (cloud_data.cloud_ptr->empty()) {
    ROS_WARN("Received empty point cloud data.");
    return;
  }
  // 检查点云数据是否包含有效的点
  if (cloud_data.cloud_ptr->points.empty()) {
    ROS_WARN("Received point cloud data with no points.");
    return;
  }
  // 检查点云数据的时间戳是否有效
  if (cloud_data.time <= 0.0) {
    ROS_WARN("Received point cloud data with invalid timestamp: %f", cloud_data.time);
    return;
  }

  // 将转换后的CloudData存入缓冲区
  std::lock_guard<std::mutex> lock(buffer_mutex_);
  data_buffer_.push_back(cloud_data);
  // 检查缓冲区是否超过最大大小 
  if (data_buffer_.size() > max_buffer_size_) {
    data_buffer_.pop_front(); // 如果缓冲区已满，删除最旧的数据
  }
}

}   // namespace communication::ros1{

#endif
    