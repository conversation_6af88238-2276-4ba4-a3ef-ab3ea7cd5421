#pragma once
#include <ros/ros.h>
#include <nav_msgs/OccupancyGrid.h>
#include "publisher_base.h"
#include "data_types/map_data.h"

namespace communication::ros1 {

class MapDataPublisherRos1 : public OccupancyGridPublisherBase {
public:
    MapDataPublisherRos1(ros::NodeHandle& nh, const std::string& topic, size_t max_buffer_size = 10);
    ~MapDataPublisherRos1() override = default;
protected:
    void PublishMsg() override {
        publisher_.publish(msg_);
    }
    void ToMsg() override {
        // Convert communication::OccupancyGrid to nav_msgs::OccupancyGrid
        msg_.header.frame_id = data_.header.frame_id;
        msg_.header.stamp = ros::Time(data_.header.stamp);
        msg_.info.resolution = data_.info.resolution;
        msg_.info.width = data_.info.width;
        msg_.info.height = data_.info.height;
        msg_.info.origin.position.x = data_.info.origin_position.x;
        msg_.info.origin.position.y = data_.info.origin_position.y;
        msg_.info.origin.position.z = data_.info.origin_position.z;
        msg_.info.origin.orientation.x = data_.info.origin_orientation.x;
        msg_.info.origin.orientation.y = data_.info.origin_orientation.y;
        msg_.info.origin.orientation.z = data_.info.origin_orientation.z;
        msg_.info.origin.orientation.w = data_.info.origin_orientation.w;
        msg_.data = data_.data;
    }
    int GetSubscriberCount() override {
        return publisher_.getNumSubscribers();
    }
private:
    ros::NodeHandle& nh_;
    ros::Publisher publisher_;
    nav_msgs::OccupancyGrid msg_;
};

} // namespace communication::ros1 