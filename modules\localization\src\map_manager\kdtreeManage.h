#pragma once

#include "DataType.h"
#include "cx_work.h"
#include "cx_singleton.h"
#include "cx_thread.h"
#include "yaml_config.h"
#include <pcl/kdtree/kdtree_flann.h>
#include <mutex>
#include <deque>
#include "use-ikfom.h"
#include "map_management.h"

// class Config;
// class CMThread;
// class CMWork;
// class CMSingleton;
// class CMMutex;
using namespace common_lib;
class KdtreeManage : public CXWork
{
public:
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
    
    KdtreeManage();
    virtual ~KdtreeManage();

    void Start();

public:
    void Initialize();
    
    // KdTree specific methods
    void InitMapManage(double dFilterSizeMapMin, string sMapPath);
    void SetCurPoint(state_ikfom &CurPos);
     void UpdateKdTree();
    // void getTreeCloud(PointCloudXYZI::Ptr &featsFromMap);
    pcl::KdTreeFLANN<PointType>::Ptr getKdTree();

private:
#ifdef WIN32
    static void MainLoop(PTP_CALLBACK_INSTANCE Instance, PVOID pContext);
#else
    static void* MainLoop(void *pContext);
#endif

private:
    cx_int Run();
   

private:
    // KdTree data
    pcl::KdTreeFLANN<PointType>::Ptr m_kdtree1;
    pcl::KdTreeFLANN<PointType>::Ptr m_kdtree2;
    pcl::KdTreeFLANN<PointType>::Ptr m_kdtree;
    pcl::KdTreeFLANN<PointType>::Ptr m_kdtree_last;
    state_ikfom m_state_point;
    bool m_bPtUpdate{false};

    // pcl::PointCloud<PointType>::Ptr m_cloud_tree1;
    // pcl::PointCloud<PointType>::Ptr m_cloud_tree2;
    int m_istatus_tree1{0}; // 0表示空闲，1表示正在更新点云，2表示正在用该树查找；
    int m_istatus_tree2{0};
    int m_ilast_ikdtree{1}; // 最新更新的ikdtree
    bool m_bflag_ikdtree_initial{false};
    // Thread related
    CXThread* m_pThread;
    CXMutex m_mtxTree;
    
    // Configuration reference
    common_lib::YamlConfig& m_Config;

    map_management m_mapManagement;
};

KdtreeManage* GetSingleton4KdtreeManage();
