<?xml version="1.0"?>
<package format="2">
  <name>perception</name>
  <version>0.0.0</version>
  <description>The perception package</description>

  <!-- One maintainer tag required, multiple allowed, one person per tag -->
  <!-- Example:  -->
  <!-- <maintainer email="<EMAIL>"><PERSON></maintainer> -->
  <maintainer email="<EMAIL>">ubuntu</maintainer>


  <!-- One license tag required, multiple allowed, one license per tag -->
  <!-- Commonly used license strings: -->
  <!--   BSD, MIT, Boost Software License, GPLv2, GPLv3, LGPLv2.1, LGPLv3 -->
  <license>TODO</license>

  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>std_msgs</build_depend>
  
  <!-- <build_depend>common_msgs</build_depend> -->
  
  <build_export_depend>roscpp</build_export_depend>
  <build_export_depend>std_msgs</build_export_depend>
  

  <exec_depend>roscpp</exec_depend>
  <exec_depend>std_msgs</exec_depend> 

  <!-- <exec_depend>common_msgs</exec_depend> -->
</package>