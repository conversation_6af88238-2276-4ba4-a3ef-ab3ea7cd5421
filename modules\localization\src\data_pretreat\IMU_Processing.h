#pragma once

#include <cmath>
#include <deque>
#include <mutex>
#include <Eigen/Eigen>
#include <pcl/common/io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/common/transforms.h>
#include "DataType.h"
#include "use-ikfom.h"
#include "esekfom.h"

#define MAX_INI_COUNT (60)  //最大迭代次数 60

// 判断点的时间先后顺序(注意curvature中存储的是时间戳)
const bool time_list(PointType &x, PointType &y) {return (x.curvature < y.curvature);};

class ImuProcess
{
public:
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW

    ImuProcess();
    ~ImuProcess();
    
    void Reset();
    void set_param(const V3D &transl, const M3D &rot, const V3D &gyr, const V3D &acc, const V3D &gyr_bias, const V3D &acc_bias);
    bool Process(const MeasureGroup &meas, esekfom::esekf &kf_state, PointCloudXYZI::Ptr &pcl_un_);
    
    // 协方差矩阵和状态参数
    Eigen::Matrix<double, 12, 12> Q;    // 噪声协方差矩阵
    V3D cov_acc;             // 加速度协方差
    V3D cov_gyr;             // 角速度协方差
    V3D cov_acc_scale;       // 外部传入的初始加速度协方差
    V3D cov_gyr_scale;       // 外部传入的初始角速度协方差
    V3D cov_bias_gyr;        // 角速度bias的协方差
    V3D cov_bias_acc;        // 加速度bias的协方差
    double first_lidar_time; // 当前帧第一个点云时间
    bool feats_undistort_vaild;

private:
    void IMU_init(const MeasureGroup &meas, esekfom::esekf &kf_state, int &N);
    void UndistortPcl(const MeasureGroup &meas, esekfom::esekf &kf_state, PointCloudXYZI &pcl_out);

    PointCloudXYZI::Ptr cur_pcl_un_;    // 当前帧点云(未去畸变)
    IMUData last_imu_;                  // 上一帧IMU数据
    std::vector<Pose6D> IMUpose;        // IMU位姿序列(用于反向传播)
    M3D Lidar_R_wrt_IMU;               // LiDAR到IMU的旋转外参
    V3D Lidar_T_wrt_IMU;               // LiDAR到IMU的平移外参
    V3D mean_acc;                      // 加速度均值
    V3D mean_gyr;                      // 角速度均值
    V3D angvel_last;                   // 上一帧角速度
    V3D acc_s_last;                    // 上一帧加速度
    double start_timestamp_;           // 开始时间戳
    double last_lidar_end_time_;       // 上一帧结束时间戳
    int init_iter_num;                 // 初始化迭代次数
    bool b_first_frame_;               // 是否是第一帧
    bool imu_need_init_;               // 是否需要初始化IMU
};

