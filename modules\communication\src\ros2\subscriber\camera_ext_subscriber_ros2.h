#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <Eigen/Dense>
#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <tf2/convert.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

#include "ros2/core/subscriber_base_ros2.h"
#include "data_types/transform_ext.h"

namespace communication::ros2
{

    class CameraExtSubscriberRos2 : public SubscriberBaseRos2<TransformExtrinsics, geometry_msgs::msg::TransformStamped>
    {
    public:
        CameraExtSubscriberRos2(rclcpp::Node::SharedPtr node,
                                const std::string &topic,
                                // typename SubscriberBaseRos2<TransformExtrinsics, geometry_msgs::msg::TransformStamped>::CallbackType callback = nullptr,
                                size_t max_buffer_size = 10)
            : SubscriberBaseRos2<TransformExtrinsics, geometry_msgs::msg::TransformStamped>(node, topic, max_buffer_size)
        {
        }

    protected:
        virtual void FromMsg(const geometry_msgs::msg::TransformStamped &msg, TransformExtrinsics &data) override
        {
            // data.time = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9;
            // // data.frame_id = msg.header.frame_id;
            // data.child_frame_id = msg.child_frame_id;

            // // Translation
            // data.x = msg.transform.translation.x;
            // data.y = msg.transform.translation.y;
            // data.z = msg.transform.translation.z;

            // // Rotation (quaternion)
            // data.qx = msg.transform.rotation.x;
            // data.qy = msg.transform.rotation.y;
            // data.qz = msg.transform.rotation.z;
            // data.qw = msg.transform.rotation.w;

            Eigen::Quaterniond eigen_quat(msg.transform.rotation.w, msg.transform.rotation.x, msg.transform.rotation.y, msg.transform.rotation.z);
            // Normalize to ensure unit quaternion
            eigen_quat.normalize();
            // Convert to rotation matrix
            Eigen::Matrix3d rot = eigen_quat.toRotationMatrix();

            data.r00 = rot(0, 0);
            data.r01 = rot(0, 1);
            data.r02 = rot(0, 2);
            data.r10 = rot(1, 0);
            data.r11 = rot(1, 1);
            data.r12 = rot(1, 2);
            data.r20 = rot(2, 0);
            data.r21 = rot(2, 1);
            data.r22 = rot(2, 2);
            data.tx = msg.transform.translation.x;
            data.ty = msg.transform.translation.y;
            data.tz = msg.transform.translation.z;
        }
    };

} // namespace communication::ros2

#endif
