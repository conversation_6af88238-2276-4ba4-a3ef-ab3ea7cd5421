#include <navigation/navigation.h>
namespace navigation_system
{

   navigation::navigation(costmap_2d::Costmap2DROS* planner_costmap_ros):planner_costmap_ros_(planner_costmap_ros){}
   
   void navigation::start()
   {
            {
           
               boost::thread *navigation_thread = new boost::thread(&navigation::makePlan,this);
               //lock.unlock();
               navigation_thread->join();
            }
            //boost::thread navigation_thread(&navigation::makePlan,this);
   }

   bool navigation::makePlan()
   {
      //while(1)
      {
          boost::unique_lock<boost::recursive_mutex> lock(planner_mutex_);
         global_cost_map_ = planner_costmap_ros_->getCostmap();
         costs_           = global_cost_map_->getCharMap(); 
         int rows = global_cost_map_->getSizeInCellsY();
         int cols = global_cost_map_->getSizeInCellsX();
         cv::Mat map_info(rows, cols, CV_8UC1, cv::Scalar(0));
         for(int i = 0; i < rows * cols; i++) {
             int y = i / cols;
             int x = i % cols;
             map_info.at<unsigned char>(y, x) = 254 - costs_[i];
         }
         cv::Mat map_info_Rotate;
         cv::rotate(map_info, map_info_Rotate, cv::ROTATE_90_COUNTERCLOCKWISE);
         // 你可以在这里使用 map_info_Rotate 进行后续处理
      }
      return true; // Add return statement
   }
   

}