#ifndef __IMAGE_SEGMENT__WITHOUT_NERUAL__H
#define __IMAGE_SEGMENT__WITHOUT_NERUAL__H

#include "image_segment/image_segment_impl.h"


namespace perception{
    
class ImageSegmentCpu : public ImageSegmentImpl{
    
public:
    ImageSegmentCpu();
    virtual ~ImageSegmentCpu();
    virtual bool Init(const std::string& model_path);
    virtual bool Inference(cv::Mat src,float box_thresh,float nms_thresh,SegmentDetectResult& out);

protected:
};

}

#endif