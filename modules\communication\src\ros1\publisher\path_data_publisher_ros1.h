/**
 * @file pose_data_publisher_ros1.h
 * @brief This file contains the implementation of a ROS1 publisher for pose data.
 * * It inherits from the PoseDataPublisherBase class and provides methods to publish pose data in ROS1 format.
 * * The publisher uses the geometry_msgs::PoseStamped message type to represent pose data.
 * * * The class provides methods to convert pose data to the appropriate ROS message format and publish it on a specified topic.
 * * It also includes methods to get the number of subscribers to the topic.
 */
#pragma once

#include <ros/ros.h>
#include <nav_msgs/Path.h>
#include "publisher_base.h"
#include "data_types/path_data.h"

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1
{
    class PathDataPublisherRos1 : public PathDataPublisherBase
    {

    public:
        PathDataPublisherRos1(ros::NodeHandle &nh, const std::string &topic,
                              const std::string &frame_id = "map", size_t max_buffer_size = 10);

        virtual ~PathDataPublisherRos1() = default;

    protected:
        virtual void PublishMsg() override
        {
            publisher_.publish(msg_);
        }

        virtual void ToMsg() override
        {
            msg_.header.stamp = ros::Time(data_.time_);
            msg_.header.frame_id = frame_id_;
            msg_.poses.clear();
            for (const auto &pose : data_.poses_)
            {
                geometry_msgs::PoseStamped pose_stamped;
                pose_stamped.header.stamp = ros::Time(pose.time);
                pose_stamped.pose.position.x = pose.position(0);
                pose_stamped.pose.position.y = pose.position(1);
                pose_stamped.pose.position.z = pose.position(2);
                pose_stamped.pose.orientation.x = pose.orientation(0);
                pose_stamped.pose.orientation.y = pose.orientation(1);
                pose_stamped.pose.orientation.z = pose.orientation(2);
                pose_stamped.pose.orientation.w = pose.orientation(3);
                msg_.poses.push_back(pose_stamped);
            }
        }

        virtual int GetSubscriberCount() override
        {
            return publisher_.getNumSubscribers();
        }

    private:
        ros::NodeHandle &nh_;
        ros::Publisher publisher_;
        nav_msgs::Path msg_;   // ROS message type for path data
        std::string frame_id_; // Frame ID for the path data
    };

} // namespace communication::ros1

#endif
