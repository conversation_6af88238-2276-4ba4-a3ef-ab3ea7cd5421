#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
// #include <functional>
#include "subscriber_base.h"

namespace communication::ros2
{

    template <typename T, typename T_MSG>
    class SubscriberBaseRos2 : public SubscriberBase<T>
    {
    public:
        // using CallbackType = std::function<void(const T&)>;

        SubscriberBaseRos2(rclcpp::Node::SharedPtr node,
                           const std::string &topic,
                           //   CallbackType callback = nullptr,
                           size_t max_buffer_size = 10)
            : SubscriberBase<T>(topic, max_buffer_size),
              //   callback_(callback),
              node_(node)
        {
            auto qos = rclcpp::QoS(max_buffer_size);
            subscription_ = node_->create_subscription<T_MSG>(
                topic,
                qos,
                [this](const typename T_MSG::SharedPtr msg)
                {
                    T data;
                    FromMsg(*msg, data);
                    // if (callback_)
                    // {
                    //     this->callback_(data);
                    // }

                    // Add to buffer for GetBuffer() functionality
                    {
                        std::lock_guard<std::mutex> lock(this->buffer_mutex_);
                        this->data_buffer_.push_back(data);
                        if (this->data_buffer_.size() > this->max_buffer_size_)
                        {
                            this->data_buffer_.pop_front();
                        }
                    }
                });
        }

        virtual ~SubscriberBaseRos2() = default;

    protected:
        // Derived classes must implement this conversion
        virtual void FromMsg(const T_MSG &msg, T &data) = 0;

    private:
        // CallbackType callback_;
        rclcpp::Node::SharedPtr node_;
        typename rclcpp::Subscription<T_MSG>::SharedPtr subscription_;
    };

} // namespace communication::ros2

#endif
