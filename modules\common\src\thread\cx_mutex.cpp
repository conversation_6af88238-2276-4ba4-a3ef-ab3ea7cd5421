﻿
#include "cx_mutex.h"
namespace common_lib {
CXMutex::CXMutex() {
#ifdef WIN32
  InitializeCriticalSection(&m_cs);
#else
  pthread_mutex_init(&mutex_, NULL);
#endif

  SetRecursiveAttr();
}

CXMutex::~CXMutex() {
#ifdef WIN32
  DeleteCriticalSection(&m_cs);
#else
  pthread_mutex_destroy(&mutex_);
#endif
}

cx_int CXMutex::Lock() {
#ifdef WIN32
  EnterCriticalSection(&m_cs);
#else
  pthread_mutex_lock(&mutex_);
#endif

  return 0;
}

cx_int CXMutex::Unlock() {
#ifdef WIN32
  LeaveCriticalSection(&m_cs);
#else
  pthread_mutex_unlock(&mutex_);
#endif

  return 0;
}

cx_int CXMutex::SetRecursiveAttr() {
#ifndef WIN32
  pthread_mutexattr_t mattr;
  pthread_mutexattr_init(&mattr);
  pthread_mutexattr_settype(&mattr, PTHREAD_MUTEX_RECURSIVE_NP);

  pthread_mutex_init(&mutex_, &mattr);

  pthread_mutexattr_destroy(&mattr);
#endif

  return 0;
}
}  // namespace common_lib