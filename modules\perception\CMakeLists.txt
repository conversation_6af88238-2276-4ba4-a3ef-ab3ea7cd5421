# perception/CMakeLists.txt
cmake_minimum_required(VERSION 3.5)
project(perception)

# 全局设置库文件输出目录
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)

#set(CMAKE_BUILD_TYPE "Release")
#set(CMAKE_BUILD_TYPE "Debug")
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_FLAGS "-std=c++17 -march=native")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall")

find_package(OpenCV REQUIRED)
find_package(PCL REQUIRED COMPONENTS common io visualization)

include_directories(
    include
    src
    ${OpenCV_INCLUDE_DIRS}
    ${PCL_INCLUDE_DIRS}
    ${CMAKE_CURRENT_SOURCE_DIR}/../communication/include 
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include
)

# 瑞芯微 平台使用rknn
if(NEURAL_TYPE STREQUAL "rknn")

  set(RGA_PATH ${CMAKE_CURRENT_SOURCE_DIR}/rknn/librga)
  set(LIBRGA ${RGA_PATH}/${CMAKE_SYSTEM_NAME}/aarch64/librga.so)
  set(RGA_INCLUDES ${RGA_PATH}/include)

  add_definitions(-DLIBRGA_IM2D_HANDLE)
  add_definitions(-DDMA_ALLOC_DMA32)

  # rknn runtime
  set(RKNN_PATH ${CMAKE_CURRENT_SOURCE_DIR}/rknn/rknpu2)
  set(LIBRKNNRT ${RKNN_PATH}/${CMAKE_SYSTEM_NAME}/aarch64/librknnrt.so)
  set(LIBRKNNRT_INCLUDES ${RKNN_PATH}/include)

  set(LIBRKNNRT ${LIBRKNNRT})

  include_directories(
    ${RGA_INCLUDES}
    ${LIBRKNNRT_INCLUDES}
    rknn/include
    ${CMAKE_CURRENT_SOURCE_DIR}/rknn/allocator/dma
    ${CMAKE_CURRENT_SOURCE_DIR}/rknn/allocator/drm
  )
  set( COMMON_SRC   src/common/utils.cpp
                    src/common/rknn_pointcloud_model.cpp
      )
  set( SEGMENT_SRC  src/image_segment/image_segment_rknn.cpp
                    src/image_segment/rknn/postprocess.cc
                    src/image_segment/rknn/yolov8_seg.cc
                    src/image_segment/rknn/image_utils.c
                    src/image_segment/rknn/yolov8_rknn.cpp
                    src/image_segment/rknn/easy_timer.cpp
                    ${CMAKE_CURRENT_SOURCE_DIR}/rknn/allocator/dma/dma_alloc.cpp                  
  )
elseif(NEURAL_TYPE STREQUAL "cuda")
  set(CMAKE_CUDA_COMPILER /usr/local/cuda/bin/nvcc)
  file(GLOB_RECURSE CUDA_SRCS src/image_segment/yolov8_cuda/*.cpp src/image_segment/yolov8_cuda/*.cu)
  enable_language(CUDA)
  set( COMMON_SRC   src/common/utils.cpp)
  set( SEGMENT_SRC src/image_segment/image_segment_cuda.cpp ${CUDA_SRCS})
  if (CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
    message("embed_platform on")
    include_directories(/usr/local/cuda/targets/aarch64-linux/include)
    link_directories(/usr/local/cuda/targets/aarch64-linux/lib)
  else()
    message("embed_platform off")
    # cuda
    include_directories(/usr/local/cuda/include)
    link_directories(/usr/local/cuda/lib64)
    # tensorrt
    #include_directories(/home/<USER>/TensorRT-8.6.1.6/include)
    #link_directories(/home/<USER>/TensorRT-8.6.1.6/lib)
  endif()
elseif(NEURAL_TYPE STREQUAL "cpu")
  set( COMMON_SRC   src/common/utils.cpp)
  set( SEGMENT_SRC src/image_segment/image_segment_cpu.cpp)
endif()

add_library(${PROJECT_NAME}_core
  src/perception.cpp
  src/perception_impl.cpp
  src/pointcloud_prejection.cpp
  src/image_segment/image_segment.cpp
  src/obstacle_devide/patchworkpp.cpp
  src/obstacle_devide/obstacle_devide.cpp
  src/perception_yolo_patchwork.cpp
  src/depth_pointcloud/depth_pointcloud.cpp
  src/depth_pointcloud/depth_pointcloud_cpu.cpp
  ${COMMON_SRC}
  ${SEGMENT_SRC}
)

link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../communication/lib
                ${CMAKE_CURRENT_SOURCE_DIR}/../common/lib
                ${CMAKE_CURRENT_SOURCE_DIR}/lib

)


if(NEURAL_TYPE STREQUAL "rknn")
  target_compile_definitions(${PROJECT_NAME}_core PRIVATE NEURAL_RKNN)
  target_link_libraries(${PROJECT_NAME}_core ${OpenCV_LIBS} ${PCL_LIBRARIES} ${LIBRGA} ${LIBRKNNRT} )
elseif(NEURAL_TYPE STREQUAL "cpu")
  target_compile_definitions(${PROJECT_NAME}_core PRIVATE NEURAL_CPU)

  target_link_libraries(${PROJECT_NAME}_core ${OpenCV_LIBS} ${PCL_LIBRARIES} )
elseif(NEURAL_TYPE STREQUAL "cuda")
  target_compile_definitions(${PROJECT_NAME}_core PRIVATE NEURAL_CUDA)
  target_link_libraries(${PROJECT_NAME}_core ${OpenCV_LIBS} ${PCL_LIBRARIES} nvinfer cudart )
endif()



add_executable(${PROJECT_NAME}_node
  src/perception_node.cpp
)

target_link_libraries(${PROJECT_NAME}_node 
                    common_lib
                    communication_core
                    ${PROJECT_NAME}_core 
                    ${OpenCV_LIBRARIES}
                    ${PCL_LIBRARIES}
)


# if (${CMAKE_BUILD_TYPE} STREQUAL "debug")
#   target_compile_definitions(${PROJECT_NAME}_core PRIVATE OUTPUT_DEBUG)
#   target_compile_definitions(${PROJECT_NAME}_node PRIVATE OUTPUT_DEBUG)
# endif()



