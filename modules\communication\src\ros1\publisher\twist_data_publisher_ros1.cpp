#include "publisher/twist_data_publisher_ros1.h"
#include <geometry_msgs/Twist.h>

#if COMMUNICATION_TYPE == ROS1
namespace communication::ros1 {

TwistDataPublisherRos1::TwistDataPublisherRos1(ros::NodeHandle &nh, const std::string &topic,       
                                             const std::string &frame_id, size_t max_buffer_size) 
                                            : TwistDataPublisherBase(topic, max_buffer_size), nh_(nh), frame_id_(frame_id) {
    publisher_ = nh_.advertise<geometry_msgs::Twist>(topic, max_buffer_size);
}

} // namespace communication::ros1

#endif // COMMUNICATION_TYPE == ROS1
