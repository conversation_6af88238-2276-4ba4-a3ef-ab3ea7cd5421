/**
 * @file state_machine.h
 * @brief State machine for decision-making process.
 */
#pragma once

#include <string>

 namespace decision{

class Decision;
     // 状态基类
class State{
public:
    virtual ~State() = default;
    virtual void enter(Decision &system) = 0;
    virtual void execute(Decision &system, DecisionData &decision_out) = 0;
    virtual void exit(Decision &system) = 0;
    virtual std::string getName() const = 0;
};

    // 具体状态类声明
class StartAccelState : public State {
public:
    void enter(Decision& system) override;
    void execute(Decision& system, DecisionData& decision_out) override;
    void exit(Decision& system) override;
    std::string getName() const override { return "START_ACCEL"; }
};
    
class NormalCruiseState : public State {
public:
    void enter(Decision& system) override;
    void execute(Decision& system, DecisionData& decision_out) override;
    void exit(Decision& system) override;
    std::string getName() const override { return "NORMAL_CRUISE"; }
};
    
class ObstacleAvoidState : public State {
public:
    void enter(Decision& system) override;
    void execute(Decision& system, DecisionData& decision_out) override;
    void exit(Decision& system) override;
    std::string getName() const override { return "OBSTACLE_AVOID"; }
};
    
class DecelerateState : public State {
public:
    void enter(Decision& system) override;
    void execute(Decision& system, DecisionData& decision_out) override;
    void exit(Decision& system) override;
    std::string getName() const override { return "DECELERATE"; }
};
    
class StopState : public State {
public:
    void enter(Decision& system) override;
    void execute(Decision& system, DecisionData& decision_out) override;
    void exit(Decision& system) override;
    std::string getName() const override { return "STOP"; }
};
    
class CenterNavState : public State {
public:
    void enter(Decision& system) override;
    void execute(Decision& system, DecisionData& decision_out) override;
    void exit(Decision& system) override;
    std::string getName() const override { return "CENTER_NAV"; }
};
    
class YieldState : public State {
public:
    void enter(Decision& system) override;
    void execute(Decision& system, DecisionData& decision_out) override;
    void exit(Decision& system) override;
    std::string getName() const override { return "YIELD"; }
};
    
class SlopeState : public State {
public:
    void enter(Decision& system) override;
    void execute(Decision& system, DecisionData& decision_out) override;
    void exit(Decision& system) override;
    std::string getName() const override { return "SLOPE"; }
};
    
class StairState : public State {
public:
    void enter(Decision& system) override;
    void execute(Decision& system, DecisionData& decision_out) override;
    void exit(Decision& system) override;
    std::string getName() const override { return "STAIR"; }
};

class RoughTerrainState : public State {
public:
    void enter(Decision& system) override;
    void execute(Decision& system, DecisionData& decision_out) override;
    void exit(Decision& system) override;
    std::string getName() const override { return "ROUGH_TERRAIN"; }
};

}