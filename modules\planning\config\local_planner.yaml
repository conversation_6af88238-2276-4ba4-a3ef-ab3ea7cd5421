# LocalPlanner 配置文件
# 去ROS化局部规划器参数配置

# 路径配置
path_config:
  path_folder: "../paths"  # 路径文件夹

# 车辆配置
vehicle_config:
  length: 1.2                      # 车辆长度 (m)
  width: 0.8                       # 车辆宽度 (m)
  sensor_offset_x: 0.0             # 传感器X方向偏移 (m)
  sensor_offset_y: 0.0             # 传感器Y方向偏移 (m)
  two_way_drive: false             # 是否启用双向驱动
  max_speed: 0.8                   # 最大速度 (m/s)

# 点云处理配置
pointcloud_config:
  laser_voxel_size: 0.05           # 激光点云体素大小 (m)
  terrain_voxel_size: 0.2          # 地形点云体素大小 (m)
  use_terrain_analysis: true       # 是否使用地形分析
  check_rot_obstacle: false        # 是否检查旋转障碍物
  adjacent_range: 4.25             # 邻近范围 (m)
  obstacle_height_thre: 0.15       # 障碍物高度阈值 (m)
  ground_height_thre: 0.1          # 地面高度阈值 (m)
  cost_height_thre: 0.1            # 代价高度阈值 (m)
  cost_score: 0.02                 # 代价分数
  use_cost: false                  # 是否使用代价
  point_per_path_thre: 2           # 每条路径点数阈值
  min_rel_z: -0.8                  # 最小相对Z值 (m)
  max_rel_z: 0.25                  # 最大相对Z值 (m)

# 路径规划配置
planning_config:
  dir_weight: 0.02                 # 方向权重
  dir_thre: 90.0                   # 方向阈值 (deg)
  dir_to_vehicle: false            # 方向是否朝向车辆
  path_scale: 1.25                 # 路径尺度
  min_path_scale: 0.75             # 最小路径尺度
  path_scale_step: 0.25            # 路径尺度步长
  path_scale_by_speed: true        # 是否根据速度调整路径尺度
  min_path_range: 1.0              # 最小路径范围 (m)
  path_range_step: 0.5             # 路径范围步长 (m)
  path_range_by_speed: true        # 是否根据速度调整路径范围
  path_crop_by_goal: true          # 是否根据目标裁剪路径
  goal_clear_range: 0.5            # 目标清除范围 (m)
  arrived_dis_threshold: 0.2       # 到达距离阈值 (m)

# 网格配置
grid_config:
  grid_voxel_size: 0.02            # 网格体素大小 (m)
  search_radius: 0.45              # 搜索半径 (m)
  grid_voxel_offset_x: 3.2         # 网格体素X偏移 (m)
  grid_voxel_offset_y: 4.5         # 网格体素Y偏移 (m)

# 控制参数配置
control_config:
  control_frequency: 100           # 控制频率 (Hz)
  timeout_threshold: 0.5           # 超时阈值 (s)

# 调试配置
debug_config:
  enable_debug: false              # 是否启用调试
  print_status: false              # 是否打印状态
  debug_output_path: "/tmp/local_planner_debug" # 调试输出路径

# 安全配置
safety_config:
  enable_safety_checks: true       # 是否启用安全检查
  emergency_stop_enabled: true     # 是否启用紧急停止
  location_failure_handling: true  # 是否处理定位失败

# 性能配置
performance_config:
  enable_fast_math: false          # 是否启用快速数学运算
  use_lookup_tables: false         # 是否使用查找表
  preallocate_buffers: true        # 是否预分配缓冲区
  buffer_size: 1000                # 缓冲区大小

# 算法配置
algorithm_config:
  # 路径搜索算法
  path_search:
    enable_multi_scale: true       # 是否启用多尺度搜索
    enable_adaptive_range: true    # 是否启用自适应范围
    max_search_iterations: 10      # 最大搜索迭代次数
  
  # 障碍物检测
  obstacle_detection:
    enable_dynamic_obstacles: true # 是否启用动态障碍物检测
    obstacle_prediction_time: 1.0  # 障碍物预测时间 (s)
    safety_margin: 0.2             # 安全边距 (m)
  
  # 路径评分
  path_scoring:
    distance_weight: 0.3           # 距离权重
    direction_weight: 0.4          # 方向权重
    obstacle_weight: 0.3           # 障碍物权重
    smoothness_weight: 0.1         # 平滑度权重

# 环境适应配置
environment_config:
  # 室内环境
  indoor:
    max_speed: 0.5                 # 室内最大速度 (m/s)
    path_scale: 1.0                # 室内路径尺度
    adjacent_range: 3.0            # 室内邻近范围 (m)
  
  # 户外环境
  outdoor:
    max_speed: 1.0                 # 户外最大速度 (m/s)
    path_scale: 1.5                # 户外路径尺度
    adjacent_range: 5.0            # 户外邻近范围 (m)
  
  # 狭窄通道
  narrow_passage:
    max_speed: 0.3                 # 狭窄通道最大速度 (m/s)
    path_scale: 0.8                # 狭窄通道路径尺度
    adjacent_range: 2.0            # 狭窄通道邻近范围 (m)

# 日志配置
logging_config:
  log_level: "INFO"                # 日志级别: DEBUG, INFO, WARN, ERROR
  log_to_file: false               # 是否记录到文件
  log_file_path: "/tmp/local_planner.log" # 日志文件路径
  max_log_file_size: 10            # 最大日志文件大小 (MB)
  
  # 特定日志开关
  log_path_updates: false          # 是否记录路径更新
  log_goal_updates: true           # 是否记录目标更新
  log_obstacle_detection: false    # 是否记录障碍物检测
  log_planning_results: true       # 是否记录规划结果

# 测试配置
test_config:
  enable_simulation_mode: false    # 是否启用仿真模式
  simulation_time_scale: 1.0       # 仿真时间缩放
  add_noise: false                 # 是否添加噪声
  noise_level: 0.01                # 噪声级别
  
  # 测试场景
  test_scenarios:
    - name: "straight_line"        # 直线测试
      start: [0.0, 0.0, 0.0]
      goal: [10.0, 0.0, 0.0]
    
    - name: "turn_left"            # 左转测试
      start: [0.0, 0.0, 0.0]
      goal: [5.0, 5.0, 1.57]
    
    - name: "obstacle_avoidance"   # 避障测试
      start: [0.0, 0.0, 0.0]
      goal: [10.0, 0.0, 0.0]
      obstacles:
        - [5.0, 0.0, 1.0]          # 中间障碍物

# 高级配置
advanced_config:
  # 内存管理
  memory_management:
    enable_memory_pool: false      # 是否启用内存池
    pool_size: 1024                # 内存池大小 (KB)
    enable_garbage_collection: true # 是否启用垃圾回收
  
  # 多线程
  threading:
    enable_multithreading: false   # 是否启用多线程
    num_threads: 2                 # 线程数量
    thread_priority: 0             # 线程优先级
  
  # 优化选项
  optimization:
    enable_compiler_optimizations: true # 是否启用编译器优化
    use_vectorization: false       # 是否使用向量化
    cache_optimization: true       # 是否启用缓存优化
