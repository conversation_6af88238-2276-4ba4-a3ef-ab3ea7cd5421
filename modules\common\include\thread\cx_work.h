#ifndef _CX_WORK_H_
#define _CX_WORK_H_

#include "cx_pubhead.h"

#ifdef WIN32
#include "windows.h"
#endif
namespace common_lib {
#ifdef WIN32
typedef void ThreadProc(PTP_CALLBACK_INSTANCE Instance, PVOID Context);
#else
typedef void *ThreadProc(void *);
#endif

class CXWork {
 public:
  CXWork();
  virtual ~CXWork();

#ifdef WIN32
  CXWork(ThreadProc *pfnThreadProc, LPVOID const pContext);
#endif

 public:
  cx_int SetName(cx_string name);
  const cx_string &GetName() const;

 public:
  cx_int SetThreadProc(ThreadProc *thread_proc);
  cx_int SetThreadContext(LPVOID context);

  ThreadProc *GetThreadProc();
  LPVOID GetContext();

 private:
  cx_int Run();

 private:
  ThreadProc *thread_proc_;
  LPVOID context_ptr_;

  cx_string name_;

  friend class CXThread;
};
}  // namespace common_lib

#endif  // _CX_WORK_H_
