
#include "depth_pointcloud.h"



#include "depth_pointcloud_cpu.h"



namespace perception{

DepthPointCloudConvert::DepthPointCloudConvert(){

    depth_pointcloud_convert_impl_ = std::make_unique<DepthPointCloudConvertCpu>();
    
}

DepthPointCloudConvert::~DepthPointCloudConvert(){}


void DepthPointCloudConvert::Init(const DepthPointCloudConvertParameter& parameter){
    depth_pointcloud_convert_impl_->Init(parameter);
}

bool DepthPointCloudConvert::Convert(cv::Mat depth,PointCloudT::Ptr& cloud){
    return depth_pointcloud_convert_impl_->Convert(depth,cloud);
}



}