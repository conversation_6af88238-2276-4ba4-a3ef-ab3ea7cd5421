#ifndef _CX_EVENT_H_
#define _CX_EVENT_H_

#include "cx_pubhead.h"

#ifdef WIN32
#include "windows.h"
#else
#include <pthread.h>
#include <sys/time.h>
const int INFINITE = -1;
#endif

namespace common_lib {
class CXEvent {
 public:
  CXEvent(cx_bool is_manual_reset = true, cx_bool is_initial_signaled = false);
  ~CXEvent();

 public:
  cx_bool Create();
  cx_bool Set();
  cx_bool Reset();
  cx_bool Wait(int cms);
  cx_bool Destroy();
  cx_bool IsTriggered();

 private:
  cx_bool EnsureInitialized();

 private:
  cx_bool is_manual_reset_;
  cx_bool is_event_status_;

#ifdef WIN32
  cx_handle m_hEvent;
#else
  cx_bool is_mutex_initialized_;
  pthread_mutex_t mutex_;
  cx_bool is_cond_initialized_;
  pthread_cond_t cond_;
#endif
};
}  // namespace common_lib
#endif  // _CX_EVENT_H_
