# Controller Module Launch Files

## 概述

本目录包含controller模块的ROS launch文件，用于启动calibration_node和velocity_smoother_node。

## 文件说明

### controller.launch
主要的launch文件，用于启动controller模块的所有节点。

**功能：**
- 启动calibration_node（校准节点）
- 启动velocity_smoother_node（速度平滑节点）
- 启动RViz可视化界面
- 配置TF变换

**使用方法：**
```bash
# 启动所有节点
roslaunch controller controller.launch

# 不启动RViz
roslaunch controller controller.launch use_rviz:=false

# 指定配置文件路径
roslaunch controller controller.launch config_dir:=/path/to/config
```

## 参数说明

### 全局参数
- `config_dir`: 配置文件目录路径（默认：$(find controller)/config）
- `rviz_config`: RViz配置文件路径（默认：$(find controller)/rviz/controller.rviz）
- `use_rviz`: 是否启动RViz（默认：true）

### calibration_node参数
- `config_file`: 校准配置文件路径
- `communication_config`: 通信配置文件路径

### velocity_smoother_node参数
- `config_file`: 速度平滑器配置文件路径
- `communication_config`: 通信配置文件路径
- `speed_lim_v`: 最大线速度（默认：2.0 m/s）
- `speed_lim_w`: 最大角速度（默认：1.5 rad/s）
- `accel_lim_v`: 线加速度限制（默认：1.0 m/s²）
- `accel_lim_w`: 角加速度限制（默认：0.8 rad/s²）
- `decel_factor`: 减速因子（默认：2.0）
- `frequency`: 控制频率（默认：50.0 Hz）

## 话题映射

### 输入话题
- `/Odometry` → 里程计数据
- `/move_base_simple/goal` → 导航目标
- `/web_goal` → Web界面目标
- `/calibration_mode` → 校准模式
- `/cmd_vel` → 速度命令
- `/twist/data` → 速度数据

### 输出话题
- `/cmd_vel_raw` → 原始速度命令
- `/cmd_vel_smooth` → 平滑后速度命令
- `/emergency_stop` → 紧急停止
- `/inner_emergency_stop` → 内部紧急停止
- `/calibration_mode_status` → 校准模式状态

## 依赖项

- ROS1
- rviz
- tf2_ros
- geometry_msgs
- nav_msgs
- sensor_msgs

## 故障排除

1. **节点启动失败**
   - 检查配置文件路径是否正确
   - 确认可执行文件是否存在
   - 查看日志输出

2. **话题连接失败**
   - 检查话题名称是否正确
   - 确认发布者和订阅者都已启动
   - 使用`rostopic list`和`rostopic echo`检查话题

3. **TF变换错误**
   - 检查坐标系名称是否正确
   - 确认TF发布者已启动
   - 使用`tf_echo`检查变换

## 示例

### 基本启动
```bash
roslaunch controller controller.launch
```

### 自定义配置
```bash
roslaunch controller controller.launch \
  config_dir:=/home/<USER>/custom_config \
  use_rviz:=false
```

### 调试模式
```bash
roslaunch controller controller.launch \
  use_rviz:=true \
  --screen
``` 