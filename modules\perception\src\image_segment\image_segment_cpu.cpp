#include "image_segment_cpu.h"

using namespace perception;

ImageSegmentCpu::ImageSegmentCpu():
    ImageSegmentImpl(){
        
}
ImageSegmentCpu::~ImageSegmentCpu(){

}
bool ImageSegmentCpu::Init(const std::string& model_path){
    return true;
}
bool ImageSegmentCpu::Inference(cv::Mat src,float box_thresh,float nms_thresh,SegmentDetectResult& out) {
    out.mask=cv::Mat::zeros(src.cols,src.rows,CV_8UC1);
    return true;
}   

