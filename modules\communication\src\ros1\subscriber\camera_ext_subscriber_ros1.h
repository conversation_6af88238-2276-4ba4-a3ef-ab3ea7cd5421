#pragma once
#if COMMUNICATION_TYPE == ROS1
#include <ros/ros.h>
#include "Extrinsics.h"
#include "subscriber_base.h"
#include "data_types/transform_ext.h"
namespace communication::ros1 {

class CameraExtSubscriberRos1 : public CameraExtSubscriberBase {
 public:
  CameraExtSubscriberRos1(ros::NodeHandle &nh, const std::string &topic,
                               size_t max_buffer_size = 10): CameraExtSubscriberBase(topic, max_buffer_size), nh_(nh) {
    subscriber_ = nh_.subscribe(topic, max_buffer_size, &CameraExtSubscriberRos1::CameraExtCallbackRos1, this);
  } 

  ~CameraExtSubscriberRos1() = default;

  void CameraExtCallbackRos1(
      const realsense2_camera::Extrinsics::ConstPtr &camera_ext_msg){
    // Convert the ROS message to TransformRealsense and store it in the buffer
    TransformExtrinsics transform_ext;
    transform_ext.r00 = camera_ext_msg->rotation[0];
    transform_ext.r01 = camera_ext_msg->rotation[1];
    transform_ext.r02 = camera_ext_msg->rotation[2];
    transform_ext.r10 = camera_ext_msg->rotation[3];
    transform_ext.r11 = camera_ext_msg->rotation[4];
    transform_ext.r12 = camera_ext_msg->rotation[5];
    transform_ext.r20 = camera_ext_msg->rotation[6];
    transform_ext.r21 = camera_ext_msg->rotation[7];
    transform_ext.r22 = camera_ext_msg->rotation[8];
    transform_ext.tx = camera_ext_msg->translation[0];
    transform_ext.ty = camera_ext_msg->translation[1];
    transform_ext.tz = camera_ext_msg->translation[2];

    // Store the transform_ext in the data buffer
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    data_buffer_.push_back(transform_ext);
    if (data_buffer_.size() > max_buffer_size_) {
      data_buffer_.pop_front(); // Remove oldest data if buffer exceeds max size
    }

   }
 private:
  ros::NodeHandle &nh_;
  ros::Subscriber subscriber_;
};

} // namespace communication::ros1
#endif // COMMUNICATION_TYPE == ROS1

