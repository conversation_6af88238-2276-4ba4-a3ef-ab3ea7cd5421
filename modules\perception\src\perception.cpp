#include "perception.h"
#include "perception_yolo_patchwork.h"

namespace perception
{

  Perception::Perception(const std::string &config_file_path)
  {
    perception_impl_ = std::make_unique<PerceptionYoloPatchwork>(config_file_path);
    
  }

  Perception::~Perception(){
  }

  void Perception::PushCloud(PointCloudT::Ptr cloud){
    perception_impl_->PushCloud(cloud);

  }
  void Perception::PushRgbImage(cv::Mat rgb){
    perception_impl_->PushRgbImage(rgb);
  }
  void Perception::PushDepthImage(cv::Mat depth){
    perception_impl_->PushDepthImage(depth);
  }
  void Perception::SetRgbIntrinsic(const CameraInfo& info ){
    perception_impl_->SetRgbIntrinsic(info);
  }
  void Perception::SetDepthIntrinsic(const CameraInfo& info ){
    perception_impl_->SetDepthIntrinsic(info);
  }
  void Perception::SetDepth2RgbTransform(const TransformExtrinsics& transform){
    perception_impl_->SetDepth2RgbTransform(transform);
  }

  void Perception::SetOutputCallback(PerceptionOutputCallback callback){
    perception_impl_->SetOutputCallback(callback);
  }

} // namespace perception {
