#ifndef __REPREJECTION__H__
#define __REPREJECTION__H__


#include <Eigen/Eigen>
#include <opencv2/opencv.hpp>
#include <vector>
#include <chrono>

#include "common/utils.h"
#include "common/threadsafe_timeout.hpp"

/*
* 将雷达坐标系下的三维空间点投影到图像坐标
*/
namespace perception{

class Reprejection{
public:
    Reprejection();
    
    ~Reprejection();
    Reprejection(const Reprejection& other)=default;
    void Init(Eigen::Vector4d K,Eigen::VectorXd dist,Eigen::VectorXd t_lidar_camera);
    bool Preject(Eigen::Vector3d point3d,Eigen::Vector2d& image_pt);

    void ResetMask(cv::Mat mask,float timeout=0.1);
    int  PixelType(Eigen::Vector2d pixel);
    bool Timeout();

    cv::Mat DebugImg(PointCloudT::Ptr cloud);
public:    
    Eigen::Matrix3d K_;         //内参矩阵
    Eigen::VectorXd dist_;      //畸变系数
    Eigen::Matrix3d r_l2c_;     //相机在雷达坐标系下的旋转
    Eigen::Vector3d t_l2c_;     //相机在雷达坐标系下的平移
    ThreadSafeTimed<cv::Mat> mask_;

};

}
#endif