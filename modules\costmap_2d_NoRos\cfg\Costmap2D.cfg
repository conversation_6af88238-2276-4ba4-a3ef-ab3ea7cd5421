#!/usr/bin/env python

from dynamic_reconfigure.parameter_generator_catkin import ParameterGenerator, double_t, int_t, str_t

gen = ParameterGenerator()

gen.add("transform_tolerance", double_t, 0, "Specifies the delay in transform (tf) data that is tolerable in seconds.", 0.3, 0, 10)
gen.add("update_frequency", double_t, 0, "The frequency in Hz for the map to be updated.", 5, 0, 100)
gen.add("publish_frequency", double_t, 0, "The frequency in Hz for the map to be publish display information.", 0, 0, 100)

#map params
gen.add("width", int_t, 0, "The width of the map in meters.", 10, 0)
gen.add("height", int_t, 0, "The height of the map in meters.", 10, 0)
gen.add("resolution", double_t, 0, "The resolution of the map in meters/cell.", 0.05, 0, 50)
gen.add("origin_x", double_t, 0, "The x origin of the map in the global frame in meters.", 0)
gen.add("origin_y", double_t, 0, "The y origin of the map in the global frame in meters.", 0)

# robot footprint shape
gen.add("footprint", str_t, 0, "The footprint of the robot specified in the robot_base_frame coordinate frame as a list in the format: [ [x1, y1], [x2, y2], ...., [xn, yn] ].", "[]")
gen.add("robot_radius", double_t, 0, 'The radius of the robot in meters, this parameter should only be set for circular robots, all others should use the footprint parameter described above.', 0.46, 0, 10)
gen.add("footprint_padding", double_t, 0, "How much to pad (increase the size of) the footprint, in meters.", 0.01)

exit(gen.generate("costmap_2d", "costmap_2d", "Costmap2D"))
