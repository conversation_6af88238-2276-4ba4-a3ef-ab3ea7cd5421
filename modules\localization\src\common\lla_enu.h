#ifndef LLA_ENU_H
#define LLA_ENU_H

#include <stdio.h>
#include <iostream>
#include <cmath>
#include <Eigen/Core>
#include <Eigen/Eigen>
#include <Eigen/Dense>

class LLAENU {
public:
    LLAENU();
    LLAENU(double longitude, double latitude, double altitude = 0.0);

    void Initialize();
    void SetOriginLLA(double longitude, double latitude, double altitude = 0.0);
    double RadToDeg(double rad);

    Eigen::Vector3d LLAToENU(double longitude, double latitude, double altitude = 0.0);
    Eigen::Vector3d ENUToLLA(double local_e, double local_n, double local_u = 0.0);

    // 地球参数
    double Re_ = 6378137.0; // 地球半径
    double f_ = 1/298.257; // 偏心率
    double local_Rn_, local_Rm_; // 原点处子午、卯酉圈半径

    // 经度(rad), 纬度(rad), 高度(m)
    double longitude_, latitude_, altitude_;
    bool origin_lla_initialized_ = false;
    double ratio_x_, ratio_y_, inv_ratio_x_, inv_ratio_y_;
};

#endif