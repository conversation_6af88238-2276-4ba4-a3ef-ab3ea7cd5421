cmake_minimum_required(VERSION 3.5)
project(common)

# Set output directories
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)

if(NOT DEFINED CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Debug")
endif()

if(NOT DEFINED PLATFORM)
    set(PLATFORM "x86_64")
endif()

if(NOT DEFINED COMMUNICATION_TYPE)
    set(COMMUNICATION_TYPE "ROS1")
endif()

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall")
set(CMAKE_CXX_FLAGS_RELEASE "-O3")

message("Starting to parse CMake file for Common project.")

if(CMAKE_SYSTEM_NAME MATCHES "Linux")
    add_compile_definitions(LINUX)
    # 或者可以添加平台特定的宏
    add_compile_definitions(LINUX_OS)
endif()

find_package(PCL REQUIRED)
find_package(PkgConfig REQUIRED)
pkg_check_modules(YAML_CPP REQUIRED yaml-cpp)
# 方法1：通过 pkg-config 指定版本
pkg_check_modules(YAML REQUIRED yaml-0.1>=0.2.2)
# pkg_check_modules(YAML REQUIRED yaml-0.1)

# Log4cplus configuration
option(ENABLE_LOG4CPLUS "Enable log4cplus logging support" ON)

if(ENABLE_LOG4CPLUS)
    find_package(log4cplus QUIET)
    if(log4cplus_FOUND)
        if(CMAKE_BUILD_TYPE STREQUAL "Release")
            message(STATUS "Release build detected - log4cplus will be disabled")
            set(USE_LOG4CPLUS FALSE)
        else()
            message(STATUS "Non-Release build detected - log4cplus will be enabled")
            set(USE_LOG4CPLUS TRUE)
            add_definitions(-DUSE_LOG4CPLUS)
            include_directories(${log4cplus_INCLUDE_DIRS})
        endif()
    else()
        message(WARNING "log4cplus not found - logging will be disabled")
        set(USE_LOG4CPLUS FALSE)
    endif()
endif()

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/include/algorithm
    ${CMAKE_CURRENT_SOURCE_DIR}/include/file
    ${CMAKE_CURRENT_SOURCE_DIR}/include/model
    ${CMAKE_CURRENT_SOURCE_DIR}/include/thread
    ${CMAKE_CURRENT_SOURCE_DIR}/include/communication/message_pump
    ${PCL_INCLUDE_DIRS}
    ${YAML_CPP_INCLUDE_DIRS}
    ${YAML_INCLUDE_DIRS}                 # libyaml 头文件
)

# Source files
file(GLOB_RECURSE SOURCES
    ${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/algorithm/*.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/file/*.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/config/*.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/logger/*.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/model/*.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/platform/*.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/communication/message_pump/*.cpp
)

add_library(${PROJECT_NAME}_lib SHARED ${SOURCES})
target_link_libraries(${PROJECT_NAME}_lib
    ${PCL_LIBRARIES}
    ${YAML_CPP_LIBRARIES}
    ${YAML_LIBRARIES} # 链接 libyaml
)

if(USE_LOG4CPLUS)
    target_link_libraries(${PROJECT_NAME}_lib log4cplus)
endif()