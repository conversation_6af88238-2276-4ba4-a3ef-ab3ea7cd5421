

#include "pointcloud_prejection.h"
#include<opencv2/opencv.hpp>

using namespace perception;

Reprejection::Reprejection(){}

void Reprejection::Init(Eigen::Vector4d K,Eigen::VectorXd dist,Eigen::VectorXd t_lidar_camera){


    std::cout<<" K :\n"<<K<<"\n lidar_camer:\n"<<t_lidar_camera<<std::endl;

    Eigen::Quaterniond quaternion(t_lidar_camera[6],t_lidar_camera[3],t_lidar_camera[4],t_lidar_camera[5]);
    
    r_l2c_=quaternion.toRotationMatrix().transpose();
    t_l2c_= -(r_l2c_*t_lidar_camera.topRows(3));     // t_lidar_camera    x y z 

    K_ << K[0],0,K[2],
          0,K[1],K[3],
          0,   0,   1;

    dist_=dist;

}
Reprejection::~Reprejection(){

}

bool Reprejection::Preject(Eigen::Vector3d point3d,Eigen::Vector2d& image_pt){
    if(mask_.timeout()){
        return false;
    }
    Eigen::Vector3d point_camera = r_l2c_ * point3d +t_l2c_;
    
    if (point_camera[2] <=0.0){
        return false;
    }
 
    Eigen::Vector3d pixel_pt = K_ * point_camera;
    if( pixel_pt[2]<=0 ){
        return false;
    }
    image_pt[0] = pixel_pt[0] / pixel_pt[2] ;
    image_pt[1] = pixel_pt[1] / pixel_pt[2] ;
    
    if(image_pt[0]<0 || image_pt[0]>=mask_().cols || image_pt[1]<0 || image_pt[1]>=mask_().rows){
     
        return false;
    }
    //printf(" plot  image_pt[0],image_pt[1] : %f  %f ....\n",image_pt[0],image_pt[1]);
    return true;

}

void Reprejection::ResetMask(cv::Mat mask,float timeout){
    if(mask.cols ==0 || mask.rows == 0){
        return;
    }
    mask_=mask.clone();
    mask_.set_timeout(timeout);

}

cv::Mat Reprejection::DebugImg(PointCloudT::Ptr cloud){

    if(!mask_.timeout()){
        cv::Mat debug_img = mask_().clone()*80;
        cv::cvtColor(debug_img, debug_img, cv::COLOR_GRAY2BGR);

        Eigen::Vector2d image_pt;
        for (auto pt : cloud->points){
            if(Preject(Eigen::Vector3d(pt.x,pt.y,pt.z),image_pt)){
                
                if( PixelType(image_pt) !=0){
                    cv::circle(debug_img, cv::Point(image_pt[0],image_pt[1]), 1, cv::Scalar(0, 0, 255), -1);
                }else{
                    cv::circle(debug_img, cv::Point(image_pt[0],image_pt[1]), 1, cv::Scalar(0, 255, 0), -1);
                }
            }
        }
        return debug_img;

    }else{
        return cv::Mat(cv::Size(480,640), CV_8UC3, cv::Scalar(0, 0, 0));
    }
}


bool Reprejection::Timeout(){
    return mask_.timeout();
}


int Reprejection::PixelType(Eigen::Vector2d pixel){
    int row=int(pixel[1]);
    int col=int(pixel[0]);
    cv::Mat mask=mask_.get();
    if(mask.cols >col && mask.rows>row && row>=0 && col>=0){
        return mask.at<uchar>(row,col);
    }
    return 0;
}