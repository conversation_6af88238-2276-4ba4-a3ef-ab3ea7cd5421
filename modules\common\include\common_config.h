#ifndef _COMMON_CONFIG_H_
#define _COMMON_CONFIG_H_

#include <memory>
#include <string>
#include <vector>

#include "cx_types.h"
namespace common_lib {
struct CommonConfigInfo {
  // sensor topic
  cx_string version_;                  // version
  cx_string color_image_topic_;        // 彩色相机图像话题
  cx_string color_camera_info_topic_;  // 彩色相机内参话题
  cx_string depth_image_topic_;        // 深度相机图像话题
  cx_string depth_camera_info_topic_;  // 深度相机内参话题
  cx_string depth_to_color_topic_;     // 深度相机到彩色相机外参变换话题
  cx_string imu_topic_;                // IMU topic name
  cx_string gnss_topic_;               // GNSS topic name
  cx_string lidar_topic_;              // LiDAR topic name

  // node output topic
  cx_string perception_output_topic_;  // perception节点输出结果话题

  // transform
  std::vector<cx_double>
      lidar2imu_extrinsic_t_;  // 雷达相对于IMU的外参T（即雷达在IMU坐标系中的坐标）
  std::vector<cx_double> lidar2imu_extrinsic_r_;  // 雷达相对于IMU的外参R
  std::vector<cx_double> rtk2Lidar_t_;       // rtk相对于雷达的外参T（即rtk在Lidar坐标系中的坐标）
  std::vector<cx_double> color_to_lidar_q_;  // color相机在雷达坐标系下的朝向(四元素 w x y z)
  std::vector<cx_double> color_to_lidar_t_;  // color相机在雷达坐标系下的平移

  // 默认构造函数
  CommonConfigInfo() { clear(); }

  // 完整参数的构造函数
  CommonConfigInfo(const cx_string &ver, const cx_string &color_img_t,
                   const cx_string &color_info_t, const cx_string &depth_img_t,
                   const cx_string &depth_info_t, const cx_string &depth_to_color_t,
                   const cx_string &imu_t, const cx_string &gnss_t, const cx_string &lidar_t,
                   const cx_string &perception_t, const std::vector<cx_double> &ext_t,
                   const std::vector<cx_double> &ext_r, const std::vector<cx_double> &rtk_t,
                   const std::vector<cx_double> &color_q, const std::vector<cx_double> &color_t)
      : version_(ver)
      , color_image_topic_(color_img_t)
      , color_camera_info_topic_(color_info_t)
      , depth_image_topic_(depth_img_t)
      , depth_camera_info_topic_(depth_info_t)
      , depth_to_color_topic_(depth_to_color_t)
      , imu_topic_(imu_t)
      , gnss_topic_(gnss_t)
      , lidar_topic_(lidar_t)
      , perception_output_topic_(perception_t)
      , lidar2imu_extrinsic_t_(ext_t)
      , lidar2imu_extrinsic_r_(ext_r)
      , rtk2Lidar_t_(rtk_t)
      , color_to_lidar_q_(color_q)
      , color_to_lidar_t_(color_t) {}

  // 清空所有成员
  void clear() {
    version_ = "v1.0";
    color_image_topic_ = "/camera/color/image_raw";
    color_camera_info_topic_ = "/camera/color/camera_info";
    depth_image_topic_ = "/camera/depth/image_rect_raw";
    depth_camera_info_topic_ = "/camera/depth/camera_info";
    depth_to_color_topic_ = "/camera/extrinsics/depth_to_color";
    imu_topic_ = "/imu";
    gnss_topic_ = "/chattgps";
    lidar_topic_ = "/lslidar_point_cloud";
    perception_output_topic_ = "/perception/terrain_pointcloud";
    lidar2imu_extrinsic_t_.clear();
    lidar2imu_extrinsic_r_.clear();
    rtk2Lidar_t_.clear();
    color_to_lidar_q_.clear();
    color_to_lidar_t_.clear();
  }
};

class CommonConfig {
 public:
  static void GetCommonConfig(CommonConfigInfo &common_config_info);

 private:
  static bool InitCommonConfig();
};
}  // namespace common_lib
#endif