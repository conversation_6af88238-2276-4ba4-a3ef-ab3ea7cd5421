
#include "perception_impl.h"
#include "config.h"

namespace perception{

    PerceptionImpl::PerceptionImpl(const std::string &config_file_path): 
            output_callback_(nullptr){

 
        //Config::GetInstance().Init("/home/<USER>/modules/perception/config/perception.yaml");
    }
    PerceptionImpl::~PerceptionImpl(){
    }

    void PerceptionImpl::SetOutputCallback(PerceptionOutputCallback callback){
        output_callback_=callback;
    }
    


}
