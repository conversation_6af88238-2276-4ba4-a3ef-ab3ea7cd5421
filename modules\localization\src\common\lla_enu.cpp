#include "lla_enu.h"

LLAENU::LLAENU() {
    std::cout << "  -- <PERSON><PERSON>ENU Construct --  \n";
}

LLAENU::LLAENU(double longitude, double latitude, double altitude)
    : longitude_(longitude), latitude_(latitude), altitude_(altitude) {
    Initialize();
    std::cout << "  -- LLAENU Construct --  \n";
    printf("     longitude_: %3.7lf; longitude_: %3.7lf; altitude_: %3.3lf; \n", 
        RadToDeg(longitude_), RadToDeg(latitude_), altitude_);
}

void LLAENU::Initialize() {
    local_Rn_ = Re_ * (1. + f_ * std::sin(latitude_) * std::sin(latitude_));
    local_Rm_ = Re_ * (1. - 2. * f_ + 3. * f_ * std::sin(latitude_) * std::sin(latitude_));
    ratio_x_ = (local_Rn_ + altitude_) * std::cos(latitude_);
    ratio_y_ = local_Rm_ + altitude_;
    inv_ratio_x_ = 1. / ratio_x_;
    inv_ratio_y_ = 1. / ratio_y_;
    origin_lla_initialized_ = true;
}

void LLAENU::SetOriginLLA(double longitude, double latitude, double altitude) {
    longitude_ = longitude;
    latitude_ = latitude;
    altitude_ = altitude;
    Initialize();
}

double LLAENU::RadToDeg(double rad) {
    return rad / M_PI * 180.0;
}

Eigen::Vector3d LLAENU::LLAToENU(double longitude, double latitude, double altitude) {
    return Eigen::Vector3d{
        (longitude - longitude_) * ratio_x_,
        (latitude - latitude_) * ratio_y_,
        altitude - altitude_
    };
}

Eigen::Vector3d LLAENU::ENUToLLA(double local_e, double local_n, double local_u) {
    return Eigen::Vector3d{
        longitude_ + local_e * inv_ratio_x_,
        latitude_ + local_n * inv_ratio_y_,
        altitude_ + local_u
    };
}