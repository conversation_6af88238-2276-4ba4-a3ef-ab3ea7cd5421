﻿#ifndef _CX_STRING_H_
#define _CX_STRING_H_

#include <memory>

#include "cx_pubhead.h"
namespace common_lib {
// 字符串分割
cx_int StringSplit(std::vector<cx_string> &dst, const cx_string &src, const cx_string &separator);

// 去掉前后空格
cx_string &StringTrim(cx_string &str);

string Vec2String(string split, vector<cx_int64> &idvt);

template <typename... Args>
string string_format(const std::string &format, Args... args) {
  size_t size = snprintf(nullptr, 0, format.c_str(), args...) + 1;  // Extra space for '\0'
  unique_ptr<char[]> buf(new char[size]);
  snprintf(buf.get(), size, format.c_str(), args...);
  return string(buf.get(), buf.get() + size - 1);  // We don't want the '\0' inside
}

cx_string ws2s(const cx_wstring &ws);
cx_wstring s2ws(const cx_string &s);
}  // namespace common_lib
#endif  // _CX_STRING_H_
