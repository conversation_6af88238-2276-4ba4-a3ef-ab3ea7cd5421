#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <memory>
#include "publisher_base.h"

namespace communication::ros2
{

    template <typename T, typename T_MSG>
    class PublisherBaseRos2 : public PublisherBase<T>
    {
    public:
        PublisherBaseRos2(rclcpp::Node::SharedPtr node,
                          const std::string &topic,
                          size_t max_buffer_size = 10)
            : PublisherBase<T>(topic, max_buffer_size),
              node_(node)
        {
            rclcpp::QoS qos(max_buffer_size);
            publisher_ = node_->create_publisher<T_MSG>(topic, qos);
        }

        virtual ~PublisherBaseRos2()
        {
            std::lock_guard<std::mutex> lock(pub_mutex_);
            if (publisher_)
            {
                publisher_.reset();

                std::this_thread::sleep_for(std::chrono::milliseconds(50)); // 给DDS时间释放资源
            }
        }

    protected:
        virtual void PublishMsg() override
        {
            std::lock_guard<std::mutex> lock(pub_mutex_);
            if (publisher_)
            {
                publisher_->publish(msg_);
            }
        }

        virtual int GetSubscriberCount() override
        {
            std::lock_guard<std::mutex> lock(pub_mutex_);
            if (publisher_)
            {
                return publisher_->get_subscription_count();
            }
        }

        rclcpp::Node::SharedPtr node_;
        typename rclcpp::Publisher<T_MSG>::SharedPtr publisher_;
        T_MSG msg_;
        std::mutex pub_mutex_;
    };

} // namespace communication::ros2

#endif
