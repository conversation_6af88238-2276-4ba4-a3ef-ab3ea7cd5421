#include "image_segment_cuda.h"
#include <fstream>
#include <vector>
#include "image_segment/yolov8_cuda/cuda_utils.h"
#include "image_segment/yolov8_cuda/preprocess.h"
#include "image_segment/yolov8_cuda/postprocess.h"
using namespace perception;

ImageSegmentCuda::ImageSegmentCuda():
    ImageSegmentImpl(){
        
}
ImageSegmentCuda::~ImageSegmentCuda(){
    release();
}
bool ImageSegmentCuda::Init(const std::string& model_path){

    std::ifstream file(model_path, std::ios::binary);
    if (!file.good()) {
        std::cerr << "read " << model_path << " error!" << std::endl;
        return false;
    }
    size_t size = 0;
    file.seekg(0, file.end);
    size = file.tellg();
    file.seekg(0, file.beg);
    char* serialized_engine = new char[size];
    assert(serialized_engine);
    file.read(serialized_engine, size);
    file.close();

    runtime_ = nvinfer1::createInferRuntime(logger_);
    assert(runtime_);
    engine_ = (runtime_)->deserializeCudaEngine(serialized_engine, size);
    assert(engine_);
    context_ = engine_->createExecutionContext();
    assert(context_);
    delete[] serialized_engine;

    CUDA_CHECK(cudaStreamCreate(&stream_));
    cuda_preprocess_init(kMaxInputImageSize);
    auto out_dims = engine_->getBindingDimensions(1);
    model_bboxes_ = out_dims.d[0];

    prepare_buffer();

    return true;
}
bool ImageSegmentCuda::Inference(cv::Mat src,float conf_thresh,float nms_thresh,SegmentDetectResult& out) {

    cuda_preprocess(src.ptr(), src.cols, src.rows, device_buffers_[0], kInputW, kInputH, stream_);

    auto start = std::chrono::system_clock::now();
    context_->enqueue(kBatchSize, (void**)device_buffers_, stream_, nullptr);
  

    //std::cout << "kOutputSize:" << kOutputSize << std::endl;
    CUDA_CHECK(cudaMemcpyAsync(output_buffer_host_, device_buffers_[1], kBatchSize * kOutputSize * sizeof(float), cudaMemcpyDeviceToHost,stream_));
    //std::cout << "kOutputSegSize:" << kOutputSegSize << std::endl;
    CUDA_CHECK(cudaMemcpyAsync(output_seg_buffer_host_, device_buffers_[2], kBatchSize * kOutputSegSize * sizeof(float),cudaMemcpyDeviceToHost, stream_));

    auto end = std::chrono::system_clock::now();
    std::cout << "inference time: " << std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count()
                  << "ms" << std::endl;
    

    CUDA_CHECK(cudaStreamSynchronize(stream_));
    //std::vector<Detection> res;

    //nms(res, output_buffer_host_, conf_thresh, nms_thresh);

    //auto masks = process_mask(output_seg_buffer_host_, kOutputSegSize, res);
    
    //out.mask=masks;
    return true;
}   


void ImageSegmentCuda::prepare_buffer() {
    assert(engine_->getNbBindings() == 3);
    // In order to bind the buffers, we need to know the names of the input and output tensors.
    // Note that indices are guaranteed to be less than IEngine::getNbBindings()
    const int inputIndex = engine_->getBindingIndex(kInputTensorName);
    const int outputIndex = engine_->getBindingIndex(kOutputTensorName);
    const int outputIndex_seg = engine_->getBindingIndex("proto");

    assert(inputIndex == 0);
    assert(outputIndex == 1);
    assert(outputIndex_seg == 2);
    // Create GPU buffers on device
    CUDA_CHECK(cudaMalloc((void**)(&device_buffers_[0]), kBatchSize * 3 * kInputH * kInputW * sizeof(float)));
    CUDA_CHECK(cudaMalloc((void**)(&device_buffers_[1]), kBatchSize * kOutputSize * sizeof(float)));
    CUDA_CHECK(cudaMalloc((void**)(&device_buffers_[2]), kBatchSize * kOutputSegSize * sizeof(float)));

    
    output_buffer_host_ = new float[kBatchSize * kOutputSize];
    output_seg_buffer_host_ = new float[kBatchSize * kOutputSegSize];
    
}

void ImageSegmentCuda::release(){
    cudaStreamDestroy(stream_);
    CUDA_CHECK(cudaFree(device_buffers_[0]));
    CUDA_CHECK(cudaFree(device_buffers_[1]));
    CUDA_CHECK(cudaFree(device_buffers_[2]));
    delete[] output_buffer_host_;
    delete[] output_seg_buffer_host_;
    cuda_preprocess_destroy();

    delete context_;
    delete engine_;
    delete runtime_;

}