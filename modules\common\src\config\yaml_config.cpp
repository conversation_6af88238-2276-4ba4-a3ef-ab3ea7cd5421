#include "yaml_config.h"
#include "yaml_config_impl.h"

namespace common_lib {

YamlConfig& YamlConfig::GetInstance() {
    static YamlConfig instance;
    return instance;
}

YamlConfig::YamlConfig() {
    memset(&document_, 0, sizeof(document_));
}

bool YamlConfig::LoadFile(const std::string& filepath) {
    FILE* file = fopen(filepath.c_str(), "r");
    if (!file) {
        return false;
    }

    yaml_parser_t parser;
    if (!yaml_parser_initialize(&parser)) {
        fclose(file);
        return false;
    }

    yaml_parser_set_input_file(&parser, file);
    if (!yaml_parser_load(&parser, &document_)) {
        yaml_parser_delete(&parser);
        fclose(file);
        return false;
    }

    config_data_.clear();

    yaml_node_t* root_node = yaml_document_get_root_node(&document_);
    if (root_node) {
        ParseNode(root_node, "");
    }

    yaml_parser_delete(&parser);
    fclose(file);
    return true;
}

void YamlConfig::ParseNode(const yaml_node_t* node, const std::string& parent_path) {
    if (node->type == YAML_SCALAR_NODE) {
        if (!parent_path.empty()) {
            config_data_[parent_path] = reinterpret_cast<const char*>(node->data.scalar.value);
        }
    } else if (node->type == YAML_MAPPING_NODE) {
        for (yaml_node_pair_t* pair = node->data.mapping.pairs.start;
             pair < node->data.mapping.pairs.top; pair++) {
            yaml_node_t* key_node = yaml_document_get_node(&document_, pair->key);
            yaml_node_t* value_node = yaml_document_get_node(&document_, pair->value);

            std::string key_path = parent_path;
            if (key_node->type == YAML_SCALAR_NODE) {
                std::string key_name = reinterpret_cast<const char*>(key_node->data.scalar.value);
                if (!key_path.empty()) {
                    key_path += "." + key_name;
                } else {
                    key_path = key_name;
                }
            }

            ParseNode(value_node, key_path);
        }
    } else if (node->type == YAML_SEQUENCE_NODE) {
        std::string sequence_values;
        for (yaml_node_item_t* item = node->data.sequence.items.start;
             item < node->data.sequence.items.top; item++) {
            yaml_node_t* item_node = yaml_document_get_node(&document_, *item);
            if (item_node->type == YAML_SCALAR_NODE) {
                if (!sequence_values.empty()) {
                    sequence_values += " ";
                }
                sequence_values += reinterpret_cast<const char*>(item_node->data.scalar.value);
            }
        }
        if (!parent_path.empty()) {
            config_data_[parent_path] = sequence_values;
        }
    }
}

std::string YamlConfig::ConvertToNestedPath(const std::string& path) const {
    std::string result;
    for (size_t i = 0; i < path.size(); ++i) {
        if (path[i] == '/') {
            result += '.';
        } else {
            result += path[i];
        }
    }
    return result;
}

}  // namespace common_lib