#ifndef _CX_THREADPOOL_H_
#define _CX_THREADPOOL_H_

#include "cx_event.h"
#include "cx_pubhead.h"
#include "cx_thread.h"
#include "cx_work.h"

#ifdef WIN32
#include "windows.h"
#endif  // WIN32
namespace common_lib {
class CXThreadPool {
 public:
  CXThreadPool();
  virtual ~CXThreadPool();

 public:
  cx_int Create();
  cx_int Close();

 public:
  cx_int SetThreadMinimum(cx_dword min);
  cx_int SetThreadMaximum(cx_dword max);

 public:
  CXThread *CreateThread();

#ifdef WIN32

 public:
  static VOID NTAPI WorkThread(PTP_CALLBACK_INSTANCE Instance, PVOID Context);

#else
 public:
  static void *WorkThread(void *const context_ptr);

#endif

 public:
  cx_int SubmitThread(CXThread *thread_ptr);

  CXThread *GetIdleThread();
  cx_int ExecuteTask(CXWork *const work_ptr, CXThread *&return_thread_ptr);

 public:
  cx_bool IsTerminate();
  cx_int DecreateThreads(const cx_dword count);
  cx_bool ThreadFinishWork(CXThread *const thread_ptr);
  cx_int ReleaseThread(CXThread *const thread_ptr);

 private:
#ifdef WIN32
  PTP_POOL m_pThreadPool;
  TP_CALLBACK_ENVIRON m_cbEnv;
  PTP_CLEANUP_GROUP m_pCleanupGroup;
#else
#endif  // WIN32

  CMThreadSet idle_threads_;
  CMThreadSet alive_threads_;
  CMThreadSet killed_threads_;

  cx_dword thread_minimum_;
  cx_dword thread_maximum_;

  static cx_bool g_initialized_thread_pool;

  CXEvent terminate_event_;
};

CXThreadPool *GetSingleton4ThreadPool();
}  // namespace common_lib
#endif  // _CX_THREADPOOL_H_
