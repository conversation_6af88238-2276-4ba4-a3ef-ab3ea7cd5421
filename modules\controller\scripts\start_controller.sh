#!/bin/bash

# Controller Module Startup Script
# 用于启动calibration_node和velocity_smoother_node

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONTROLLER_DIR="$(dirname "$SCRIPT_DIR")"

# 配置文件路径
CONFIG_DIR="$CONTROLLER_DIR/config"
BIN_DIR="$CONTROLLER_DIR/bin"

# 检查可执行文件是否存在
if [ ! -f "$BIN_DIR/calibration_node" ]; then
    echo "错误: calibration_node 不存在于 $BIN_DIR"
    exit 1
fi

if [ ! -f "$BIN_DIR/velocity_smoother_node" ]; then
    echo "错误: velocity_smoother_node 不存在于 $BIN_DIR"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "$CONFIG_DIR/calibration.yaml" ]; then
    echo "错误: calibration.yaml 不存在于 $CONFIG_DIR"
    exit 1
fi

if [ ! -f "$CONFIG_DIR/velocity_smoother_config.yaml" ]; then
    echo "错误: velocity_smoother_config.yaml 不存在于 $CONFIG_DIR"
    exit 1
fi

# 设置环境变量
export LD_LIBRARY_PATH="$CONTROLLER_DIR/lib:$CONTROLLER_DIR/../common/lib:$CONTROLLER_DIR/../communication/lib:$LD_LIBRARY_PATH"

echo "=== 启动 Controller 模块 ==="
echo "配置文件目录: $CONFIG_DIR"
echo "可执行文件目录: $BIN_DIR"

# 启动calibration_node
echo "启动 calibration_node..."
cd "$BIN_DIR"
./calibration_node "$CONFIG_DIR/calibration.yaml" &
CALIBRATION_PID=$!

# 等待一下确保calibration_node启动
sleep 2

# 启动velocity_smoother_node
echo "启动 velocity_smoother_node..."
cd "$BIN_DIR"
./velocity_smoother_node "$CONFIG_DIR/velocity_smoother_config.yaml" &
SMOOTHER_PID=$!

echo "所有节点已启动"
echo "calibration_node PID: $CALIBRATION_PID"
echo "velocity_smoother_node PID: $SMOOTHER_PID"

# 等待用户中断
echo "按 Ctrl+C 停止所有节点"
trap 'echo "正在停止节点..."; kill $CALIBRATION_PID $SMOOTHER_PID 2>/dev/null; exit 0' INT

# 等待进程结束
wait 