#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>

#include "ros2/core/subscriber_base_ros2.h"
#include "data_types/pose_data.h"

namespace communication::ros2
{

    class PoseDataSubscriberRos2 : public SubscriberBaseRos2<PoseData, geometry_msgs::msg::PoseStamped>
    {
    public:
        PoseDataSubscriberRos2(rclcpp::Node::SharedPtr node,
                               const std::string &topic,
                               //    typename SubscriberBaseRos2<PoseData, geometry_msgs::msg::PoseStamped>::CallbackType callback = nullptr,
                               size_t max_buffer_size = 10)
            : SubscriberBaseRos2<PoseData, geometry_msgs::msg::PoseStamped>(node, topic, max_buffer_size)
        {
        }

    protected:
        virtual void FromMsg(const geometry_msgs::msg::PoseStamped &msg, PoseData &data) override
        {
            data.time = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9;
            // data.frame_id = msg.header.frame_id;
            data.position[0] = msg.pose.position.x;
            data.position[1] = msg.pose.position.y;
            data.position[2] = msg.pose.position.z;
            data.orientation[0] = msg.pose.orientation.x;
            data.orientation[1] = msg.pose.orientation.y;
            data.orientation[2] = msg.pose.orientation.z;
            data.orientation[3] = msg.pose.orientation.w;
        }
    };

} // namespace communication::ros2

#endif
