#pragma once

#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include <iomanip>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <eigen3/Eigen/Dense>
#include "pid_controller.hpp"

#include <pcl/common/time.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/io/pcd_io.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/registration/icp.h>

#define PI 3.1415926535

namespace calibration {

// 时间戳结构
struct TimeStamp {
    double sec;
    TimeStamp() : sec(0.0) {}
    TimeStamp(double s) : sec(s) {}
    static TimeStamp now() {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        double seconds = std::chrono::duration<double>(duration).count();
        return TimeStamp(seconds);
    }
    double toSec() const { return sec; }
};

// 坐标点与姿态结构体
struct Point {
    double x, y, z;
    Point() : x(0.0), y(0.0), z(0.0) {}
    Point(double _x, double _y, double _z) : x(_x), y(_y), z(_z) {}
};

struct Quaternion {
    double x, y, z, w;
    Quaternion() : x(0.0), y(0.0), z(0.0), w(1.0) {}
    static Quaternion fromYaw(double yaw) {
        Quaternion q;
        q.w = cos(yaw * 0.5);
        q.z = sin(yaw * 0.5);
        return q;
    }
    double toYaw() const {
        return atan2(2.0 * (w * z), 1.0 - 2.0 * (z * z));
    }
};

struct Pose {
    Point position;
    Quaternion orientation;
    Pose() {}
    Pose(double x, double y, double z, double yaw)
        : position(x, y, z), orientation(Quaternion::fromYaw(yaw)) {}
};
/**
 * @brief 速度消息结构体，对应geometry_msgs/Twist
 * 包含线速度和角速度信息
 */


struct TwistMsg {
    double time;
    Eigen::Vector3d linear_vel;  // Linear velocity (x, y, z)
    Eigen::Vector3d angular_vel; // Angular velocity (roll, pitch, yaw)

    TwistMsg() : time(0.0) {
        linear_vel.setZero();
        angular_vel.setZero();
    }
};
/*
struct TwistMsg {
    Eigen::Vector3d linear;
    Eigen::Vector3d angular;
    TwistMsg() : linear(Eigen::Vector3d::Zero()), angular(Eigen::Vector3d::Zero()) {}
    TwistMsg(double linear_x, double angular_z) {
        linear = Eigen::Vector3d(linear_x, 0.0, 0.0);
        angular = Eigen::Vector3d(0.0, 0.0, angular_z);
    }
};
*/
struct Int8Msg {
    int8_t data;
    Int8Msg() : data(0) {}
    explicit Int8Msg(int value) : data(static_cast<int8_t>(value)) {}
};

struct BoolMsg {
    bool data;
    BoolMsg() : data(false) {}
    explicit BoolMsg(bool value) : data(value) {}
};

struct Odometry {
    struct Header { double toSec() const { return 0.0; } } header;
    struct Pose {
        struct Position { double x = 0.0, y = 0.0, z = 0.0; } position;
        struct Orientation {
            double x = 0.0, y = 0.0, z = 0.0, w = 1.0;
            double toYaw() const { return atan2(2.0 * (w * z), 1.0 - 2.0 * (z * z)); }
        } orientation;
    } pose;
};

struct PoseStamped {
    struct Pose {
        struct Position { double x = 0.0, y = 0.0, z = 0.0; } position;
        struct Orientation {
            double x = 0.0, y = 0.0, z = 0.0, w = 1.0;
            double toYaw() const { return atan2(2.0 * (w * z), 1.0 - 2.0 * (z * z)); }
        } orientation;
    } pose;
};

struct PointCloud2 {
    std::vector<uint8_t> data;
};

// 校准主类
class Calibration {
public:
    explicit Calibration(const std::string& name);
    ~Calibration();

    bool init();
    void start();
    void stop();

    void setPIDParameters(double p_yaw, double i_yaw, double d_yaw,
                          double p_x, double i_x, double d_x,
                          double p_y, double i_y, double d_y);

    void setErrorLimits(double errorYaw_max, double errorYaw_min, 
                       double errorX_max, double errorY_max);
    void setVelocityLimits(double X_max, double Y_max, double Yaw_max);
    void setPrecision(double set_yaw_precision, double set_x_precision, double set_y_precision);

    void inputOdometry(const Odometry& odom);
    void inputGoal(const PoseStamped& goal);
    void inputWebGoal(const PoseStamped& goal);
    void inputMode(const BoolMsg& mode);
    void inputTerrainCloud(const PointCloud2& cloud);

    void setSpeedPublishCallback(std::function<void(const std::shared_ptr<TwistMsg>&)> cb);
    void setStopPublishCallback(std::function<void(const std::shared_ptr<Int8Msg>&)> cb);
    void setInnerStopPublishCallback(std::function<void(const std::shared_ptr<Int8Msg>&)> cb);
    void setModePublishCallback(std::function<void(const std::shared_ptr<BoolMsg>&)> cb);

private:
    std::string name_;
    std::atomic<bool> is_running_;
    std::atomic<bool> is_initialized_;
    std::thread worker_thread_;
    std::mutex data_mutex_;

    std::function<void(const std::shared_ptr<TwistMsg>&)> speed_callback_;
    std::function<void(const std::shared_ptr<Int8Msg>&)> stop_callback_;
    std::function<void(const std::shared_ptr<Int8Msg>&)> inner_stop_callback_;
    std::function<void(const std::shared_ptr<BoolMsg>&)> mode_callback_;

    // 车辆状态
    float vehicleX = 0, vehicleY = 0, vehicleYaw = 0;
    double goalX = 0, goalY = 0, goalYaw = 0;
    int arrived = 1, nav_start = 0;

    // 控制精度与限制
    double set_x_precision = 0.3, set_y_precision = 0.3, set_yaw_precision = 0.3;
    double max_linear_velocity_ = 0.5, max_angular_velocity_ = 0.5;
    double errorYaw_max = 3.0, errorYaw_min = 1.0;
    double errorX_max = 3.0, errorY_max = 3.0;
    double X_max = 0.5, Y_max = 0.1, Yaw_max = 0.1;

    // PID 控制器
    std::unique_ptr<calibration::PIDController> pid_x;
    std::unique_ptr<calibration::PIDController> pid_y;
    std::unique_ptr<calibration::PIDController> pid_yaw;

    // 内部方法 
    void controlLoop();
    void goalHandler(const std::shared_ptr<const PoseStamped>& goal);
    void odomHandler(const std::shared_ptr<const Odometry>& odom);
    double normalizeAngle(double angle);
    void printStatus() const;
};

} // namespace calibration
