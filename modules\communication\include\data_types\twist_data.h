/**
 * @file twist_data.h
 * @brief This file contains the definition of the TwistData class, which stores linear and angular velocity data.
 */
#pragma once

#include <Eigen/Dense>
#include <deque>
#include <mutex>


namespace communication {

// Twist数据类，存储线速度和角速度数据
struct TwistData {
    double time;
    Eigen::Vector3d linear_vel;  // Linear velocity (x, y, z)
    Eigen::Vector3d angular_vel; // Angular velocity (roll, pitch, yaw)

    TwistData() : time(0.0) {
        linear_vel.setZero();
        angular_vel.setZero();
    }

};

}  // namespace communication
