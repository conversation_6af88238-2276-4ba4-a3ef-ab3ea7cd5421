#pragma once

#include "communication.h"
#include "imu_data.h"
#include "DataType.h"
#include "lla_enu.h"
#include "lidar_preprocess.h"
#include "zero_vel_detect.h"
#include "yaml_config.h"
#include "cx_work.h"
#include "cx_singleton.h"
#include "cx_thread.h"
using namespace common_lib;
class LocSubcribe: public CXWork
{
private:
    /* data */
public:
    LocSubcribe();
    ~LocSubcribe();
    
    void Init();
    void InitGnss();
    void Start();
    void StartCommunication();

    // Callback functions for data processing
    void ProcessImuData(communication::IMUData& imu_data);
    void ProcessGnssData(const communication::GNSSData& gnss_data);
    void ProcessLidarData(const communication::CloudXYZRIData& lidar_data);
    void ProcessPoseData(const communication::PoseData& pose_data);
    void ProcessWebPos(const communication::PoseData& pose_data);
    bool SyncData(MeasureGroup& meas);
    bool TryGetOneLidarFrame(MeasureGroup& meas);

    void PublishDSGlobalMap(const communication::CloudNormalData::CLOUD_NormalType_PTR& DSpointcloudmap);
    void Publish_frame_world(const communication::CloudNormalData::CLOUD_NormalType_PTR& pointcloudWorld);
    void Publish_frame_body(const communication::CloudNormalData::CLOUD_NormalType_PTR& pointcloudBody);
    void Publish_LocalMap(const communication::CloudNormalData::CLOUD_NormalType_PTR& local_map);
    void PublishOdometry(const communication::PoseVelData& odometry_data);
    void Publish_TFData(const communication::PoseData& tf_data);
    void Publish_Path(const communication::PathData& path_data);
    void PublishGnssPath();

    communication::CloudData CloudNormalDataToCloudData(const communication::CloudNormalData::CLOUD_NormalType_PTR& cloud_normal_data);

    bool IsNeedReloc();
    bool IsGnssDataReady();
    ManualPos GetManualPos();
    GnssENU GetGNSSPoseData();
    PointCloudXYZI::Ptr GetProcessLidarData();

private:
#ifdef WIN32
    static void MainLoop(PTP_CALLBACK_INSTANCE Instance, PVOID pContext);
#else
    static void* MainLoop(void *pContext);
#endif

private:
    cx_int Run();
    void UpdateIMUData();
    void UpdateGnssData();
    void UpdateLidarData();
    void UpdateManualPos();
    void UpdateWebPos();

private:
    CXThread* m_pThread{nullptr};
    // Communication object for subscribing to data
    std::shared_ptr<communication::Communication> m_ptr_communication_;

    // Subscribers for IMU, GNSS, and LiDAR data
    std::shared_ptr<communication::ImuDataSubscriberBase> m_ptr_imu_subscriber_;
    std::shared_ptr<communication::GnssDataSubscriberBase> m_ptr_gnss_subscriber_;
    std::shared_ptr<communication::LidarDataSubscriberBase> m_ptr_lidar_subscriber_;
    std::shared_ptr<communication::PoseDataSubscriberBase> m_ptr_manaul_pose_data_subscriber;
    std::shared_ptr<communication::PoseDataSubscriberBase> m_ptr_web_pose_data_subscriber;

    std::shared_ptr<communication::CloudDataPublisherBase> m_local_map_publisher_ptr;
    std::shared_ptr<communication::CloudDataPublisherBase> m_pcloud_data_publisher_ptr;
    std::shared_ptr<communication::CloudDataPublisherBase> m_pcloud_world_publisher_ptr;
    std::shared_ptr<communication::CloudDataPublisherBase> m_pcloud_body_publisher_ptr;
    std::shared_ptr<communication::OdometryPublisherBase> m_podometry_publisher_ptr;
    std::shared_ptr<communication::TFDataPublisherBase> m_tf_data_publisher_ptr;
    std::shared_ptr<communication::PathDataPublisherBase> m_path_data_publisher_ptr;
    std::shared_ptr<communication::PathDataPublisherBase> m_gnss_path_publisher_ptr;

    //imu
    bool m_bimu_vaild;
    double m_dimu_fault_time; // 
    double m_dlast_timestamp_imu;
    std::string m_simu_axis;
    std::deque<IMUData> m_deq_IMUData;
    std::deque<IMUData> m_deq_IMUData2RTK;

    //zero
    bool m_bzero_detect_enable;
    CZeroVelDetect zero_vel_detect; // 零速检测对象

    //gnss
    shared_ptr<LLAENU> m_ptr_gnss2enu;
    double m_dlast_timestamp_gnss;
    std::deque<GnssENU> m_deq_GnssData;
    communication::PathData m_path_data;

    //lidar
    std::deque<PointCloudXYZI::Ptr> m_lidar_buffer;
    PointCloudXYZI::Ptr m_process_lidar;
    std::deque<double> m_time_buffer;
    std::deque<double> m_time_end_buffer;
    bool m_first_lidar;
    double m_lidar_end_time;
    double m_last_timestamp_lidar;
    double m_lidar_mean_scantime;
    double m_last_time_packed;
    double m_package_end_time_last;
    bool m_initialized_last_time;
    double m_last_time;
    double m_time_interval;

    //preprocess
    shared_ptr<LidarPreprocess> m_LidarPreprocess;

    //reloc
    bool m_bflag_manualpos;
    bool m_bGnssDataReady;

    ManualPos m_manualpos;

    bool m_buse_rtk;
    std::mutex m_mtx_buffer; // Mutex for thread safety

    //Config& m_Config;
    common_lib::YamlConfig& m_Config;
};

LocSubcribe* GetSingleton4LocSubcribe();


