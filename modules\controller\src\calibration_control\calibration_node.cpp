#include "calibration.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <iomanip>
#include <yaml-cpp/yaml.h>
#include "communication.h"

using namespace calibration;
using namespace communication;

// 全局变量
std::unique_ptr<Calibration> calibration_;
std::shared_ptr<OdometrySubscriberBase> odometry_subscriber_ptr;
std::shared_ptr<PoseDataSubscriberBase> goal_subscriber_ptr;
std::shared_ptr<PoseDataSubscriberBase> web_goal_subscriber_ptr;
std::shared_ptr<BoolDataSubscriberBase> mode_subscriber_ptr;
std::shared_ptr<CloudDataSubscriberBase> terrain_cloud_subscriber_ptr;
std::shared_ptr<TwistDataPublisherBase> cmd_vel_publisher_ptr;
std::shared_ptr<Int8DataPublisherBase> stop_publisher_ptr;
std::shared_ptr<Int8DataPublisherBase> inner_stop_publisher_ptr;
std::shared_ptr<BoolDataPublisherBase> mode_publisher_ptr;

// 全局变量用于控制程序运行
static bool g_running = true;

// 信号处理函数
void signalHandler(int signum) {
    std::cout << "\n🛑 接收到终止信号 " << signum << std::endl;
    g_running = false;
}

// 数据接收线程
void DataReceiveThread() {
    while (g_running) {
        // 处理里程计数据
        if (!odometry_subscriber_ptr->IsBufferEmpty()) {
            auto odometry_data = odometry_subscriber_ptr->GetBuffer();
            Odometry msg;
            msg.pose.position.x = odometry_data.front().pose_data.position[0];
            msg.pose.position.y = odometry_data.front().pose_data.position[1];
            msg.pose.position.z = odometry_data.front().pose_data.position[2];
            msg.pose.orientation.x = odometry_data.front().pose_data.orientation[0];
            msg.pose.orientation.y = odometry_data.front().pose_data.orientation[1];
            msg.pose.orientation.z = odometry_data.front().pose_data.orientation[2];
            msg.pose.orientation.w = odometry_data.front().pose_data.orientation[3];
            calibration_->inputOdometry(msg);
        }

        // 处理目标位姿数据
        if (!goal_subscriber_ptr->IsBufferEmpty()) {
            auto goal_data = goal_subscriber_ptr->GetBuffer();
            PoseStamped msg;
            msg.pose.position.x = goal_data.front().position[0];
            msg.pose.position.y = goal_data.front().position[1];
            msg.pose.position.z = goal_data.front().position[2];
            msg.pose.orientation.x = goal_data.front().orientation[0];
            msg.pose.orientation.y = goal_data.front().orientation[1];
            msg.pose.orientation.z = goal_data.front().orientation[2];
            msg.pose.orientation.w = goal_data.front().orientation[3];
            calibration_->inputGoal(msg);
        }

        // 处理Web目标位姿数据
        if (!web_goal_subscriber_ptr->IsBufferEmpty()) {
            auto web_goal_data = web_goal_subscriber_ptr->GetBuffer();
            PoseStamped msg;
            msg.pose.position.x = web_goal_data.front().position[0];
            msg.pose.position.y = web_goal_data.front().position[1];
            msg.pose.position.z = web_goal_data.front().position[2];
            msg.pose.orientation.x = web_goal_data.front().orientation[0];
            msg.pose.orientation.y = web_goal_data.front().orientation[1];
            msg.pose.orientation.z = web_goal_data.front().orientation[2];
            msg.pose.orientation.w = web_goal_data.front().orientation[3];
            calibration_->inputWebGoal(msg);
        }

        // 处理模式数据
        if (!mode_subscriber_ptr->IsBufferEmpty()) {
            auto mode_data = mode_subscriber_ptr->GetBuffer();
            BoolMsg msg;
            msg.data = mode_data.front();
            calibration_->inputMode(msg);
        }
        /*
        // 处理地形点云数据
        if (!terrain_cloud_subscriber_ptr->IsBufferEmpty()) {
            auto cloud_data = terrain_cloud_subscriber_ptr->GetBuffer();
            PointCloud2 msg;
            msg.data = cloud_data.front().data;
            calibration_->inputTerrainCloud(msg);
        }
       */
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

/**
 * @brief 校准节点 - 基础节点创建和初始化
 */
int main(int argc, char** argv) {
    std::cout << "=== Calibration Node ===" << std::endl;
    // 解析calibration.yaml配置文件
    YAML::Node config;
    try {
        config = YAML::LoadFile("../config/calibration.yaml");
    } catch (const YAML::Exception& e) {
        std::cerr << "❌ 无法加载calibration.yaml配置文件: " << e.what() << std::endl;
        return -1;
    }

    // 获取PID参数
    double p_vel_yaw = config["p_vel_yaw"].as<double>();
    double i_vel_yaw = config["i_vel_yaw"].as<double>();
    double d_vel_yaw = config["d_vel_yaw"].as<double>();
    double p_vel_x = config["p_vel_x"].as<double>();
    double i_vel_x = config["i_vel_x"].as<double>();
    double d_vel_x = config["d_vel_x"].as<double>();
    double p_vel_y = config["p_vel_y"].as<double>();
    double i_vel_y = config["i_vel_y"].as<double>();
    double d_vel_y = config["d_vel_y"].as<double>();

    // 获取误差限幅参数
    double errorYaw_max = config["errorYaw_max"].as<double>();
    double errorYaw_min = config["errorYaw_min"].as<double>();
    double errorX_max = config["errorX_max"].as<double>();
    double errorY_max = config["errorY_max"].as<double>();

    // 获取速度限幅参数
    double Yaw_max = config["Yaw_max"].as<double>();
    double X_max = config["X_max"].as<double>();
    double Y_max = config["Y_max"].as<double>();

    // 获取精度参数
    double set_yaw_precision = config["set_yaw_precision"].as<double>();
    double set_x_precision = config["set_x_precision"].as<double>();
    double set_y_precision = config["set_y_precision"].as<double>();
    
    std::cout << "p_vel_yaw: " << p_vel_yaw << std::endl;
    std::cout << "i_vel_yaw: " << i_vel_yaw << std::endl;
    std::cout << "d_vel_yaw: " << d_vel_yaw << std::endl;
    std::cout << "p_vel_x: " << p_vel_x << std::endl;
    std::cout << "i_vel_x: " << i_vel_x << std::endl;
    std::cout << "d_vel_x: " << d_vel_x << std::endl;
    std::cout << "p_vel_y: " << p_vel_y << std::endl;
  
    std::cout << "✅ 成功加载calibration.yaml配置文件" << std::endl;
    try {
      
    
        // 创建通信模块实例
        auto communication_ptr = std::make_shared<Communication>("calibration");
        if (!communication_ptr->Initialize("config/communication_config.yaml")) {
            std::cerr << "❌ 通信模块初始化失败" << std::endl;
            return -1;
        }

        // 创建校准控制器
        calibration_ = std::make_unique<Calibration>("calibration");
        
        // 在对象创建后设置PID参数
        calibration_->setPIDParameters(p_vel_yaw, i_vel_yaw, d_vel_yaw,
                                     p_vel_x, i_vel_x, d_vel_x,
                                     p_vel_y, i_vel_y, d_vel_y);

        // 设置误差限幅
        calibration_->setErrorLimits(errorYaw_max, errorYaw_min, errorX_max, errorY_max);

        // 设置速度限幅
        calibration_->setVelocityLimits(X_max, Y_max, Yaw_max);

        // 设置精度参数
        calibration_->setPrecision(set_yaw_precision, set_x_precision, set_y_precision);

        // 设置回调函数
        calibration_->setSpeedPublishCallback([](const std::shared_ptr<TwistMsg>& msg) {
            if (cmd_vel_publisher_ptr) {
                TwistData twist_data;
                twist_data.time = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count();
                twist_data.linear_vel = msg->linear_vel;
                twist_data.angular_vel = msg->angular_vel;
                cmd_vel_publisher_ptr->Publish(twist_data);
            }
        });

        calibration_->setStopPublishCallback([](const std::shared_ptr<Int8Msg>& msg) {
            if (stop_publisher_ptr) {
                Int8Data int8_data;
                int8_data.time = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count();
                int8_data.data = msg->data;
                stop_publisher_ptr->Publish(int8_data);
            }
        });

        calibration_->setInnerStopPublishCallback([](const std::shared_ptr<Int8Msg>& msg) {
            if (inner_stop_publisher_ptr) {
                Int8Data int8_data;
                int8_data.time = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count();
                int8_data.data = msg->data;
                inner_stop_publisher_ptr->Publish(int8_data);
            }
        });

        calibration_->setModePublishCallback([](const std::shared_ptr<BoolMsg>& msg) {
            if (mode_publisher_ptr) {
                bool bool_data = msg->data;
                mode_publisher_ptr->Publish(bool_data);
            }
        });

        // 创建订阅器和发布器
        odometry_subscriber_ptr = communication_ptr->CreateOdometrySubscriber("/odometry");
        goal_subscriber_ptr = communication_ptr->CreatePoseDataSubscriber("/goal");
        web_goal_subscriber_ptr = communication_ptr->CreatePoseDataSubscriber("/web_goal");
        mode_subscriber_ptr = communication_ptr->CreateBoolDataSubscriber("/mode");
        terrain_cloud_subscriber_ptr = communication_ptr->CreateCloudDataSubscriber("/terrain_cloud");
        cmd_vel_publisher_ptr = communication_ptr->CreateTwistDataPublisher("/cmd_vel", "map", 10);
        stop_publisher_ptr = communication_ptr->CreateInt8DataPublisher("/stop", 10);
        inner_stop_publisher_ptr = communication_ptr->CreateInt8DataPublisher("/inner_stop", 10);
        mode_publisher_ptr = communication_ptr->CreateBoolDataPublisher("/mode", 10);

        // 初始化校准控制器
        if (!calibration_->init()) {
            std::cerr << "❌ 校准控制器初始化失败" << std::endl;
            return -1;
        }

        // 启动数据接收线程
        std::thread data_receive_thread(DataReceiveThread);
        data_receive_thread.detach();

        // 启动校准控制器
        calibration_->start();

        // 运行通信模块
        communication_ptr->Run();

    } catch (const std::exception& e) {
        std::cerr << "❌ 程序执行出错: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
