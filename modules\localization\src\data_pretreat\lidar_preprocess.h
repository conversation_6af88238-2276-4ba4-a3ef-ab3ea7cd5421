#pragma once 

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <vector>
#include <Eigen/Dense>
#include "DataType.h"
#include "communication.h"
using namespace communication;
class LidarPreprocess
{
public:
    LidarPreprocess();
    ~LidarPreprocess();
    
    void process(const CloudXYZRIData &pcl_in, PointCloudXYZI::Ptr &pcl_out);
    void set(bool feat_en, int lid_type, double bld, int pfilt_num);
    double get_avg_range();

    PointCloudXYZI pl_full, pl_corn, pl_surf;
    PointCloudXYZI pl_buff[128]; //maximum 128 line lidar
    std::vector<orgtype> typess[128]; //maximum 128 line lidar
    float time_unit_scale;
    int lidar_type, point_filter_num, N_SCANS, SCAN_RATE, time_unit;
    double blind;
    bool feature_enabled, given_offset_time;

private:
    void rs_process(const CloudXYZRIData &pcl_in);
    void oust64_process(const CloudXYZRIData &pcl_in);
    void velodyne_process(const CloudXYZRIData &pcl_in);
    void give_feature(PointCloudXYZI &pl, std::vector<orgtype> &types);
    int plane_judge(const PointCloudXYZI &pl, std::vector<orgtype> &types, uint i, uint &i_nex, Eigen::Vector3d &curr_direct);
    bool edge_jump_judge(const PointCloudXYZI &pl, std::vector<orgtype> &types, uint i, Surround nor_dir);
    
    int group_size;
    double disA, disB, inf_bound;
    double limit_maxmid, limit_midmin, limit_maxmin;
    double p2l_ratio;
    double jump_up_limit, jump_down_limit;
    double cos160;
    double edgea, edgeb;
    double smallp_intersect, smallp_ratio;
    double vx, vy, vz;
    double avg_range;
};