#ifndef _CX_BITS_H_
#define _CX_BITS_H_
#include "cx_pubhead.h"

namespace common_lib {
template <typename T>
void SetBit(T &value, cx_uint8 bits, bool fit) {
  value = fit ? value |= T(1) << bits : value &= ~(T(1) << bits);
}

template <typename T>
bool GetBit(T value, cx_uint8 bits) {
  return ((value & (T(1) << bits)) != 0);
}

template <typename T>
void SetBits(T &value, cx_uint8 from, cx_uint8 to, T val) {
  if (from > to) {
    ASSERT(0);
    return;
  }

  T mask = ((T(1) << (to - from + 1)) - 1) << from;

  val &= ~mask;

  value |= val;
}

template <typename T>
T GetBits(T value, cx_uint8 from, cx_uint8 to) {
  if (from > to) {
    return (value);
  }

  T mask = (T(1) << (to - from + 1)) - 1;

  return ((value & (mask << from)) >> from);
}

template <typename T>
T Unsigned(T src) {
  cx_uint64 mask;
  mask = (0x1 << (sizeof(T) * 8)) - 1;
  return (T)(src & (T)mask);
}

template <typename T1, typename T2>
void Signed(T1 src, T2 &dest, cx_uint8 num) {
  T1 bit_max = 0;
  cx_uint8 symbol = 0x00;

  if ((sizeof(T1) * 8) >= num) {
    symbol = (cx_uint8)((src >> (num - 1)) & 0x00000001);

    if ((0x00 == symbol) || (32 == num)) {
      dest = (T2)src;
    } else {
      bit_max = (T1)(0x00000001 << num);
      dest = (T2)(src - bit_max);
    }
  }
}
}  // namespace common_lib

#endif  // _CX_BITS_H_
