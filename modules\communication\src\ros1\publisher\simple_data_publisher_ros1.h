/**
 * @file simple_data_publisher_ros1.h
 * @brief This file contains the implementation of a simple data publisher for ROS1.
 * It provides a template class for publishing different data types (int, double, string, bool) using ROS1 messages.
 * * The class inherits from PublisherBase and implements the necessary methods to publish messages.
 * * It uses the ROS1 NodeHandle to create publishers for the specified topics.
 */
#pragma once

#if COMMUNICATION_TYPE == ROS1

#include <ros/ros.h>
// #include <deque>
// #include <mutex>
#include <std_msgs/Int32.h>
#include <std_msgs/Float32.h>
#include <std_msgs/Float64.h>
#include <std_msgs/String.h>
#include <std_msgs/Bool.h>

#include "publisher_base.h"

namespace communication::ros1
{

    template <typename T, typename T_MSG>
    class SimpleDataPublisherRos1 : public PublisherBase<T>
    {
    public:
        SimpleDataPublisherRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size = 10) : PublisherBase<T>(topic, max_buffer_size), nh_(nh)
        {
            // Initialize the ROS publisher with the specified topic and queue size
            publisher_ = nh_.advertise<T_MSG>(topic, max_buffer_size, true); // 保存最后一条发布的消息
        }

        ~SimpleDataPublisherRos1() = default;

    protected:
        virtual void PublishMsg() override
        {
            publisher_.publish(msg_);
        }

        virtual void ToMsg() override
        {
            msg_.data = PublisherBase<T>::data_; // Assign the data to the ROS message
        }

        virtual int GetSubscriberCount() override
        {

            return publisher_.getNumSubscribers();
        };

    private:
        ros::NodeHandle &nh_;
        ros::Publisher publisher_;
        T_MSG msg_; // ROS message type for integer data
    };

    using IntDataPublisherRos1 = SimpleDataPublisherRos1<int, std_msgs::Int32>;
    using DoubleDataPublisherRos1 = SimpleDataPublisherRos1<double, std_msgs::Float64>;
    using StringDataPublisherRos1 = SimpleDataPublisherRos1<std::string, std_msgs::String>;
    using BoolDataPublisherRos1 = SimpleDataPublisherRos1<bool, std_msgs::Bool>;
    using FloatDataPublisherRos1 = SimpleDataPublisherRos1<float, std_msgs::Float32>;

} // namespace communication::ros1{

#endif