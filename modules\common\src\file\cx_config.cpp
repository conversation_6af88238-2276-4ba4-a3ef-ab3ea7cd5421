#include "cx_config.h"

namespace common_lib {
using namespace std;

CXConfig::CXConfig(string filename, string delimiter, string comment)
    : delimiter_(delimiter)
    , comment_(comment) {
  is_init_ = true;
  // Construct a Config, getting keys and values from given file

  std::ifstream in(filename.c_str());

  if (!in) {
    is_init_ = false;
    File_not_found(filename);
    // if( !in ) throw File_not_found( filename );
  } else {
    in >> (*this);
    in.close();
  }
}

CXConfig::CXConfig()
    : delimiter_(string(1, '='))
    , comment_(string(1, '#')) {
  is_init_ = true;
  // Construct a Config without a file; empty
}

bool CXConfig::IsInit() {
  return is_init_;
}

bool CXConfig::KeyExists(const string &key) const {
  // Indicate whether key is found
  map_const_iterator p = contents_.find(key);
  return (p != contents_.end());
}

/* static */
void CXConfig::Trim(string &inout_s) {
  // Remove leading and trailing whitespace
  static const char whitespace[] = " \n\t\v\r\f";
  inout_s.erase(0, inout_s.find_first_not_of(whitespace));
  inout_s.erase(inout_s.find_last_not_of(whitespace) + 1U);

  return;
}

std::ostream &operator<<(std::ostream &os, const CXConfig &cf) {
  // Save a Config to os
  for (CXConfig::map_const_iterator p = cf.contents_.begin(); p != cf.contents_.end(); ++p) {
    os << p->first << " " << cf.delimiter_ << " ";
    os << p->second << std::endl;
  }

  return os;
}

void CXConfig::Remove(const string &key) {
  // Remove key and its value
  contents_.erase(contents_.find(key));

  return;
}

std::istream &operator>>(std::istream &is, CXConfig &cf) {
  // Load a Config from is
  // Read in keys and values, keeping internal whitespace
  typedef string::size_type pos;
  const string &delim = cf.delimiter_;  // separator
  const string &comm = cf.comment_;     // comment
  const pos skip = delim.length();      // length of separator

  string nextline = "";  // might need to read ahead to see where value ends

  while (is || nextline.length() > 0) {
    // Read an entire line at a time
    string line;
    if (nextline.length() > 0) {
      line = nextline;  // we read ahead; use it now
      nextline = "";
    } else {
      std::getline(is, line);
    }

    // Ignore comments
    line = line.substr(0, line.find(comm));

    // Parse the line if it contains a delimiter
    pos delimPos = line.find(delim);
    if (delimPos < string::npos) {
      // Extract the key
      string key = line.substr(0, delimPos);
      line.replace(0, delimPos + skip, "");

      // See if value continues on the next line
      // Stop at blank line, next line with a key, end of stream,
      // or end of file sentry
      bool terminate = false;
      while (!terminate && is) {
        std::getline(is, nextline);
        terminate = true;

        string nlcopy = nextline;
        CXConfig::Trim(nlcopy);
        if (nlcopy == "") {
          continue;
        }

        nextline = nextline.substr(0, nextline.find(comm));
        if (nextline.find(delim) != string::npos) {
          continue;
        }

        nlcopy = nextline;
        CXConfig::Trim(nlcopy);
        if (nlcopy != "") {
          line += "\n";
        }

        line += nextline;
        terminate = false;
      }

      // Store key and value
      CXConfig::Trim(key);
      CXConfig::Trim(line);
      cf.contents_[key] = line;  // overwrites if key is repeated
    }
  }

  return is;
}

bool CXConfig::FileExist(std::string filename) {
  bool exist = false;
  std::ifstream in(filename.c_str());
  if (in)
    exist = true;
  return exist;
}

void CXConfig::ReadFile(string filename, string delimiter, string comment) {
  delimiter_ = delimiter;
  comment_ = comment;
  std::ifstream in(filename.c_str());

  if (!in)
    throw File_not_found(filename);

  in >> (*this);
}
}  // namespace common_lib