#!/usr/bin/env python

from dynamic_reconfigure.parameter_generator_catkin import ParameterGenerator, bool_t, double_t, int_t

gen = ParameterGenerator()

gen.add("enabled", bool_t, 0, "Whether to apply this plugin or not", True)
gen.add("footprint_clearing_enabled", bool_t, 0, "Whether to clear the robot's footprint of lethal obstacles", True)
gen.add("max_obstacle_height", double_t, 0, "The maximum height of any obstacle to be inserted into the costmap in meters.", 2, 0, 50)

combo_enum = gen.enum([ gen.const("Overwrite", int_t,  0, "Overwrite values"),
                        gen.const("Maximum",   int_t,  1, "Take the maximum of the values"), 
                        gen.const("Nothing",   int_t, 99, "Do nothing")
                      ],
                      "Method for combining layers enum")

gen.add("combination_method", int_t, 0, "Method for combining two layers", 1, edit_method=combo_enum)


#gen.add("max_obstacle_range", double_t, 0, "The default maximum distance from the robot at which an obstacle will be inserted into the cost map in meters.", 2.5, 0, 50)
#gen.add("raytrace_range", double_t, 0, "The default range in meters at which to raytrace out obstacles from the map using sensor data.", 3, 0, 50)
exit(gen.generate("costmap_2d", "costmap_2d", "ObstaclePlugin"))
