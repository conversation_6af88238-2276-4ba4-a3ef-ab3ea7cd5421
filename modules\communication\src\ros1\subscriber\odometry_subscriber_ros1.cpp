#include "subscriber/odometry_subscriber_ros1.h"
#include <pcl_conversions/pcl_conversions.h>

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1{

OdometrySubscriberRos1::OdometrySubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size)
            : OdometrySubscriberBase(topic, max_buffer_size), nh_(nh) 
{
            subscriber_ = nh_.subscribe(topic, max_buffer_size, &OdometrySubscriberRos1::OdometryCallBackRos1, this);
}

//消息回调函数
void OdometrySubscriberRos1::OdometryCallBackRos1(const nav_msgs::Odometry::ConstPtr &odom_msg) 
{
    //消息转换
    PoseVelData posevel_data;
    posevel_data.pose_data.time = odom_msg->header.stamp.toSec();
    // 将位姿转换为Eigen矩阵
    // Eigen::Quaternionf q(odom_msg->pose.pose.orientation.w,
    //                      odom_msg->pose.pose.orientation.x,
    //                      odom_msg->pose.pose.orientation.y,
    //                      odom_msg->pose.pose.orientation.z);
    // posevel_data.pose.block<3, 3>(0, 0) = q.toRotationMatrix();
    // posevel_data.pose.block<3, 1>(0, 3) = Eigen::Vector3f(odom_msg->pose.pose.position.x,
    //                                                   odom_msg->pose.pose.position.y,
    //                                                   odom_msg->pose.pose.position.z);

    posevel_data.pose_data.position[0] = odom_msg->pose.pose.position.x;
    posevel_data.pose_data.position[1] = odom_msg->pose.pose.position.y;
    posevel_data.pose_data.position[2] = odom_msg->pose.pose.position.z;

    posevel_data.pose_data.orientation[0] = odom_msg->pose.pose.orientation.x;
    posevel_data.pose_data.orientation[1] = odom_msg->pose.pose.orientation.y;
    posevel_data.pose_data.orientation[2] = odom_msg->pose.pose.orientation.z;
    posevel_data.pose_data.orientation[3] = odom_msg->pose.pose.orientation.w;

    posevel_data.vel[0] = odom_msg->twist.twist.linear.x;
    posevel_data.vel[1] = odom_msg->twist.twist.linear.y;
    posevel_data.vel[2] = odom_msg->twist.twist.linear.z;
    posevel_data.angle_vel[0] = odom_msg->twist.twist.angular.x;
    posevel_data.angle_vel[1] = odom_msg->twist.twist.angular.y;
    posevel_data.angle_vel[2] = odom_msg->twist.twist.angular.z;        
    
    // 检查位姿数据是否有效
    if (posevel_data.pose_data.time <= 0.0)  {
        ROS_WARN("Received invalid odometry data.");
        return;
    }

    // 将转换后的数据存入缓冲区
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    data_buffer_.push_back(posevel_data);
    // 检查缓冲区是否超过最大大小
    if (data_buffer_.size() > max_buffer_size_) {
        data_buffer_.pop_front(); // 如果缓冲区已满，删除最旧的数据
    }
}

} // namespace communication::ros1{
#endif
    