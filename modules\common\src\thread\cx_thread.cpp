// #include <string.h>

#include "cx_thread.h"

#include "cx_thread_pool.h"
namespace common_lib {
CXThread::CXThread() {
  work_ptr_ = NULL;

  terminate_event_.Create();
  finished_event_.Create();
  run_event_.Create();

  terminate_event_.Reset();
  finished_event_.Reset();
  run_event_.Reset();
}

CXThread::~CXThread() {
  terminate_event_.Destroy();
  finished_event_.Destroy();
  run_event_.Destroy();
}

cx_int CXThread::AssignWork(CXWork *pWork) {
  // ASSERT(NULL == m_pWork);

  work_ptr_ = pWork;

  return 0;
}

cx_int CXThread::Start() {
  ASSERT(work_ptr_);
  if (NULL == work_ptr_) {
    return -1;
  }

  finished_event_.Reset();
  run_event_.Reset();

  SetState(CM_TS_RUNNING);
  work_ptr_->Run();

  return 0;
}

cx_int CXThread::Pause() {
  run_event_.Wait(INFINITE);

  return 0;
}

cx_int CXThread::Resume() {
  run_event_.Set();

  return 0;
}

cx_int CXThread::Stop() {
  terminate_event_.Set();
  Resume();

#ifdef WIN32
  //(void)WaitForSingleObjectInfinite(m_hThreadHandle, INFINITE);
#else
  pthread_join(m_pthread, NULL);
#endif

  return 0;
}

cx_int CXThread::SetState(CM_THREAD_STATE eState) {
  state_ = eState;

  return 0;
}

CM_THREAD_STATE CXThread::GetState() {
  return state_;
}

CXWork *CXThread::GetWork() {
  return work_ptr_;
}

ThreadProc *CXThread::GetThreadProc() {
  ASSERT(work_ptr_);

  return work_ptr_ ? work_ptr_->GetThreadProc() : NULL;
}

LPVOID CXThread::GetContext() {
  ASSERT(work_ptr_);

  return work_ptr_ ? work_ptr_->GetContext() : NULL;
}

cx_int CXThread::Create(ThreadProc pfnThreadProc, LPVOID const pContext) {
#ifdef LINUX
  pthread_create(&m_pthread, NULL, pfnThreadProc, pContext);
#endif

  return 0;
}

const cx_bool CXThread::IsTerminate() {
  return terminate_event_.IsTriggered() || GetSingleton4ThreadPool()->IsTerminate();
}

cx_int CXThread::Reset() {
  //(void)SetPriority(THREAD_PRIORITY_NORMAL);

  terminate_event_.Reset();
  finished_event_.Reset();
  run_event_.Reset();

  return 0;
}

cx_int CXThread::FinishWork() {
  finished_event_.Set();

  return 0;
}

cx_int SleepS(cx_dword dwSeconds) {
#ifdef WIN32
  Sleep(dwSeconds * 1000);
#else
  sleep(dwSeconds);
#endif  // WIN32

  return 0;
}

cx_int SleepMS(cx_dword dwMilliseconds) {
#ifdef WIN32
  Sleep(dwMilliseconds);
#else
  usleep(dwMilliseconds * 1000);
#endif  // WIN32

  return 0;
}

cx_int SleepUS(cx_dword dwMicroseconds) {
#ifdef WIN32
  Sleep(dwMicroseconds / 1000);
#else
  usleep(dwMicroseconds);
#endif  // WIN32

  return 0;
}

cx_int CXThread::GetPriority(int &priority) const {
  cx_int iResult = 0;

  int policy = 0;
  struct sched_param sp = {0};

  iResult = pthread_getschedparam(m_pthread, &policy, &sp);
  if (0 == iResult) {
    priority = sp.sched_priority;
  }

  return iResult;
}

cx_int CXThread::SetPriority(const int iPriority) {
  cx_int iResult = 0;

  int iPRI = iPriority;

  int policy = 0;
  int priority = 0;
  iResult = GetPriority(priority);

  if (0 != iResult) {
    return iResult;
  }

  struct sched_param sp = {0};
  int iMax = sched_get_priority_max(policy);
  int iMin = sched_get_priority_min(policy);

  if (iPRI < iMin) {
    iPRI = iMin;
  }

  if (iPRI > iMax) {
    iPRI = iMax;
  }

  sp.sched_priority = iPRI;

  iResult = pthread_setschedparam(m_pthread, policy, &sp);

  return iResult;
}
}  // namespace common_lib