#include "localization.h"
#include "coordtrans.h"
#include "cx_thread_pool.h"
#include "cx_time.h"
#include "pose.h"

namespace localization
{

  Localization::Localization(const std::string &config_file_path)
    : m_Config(common_lib::YamlConfig::GetInstance()),
    m_Lidar_R_wrt_IMU(M3D::Identity()),
    m_pImu(new ImuProcess()),
    m_pointcloudmap(new pcl::PointCloud<PointType>()),
    m_DSpointcloudmap(new pcl::PointCloud<PointType>()),
    m_feats_undistort(new PointCloudXYZI()),
    m_feats_down_body(new PointCloudXYZI()),
    m_feats_down_world(new PointCloudXYZI()),
    m_LocalMap(new PointCloudXYZI()),
    m_bscan_pub_en(true),
    m_bdense_pub_en(true),
    m_bscan_body_pub_en(true),
    m_pThread(nullptr),
    m_kdtreeManage(nullptr),
    m_iNUM_MAX_ITERATIONS(4),
    m_bextrinsic_est_en(false),
    m_ieffct_feat_num(0),
    m_ivaild_points(0),
    m_buseleg(false),
    m_brtk_vaild(false),
    m_brtk_heading_vaild(false),
    m_iMapCount(0)
  {
    // Load parameters
    //m_Config.init(config_file_path);
    Initialize();
  }

  Localization::~Localization()
  {
  }

  void Localization::Start()
  {
     CXWork::SetName("Localization");
     CXWork::SetThreadProc(Localization::MainLoop);
     CXWork::SetThreadContext(this);
     GetSingleton4ThreadPool()->ExecuteTask(this, m_pThread);
     m_ptrLocSubcribe->StartCommunication();
  }

  void Localization::Initialize()
  {
      //GetMapPath(m_sMapPath);
      m_sMapPath = m_Config.GetParam<std::string>("loadmappath",
                                                  "/mnt/d/work/01_code/jianmi/20250319/na_localization_lslidar/src/na_localization/");

      m_kdtreeManage = GetSingleton4KdtreeManage();
      m_Reloc = GetSingleton4Relocalization();
      m_ptrLocSubcribe = GetSingleton4LocSubcribe();
      m_ptrLocSubcribe->Init(); 

      InitDSFS();
      InitIMU();
      LoadPointMap();
      InitMapManage();
      InitParameters();

      m_flg_first_scan = true;
      m_initialized = false;
      m_system_initialized = false;
      m_last_timestamp = 0.0;
      m_current_pose.setIdentity();
      m_current_velocity.setZero();
      m_current_ba.setZero();
      m_current_bg.setZero();

      m_bflag_ikdtree_initial = false;
      m_bneed_relocal = true;

      m_bflag_gnss_pose = false;
      m_bflag_manualpos = false;
      //Start();  
  }

  void Localization::InitIMU()
  {
      vector<double> extrinT = m_Config.GetParam<std::vector<double>>("mapping/extrinsic_T", std::vector<double>()); 
      vector<double> extrinR = m_Config.GetParam<std::vector<double>>("mapping/extrinsic_R", std::vector<double>()); 
      printf("extrinT size: %d, extrinR size: %d\n", extrinT.size(), extrinR.size());
      printf("extrinT: ");
      for (const auto& val : extrinT)
      {
          printf("%f ", val);
      }
      printf("\nextrinR: ");
      for (const auto& val : extrinR)
      {
          printf("%f ", val);
      }
      printf("\n");
      m_Lidar_T_wrt_IMU << VEC_FROM_ARRAY(extrinT);
      m_Lidar_R_wrt_IMU << MAT_FROM_ARRAY(extrinR);
      double gyr_cov = m_Config.GetParam<double>("mapping/gyr_cov", 0.1);
      double acc_cov = m_Config.GetParam<double>("mapping/acc_cov", 0.1);
      double b_gyr_cov = m_Config.GetParam<double>("mapping/b_gyr_cov", 0.0001);
      double b_acc_cov = m_Config.GetParam<double>("mapping/b_acc_cov", 0.0001);
      m_pImu->set_param(m_Lidar_T_wrt_IMU, 
                        m_Lidar_R_wrt_IMU, 
                        V3D(gyr_cov, gyr_cov, gyr_cov), 
                        V3D(acc_cov, acc_cov, acc_cov),
                        V3D(b_gyr_cov, b_gyr_cov, b_gyr_cov), 
                        V3D(b_acc_cov, b_acc_cov, b_acc_cov));
  }

  void Localization::InitDSFS()
  {
      double filter_size_surf_min = m_Config.GetParam<double>("filter_size_surf", 0.5);
      m_downSizeFilterSurf.setLeafSize(filter_size_surf_min, filter_size_surf_min, filter_size_surf_min);
  } 

  void Localization::LoadPointMap()
  {
      double filter_size_map_min = m_Config.GetParam<double>("filter_size_map", 0.5);

      pcl::PCDReader pcd_reader;
      if(pcd_reader.read(m_sMapPath, *m_pointcloudmap) == 0)
      {
         cout << "read pcd success!" << endl;
      }
      else
      {
         cout << "read pcd failed!" << endl;
         return;
      }   
      
      m_downSizepointcloudmap.setInputCloud(m_pointcloudmap);
      m_downSizepointcloudmap.setLeafSize(filter_size_map_min, filter_size_map_min, filter_size_map_min);
      m_downSizepointcloudmap.filter(*m_pointcloudmap);

      // 需要对地图点云降采样，不然在rviz里显示太卡
      m_downSizepointcloudmap.setInputCloud(m_pointcloudmap);
      m_downSizepointcloudmap.setLeafSize(1.0f, 1.0f, 1.0f);
      m_downSizepointcloudmap.filter(*m_DSpointcloudmap);

      // sensor_msgs::PointCloud2 globalmapmsg;
      // pcl::toROSMsg(*DSpointcloudmap, globalmapmsg);
      // globalmapmsg.header.frame_id = "map"; // todo 这里发布一个从读取点云里面稀疏后的降采样点云 就是为了方便在RVIZ里面观察
      m_ptrLocSubcribe->PublishDSGlobalMap(m_DSpointcloudmap);
  }

  void Localization::InitMapManage()
  {
      m_mapManagement.set_ds_size( m_Config.GetParam<double>("filter_size_map", 0.5));
      m_mapManagement.set_input_PCD(m_sMapPath);
      m_mapManagement.voxel_process(); 
  }

  void Localization::InitParameters()
  {
      double sigmaleg = 0.0025;                                // 0.01
      m_Sigma_leg(0, 0) = sigmaleg;
      m_Sigma_leg(1, 1) = sigmaleg;
      m_Sigma_leg(2, 2) = sigmaleg;
      m_z_leg =  Eigen::Vector3d::Zero();

      double sigmartk = 0.05 * 0.05;
      m_Sigma_rtk(0, 0) = sigmartk;
      m_Sigma_rtk(1, 1) = sigmartk;
      m_Sigma_rtk(2, 2) = sigmartk;
      m_z_rtk = Eigen::Vector3d::Zero();

      string load_pose_path = m_Config.GetParam<std::string>("loadposepath", "");
      std::ifstream inputFile(load_pose_path);
      double lat0, lon0, alt0;
      if (inputFile >> lat0 >> lon0 >> alt0 >> m_max_z >> m_min_z)
      {
         m_max_z = ceil(m_max_z);
         m_min_z = floor(m_min_z);
         printf("先验地图原点经纬度坐标： 经度 = %3.7lf; 纬度 =  %3.7lf;  高度 =  %3.3lf; 构图过程位姿最大高度 =  %2.2f; 构图过程位姿最小高度 =  %2.2f; \n", lon0, lat0, alt0, m_max_z, m_min_z);
      }
      if (lon0 == 0 || lat0 == 0 || alt0 == 0)
      {
         cout << "无法读取地图rtk原点,需进行手动重定位" << endl;
         //usertk = false;
      }
  }

#ifdef WIN32
void Localization::MainLoop(PTP_CALLBACK_INSTANCE Instance, PVOID pContext)
#else
void* Localization::MainLoop(void* pContext)
#endif
{
    if (!pContext)
    {
#ifdef WIN32
        return;
#else
        return nullptr;
#endif
    }

    Localization* pLoc = static_cast<Localization*>(pContext);
    
    while (pLoc && pLoc->m_pThread && !pLoc->m_pThread->IsTerminate())
    {
        pLoc->Run();
        SleepMS(2);
    }

#ifdef WIN32
    return;
#else
    return nullptr;
#endif
}

cx_int Localization::Run()
{
    //根据手动点是否有效判断是否需要重定位
    if(m_ptrLocSubcribe->IsNeedReloc())
    {
        m_bneed_relocal = true;
        m_bflag_manualpos = true ;
    };
    Reloc();
    if(m_ptrLocSubcribe->SyncData(m_MeasureData))
    {
        // 处理测量数据
        if(!m_bneed_relocal)
        {
            Process(m_MeasureData);
        }
        
    }
    //根据定位状态判断是否需要RTK重定位
    if(m_bneed_relocal && m_ptrLocSubcribe->IsGnssDataReady())
    {
        m_gnss_pose = m_ptrLocSubcribe->GetGNSSPoseData();
        m_bflag_gnss_pose = true;
    }
    return 0;
}

void Localization::Reloc()
{
    // 判断是否需要重定位
    if (!m_bneed_relocal)
    {
        return;
    }

    // 定期发布全局地图
    if (m_iMapCount < 100)
    {
        m_iMapCount++;
    }
    else
    {
        m_ptrLocSubcribe->PublishDSGlobalMap(m_DSpointcloudmap);
        m_iMapCount = 0;
    }

    bool bRelocSuccess = false;
    PointCloudXYZI::Ptr input_cloud_ptr = m_ptrLocSubcribe->GetProcessLidarData();
    // 选择重定位方式
    if (m_bflag_gnss_pose == true) // rtk重定位   && flag_rtkheading==true
    {
        for(int i = 0; i < m_max_z - m_max_z + 1; i++)
        {
            if(m_gnss_pose.status < 4 && m_bflag_manualpos)
            {
                break;
            }
            bRelocSuccess = m_Reloc->rtk_reloc(m_gnss_pose,input_cloud_ptr); // todo gnss 单点重定位
            if(bRelocSuccess)
            {
                break;
            }
        }
        m_bflag_gnss_pose = false; // 重置RTK重定位标志位
    }

    if (m_bflag_manualpos == true) // 手动重定位
    {
        ManualPos manual_pos = m_ptrLocSubcribe->GetManualPos();
        
        for (int i = 0; i < m_max_z - m_max_z + 1; i++) // 遍历可能的高度值
        {
            cout << "尝试手动局部重定位, 可能的高度值: " << m_min_z + i << endl;
            manual_pos.manualpos[2] = m_min_z + i; // 设置高度值
            bRelocSuccess = m_Reloc->ManualPosReloc(manual_pos,input_cloud_ptr);
            if(bRelocSuccess)
            {
                break;
            }
        } 
        if(!bRelocSuccess)
        {
            cout << "手动局部重定位失败,  publish Relocal_flag_msg" << endl;
        }
        m_bflag_manualpos = false; // 重置手动重定位标志位
    }

    if(bRelocSuccess)
    {
        utils::Pose reloc_pose = m_Reloc->getRelocPos();
        m_state_point = m_kf.get_x();
        m_state_point.rot = Sophus::SO3d(reloc_pose.q_);
        m_state_point.pos = reloc_pose.t_;

        m_kf.change_x(m_state_point);
        m_bneed_relocal = false;
        cout << "手动局部重定位成功,  publish Relocal_flag_msg" << endl;
        // Relocal_flag_msg.data = true;
        // pubRelocal_flag.publish(Relocal_flag_msg);
    }
    
    // 重定位完成
    if (!m_bneed_relocal)
    {
        // ikdtree初始化完成，说明本次重定位是定位丢失后的重定位，需要重置ikdtree和包围盒
        if (m_bflag_ikdtree_initial)
        {
            Resetikdtree();
        }
    }
}

void Localization::Process(const MeasureGroup &Measures)
{
    if (m_flg_first_scan)
    {
        m_dfirst_lidar_time = Measures.lidar_beg_time;
        m_pImu->first_lidar_time = m_dfirst_lidar_time;
        m_flg_first_scan = false;
        return;
    }
    //m_state_point_last = m_state_point;

    bool bIMUReaday = m_pImu->Process(Measures, m_kf, m_feats_undistort);//去畸变
    // if (bIMUReaday)
    // {
    //     cout << "IMU去畸变成功" << endl;
    // }
    //发布节点启动成功消息 to do

    if (!m_pImu->feats_undistort_vaild)
    {        
        cout << "当前帧点云未去畸变, 退出本次处理" << endl;
        return;
    }

    m_state_point = m_kf.get_x();
    //cout<<"x: "<<m_state_point.pos[0]<<" y: "<<m_state_point.pos[1]<<" z: "<<m_state_point.pos[2]<<endl;

    //m_pos_lid = m_state_point.pos + m_state_point.rot.matrix() * m_state_point.offset_T_L_I;
    //m_bflg_EKF_inited = (Measures.lidar_beg_time - m_dfirst_lidar_time) < INIT_TIME ? false : true;

     // 点云下采样
    m_downSizeFilterSurf.setInputCloud(m_feats_undistort);
    m_downSizeFilterSurf.filter(*m_feats_down_body);
    m_ifeats_down_size = m_feats_down_body->points.size();
    //cout << "(雷达系)当前帧去畸变点云降采样后点数: " << m_ifeats_down_size << endl;

    //设置kdtree位置
    m_kdtreeManage->SetCurPoint(m_state_point);
    //m_kdtreeManage.UpdateKdTree();
    // if(m_bflag_ikdtree_initial == false)
    // {
    //     m_bflag_ikdtree_initial = true;  
    //     printf("ikdtree初始化完成\n");
    // }

    // 计算当前roll、pitch、yaw
    Eigen::Vector3d cur_atti = Eigen::Vector3d::Zero();
    cur_atti[0] = atan2(m_state_point.rot.matrix()(2, 1), m_state_point.rot.matrix()(2, 2));
    cur_atti[1] = -asin(m_state_point.rot.matrix()(2, 0));
    cur_atti[2] = atan2(m_state_point.rot.matrix()(1, 0), m_state_point.rot.matrix()(0, 0));

    m_Nearest_Points.resize(m_ifeats_down_size); // 初始化存储近邻点的vector

    bool kf_converge = false;
    pcl::KdTreeFLANN<PointType>::Ptr kdtree = m_kdtreeManage->getKdTree();
    m_LocalMap->clear();
    //m_LocalMap = kdtree->getInputCloud();
    m_LocalMap = boost::const_pointer_cast<pcl::PointCloud<pcl::PointXYZINormal>>(kdtree->getInputCloud());
    if (!kdtree) 
    {
        //cout << "Failed to get KdTree for localization update" << endl;
        return;
    }
    else
    {
        kf_converge = m_kf.update_iterated_dyn_share_modified(LASER_POINT_COV, m_feats_down_body, *kdtree, m_Nearest_Points, m_iNUM_MAX_ITERATIONS, 
                                                               m_bextrinsic_est_en, m_ieffct_feat_num, m_ivaild_points,
                                                               m_Sigma_leg, m_z_leg, m_buseleg,  m_bneed_relocal, 
                                                               m_Sigma_rtk, m_z_rtk, m_brtk_vaild, cur_atti, m_drtk_heading, m_brtk_heading_vaild, Measures);

       
    }

    if(kf_converge == false && !Measures.zero_vel_vaild) 
    {
        m_bneed_relocal = true;
        cout << "定位丢失!! 发布定位丢失消息，并开始重定位" << endl;
    }

    m_state_point = m_kf.get_x();

    publish_odometry_tf_path(m_state_point);

    if (m_bscan_pub_en)
    {
       publish_frame_world();
    }
    if (m_bscan_pub_en && m_bscan_body_pub_en)
    {
        publish_frame_body();
    }
    publish_local_map();
    return;
}

void Localization::PublishDSGlobalMap()
{
  
}

void Localization::Resetikdtree()
{
    std::lock_guard<std::mutex> lock(m_mapManagement.mutex_);

    cout << "start reset ikdtree" << endl;
    m_mapManagement.get_map(m_state_point.pos[0], m_state_point.pos[1]);
    // ikdtree.reconstruct(MM.pointcloud_output->points);

    // cout << "start reset ikdtree 2" << endl;

    pcl::PointCloud<PointType>::Ptr cloud_copy_1;
    cloud_copy_1.reset(new pcl::PointCloud<PointType>());
    *cloud_copy_1 = *(m_mapManagement.pointcloud_output);
    m_kdtree1.setInputCloud(cloud_copy_1);

    cout << "start reset ikdtree 3" << endl;

    pcl::PointCloud<PointType>::Ptr cloud_copy_2;
    cloud_copy_2.reset(new pcl::PointCloud<PointType>());
    *cloud_copy_2 = *(m_mapManagement.pointcloud_output);
    m_kdtree2.setInputCloud(cloud_copy_2);

    // kdtree1.setInputCloud(MM.pointcloud_output);
    
    // kdtree2.setInputCloud(MM.pointcloud_output);

    cout << "end reset ikdtree" << endl;
}

void Localization::publish_frame_world()
{
    if(m_bscan_body_pub_en)
    {
        PointCloudXYZI::Ptr laserCloudFullRes(m_bdense_pub_en ? m_feats_undistort : m_feats_down_body);
        int size = laserCloudFullRes->points.size();
        PointCloudXYZI::Ptr laserCloudWorld(new PointCloudXYZI(size, 1));
        for (int i = 0; i < size; i++)
        {
            pointBodyToWorld(&laserCloudFullRes->points[i],&laserCloudWorld->points[i],m_state_point);
        }
        GetSingleton4LocSubcribe()->Publish_frame_world(laserCloudWorld);
    }
}

void Localization::publish_frame_body()
{
    int size = m_feats_undistort->points.size();
    PointCloudXYZI::Ptr laserCloudIMUBody(new PointCloudXYZI(size, 1));

    for (int i = 0; i < size; i++)
    {
        RGBpointBodyLidarToIMU(&m_feats_undistort->points[i],&laserCloudIMUBody->points[i], m_state_point);
    }
    GetSingleton4LocSubcribe()->Publish_frame_body(laserCloudIMUBody);
}

void Localization::publish_local_map()
{
    if (m_LocalMap->points.empty())
    {
        //cout << "LocalMap is empty, not publishing." << endl;
        return;
    }
    // 发布局部地图
    GetSingleton4LocSubcribe()->Publish_LocalMap(m_LocalMap);
}

void Localization::publish_odometry_tf_path(state_ikfom & state_point)
{
    communication::PoseVelData odometry_data;
    odometry_data.pose_data.time = GetCurrentSeconds();
    odometry_data.pose_data.position[0] = state_point.pos[0];
    odometry_data.pose_data.position[1] = state_point.pos[1];
    odometry_data.pose_data.position[2] = state_point.pos[2];

    auto q_ = Eigen::Quaterniond(state_point.rot.matrix());
    // 将四元数转换为Eigen::Vector4f
    odometry_data.pose_data.orientation[0] = q_.coeffs()[0];
    odometry_data.pose_data.orientation[1] = q_.coeffs()[1];
    odometry_data.pose_data.orientation[2] = q_.coeffs()[2];
    odometry_data.pose_data.orientation[3] = q_.coeffs()[3];

    odometry_data.vel[0] = state_point.vel[0];
    odometry_data.vel[1] = state_point.vel[1];
    
    auto euler= utils::Pose::Quat2rpy(q_);
    odometry_data.vel[2] = utils::Pose::normalizeAngle(euler[0]) * 57.3;
    //发布Odometry数据
    GetSingleton4LocSubcribe()->PublishOdometry(odometry_data);

    // 发布TF数据
    communication::PoseData tf_data;
    tf_data.time = odometry_data.pose_data.time;
    tf_data.position = odometry_data.pose_data.position;
    tf_data.orientation = odometry_data.pose_data.orientation;
    GetSingleton4LocSubcribe()->Publish_TFData(tf_data);

    // 发布路径数据
    // 创建路径数据对象并填充
    static communication::PathData path_data;
    static int index = 0;
    index++;
    if(index % 10 == 0)
    {
        if(path_data.poses_.size() > 100) // 限制路径数据的大小
        {
            path_data.poses_.erase(path_data.poses_.begin()); // 清空路径数据
        }
        path_data.time_ = m_MeasureData.lidar_end_time;
        path_data.poses_.push_back(odometry_data.pose_data);
        GetSingleton4LocSubcribe()->Publish_Path(path_data);
    }

}

} // namespace localization{
