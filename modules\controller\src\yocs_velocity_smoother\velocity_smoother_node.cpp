#include "velocity_smoother.h"
#include <iostream>
#include <chrono>
#include <thread>
#include <signal.h>
#include <iomanip>
#include "communication.h"


using namespace yocs_velocity_smoother;

// 全局变量
std::unique_ptr<VelocitySmoother> smoother_;
std::shared_ptr<communication::OdometrySubscriberBase> odometry_subscriber_ptr;
std::shared_ptr<communication::TwistDataSubscriberBase> twist_data_subscriber_ptr;
std::shared_ptr<communication::TwistDataSubscriberBase> cmd_vel_subscriber_ptr;
std::shared_ptr<communication::TwistDataPublisherBase> twist_data_publisher_ptr;
std::shared_ptr<communication::TwistDataPublisherBase> cmd_vel_publisher_ptr;

// 全局变量用于控制程序运行
static bool g_running = true;

// 信号处理函数
void signalHandler(int signum) {
    std::cout << "\n🛑 接收到终止信号 " << signum << std::endl;
    g_running = false;
}

// 数据接收线程
void DataReceiveThread() {
    while (true) {
        
        if (!twist_data_subscriber_ptr->IsBufferEmpty()) {
            auto twist_data = twist_data_subscriber_ptr->GetBuffer();
            

                TwistMsg msg;
                msg.linear.x = twist_data.front().linear_vel.x();
                msg.linear.y = twist_data.front().linear_vel.y();
                msg.linear.z = twist_data.front().linear_vel.z();
                msg.angular.x = twist_data.front().angular_vel.x();
                msg.angular.y = twist_data.front().angular_vel.y();
                msg.angular.z = twist_data.front().angular_vel.z();
                smoother_->inputRobotVel(msg);
            
        }
    
        if (!cmd_vel_subscriber_ptr->IsBufferEmpty()) {
            auto cmd_vel = cmd_vel_subscriber_ptr->GetBuffer();
           
              
                TwistMsg msg;
                msg.linear.x = cmd_vel.front().linear_vel.x();
                msg.linear.y = cmd_vel.front().linear_vel.y();
                msg.linear.z = cmd_vel.front().linear_vel.z();
                msg.angular.x = cmd_vel.front().angular_vel.x();
                msg.angular.y = cmd_vel.front().angular_vel.y();
                msg.angular.z = cmd_vel.front().angular_vel.z();
                smoother_->inputVelocity(msg);
                 
        }

       if (!odometry_subscriber_ptr->IsBufferEmpty()) {
           auto odometry_data = odometry_subscriber_ptr->GetBuffer();

                OdometryMsg msg;
                msg.header.stamp = odometry_data.front().pose_data.time;
                msg.pose.pose.position.x = odometry_data.front().pose_data.position[0];
                msg.pose.pose.position.y = odometry_data.front().pose_data.position[1];
                msg.pose.pose.position.z = odometry_data.front().pose_data.position[2];
                msg.pose.pose.orientation.x = odometry_data.front().pose_data.orientation[0];
                msg.pose.pose.orientation.y = odometry_data.front().pose_data.orientation[1];
                msg.pose.pose.orientation.z = odometry_data.front().pose_data.orientation[2];
                msg.pose.pose.orientation.w = odometry_data.front().pose_data.orientation[3];
                smoother_->inputOdometry(msg);
    }

     std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

  void DataSendThread() {
            while (true) {
                if (smoother_->isRunning()) {
                    // 获取当前平滑后的速度
                    TwistMsg current_vel = smoother_->getTargetVel();
                    
                    // 创建并发送TwistData消息
                    communication::TwistData twist_data;
                    twist_data.time = std::chrono::duration_cast<std::chrono::seconds>(
                        std::chrono::system_clock::now().time_since_epoch()).count();
                    
                    // 转换线性速度
                    twist_data.linear_vel = Eigen::Vector3d(current_vel.linear.x, 
                                                          current_vel.linear.y, 
                                                          current_vel.linear.z);
                    
                    // 转换角速度
                    twist_data.angular_vel = Eigen::Vector3d(current_vel.angular.x, 
                                                           current_vel.angular.y, 
                                                           current_vel.angular.z);
                    
                    // 发布平滑后的速度命令
                    cmd_vel_publisher_ptr->Publish(twist_data);
                }
                
                // 控制发送频率
                std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 50Hz
            }
        }
/**
 * @brief 主程序
 */
int main(int argc, char** argv) {
    std::cout << "=== VelocitySmoother Node ===" << std::endl;
    
    try {
        // 创建通信模块实例
        auto communication_ptr = std::make_shared<communication::Communication>("velocity_smoother");
        if (!communication_ptr->Initialize("config/communication_config.yaml")) {
            std::cerr << " 通信模块初始化失败" << std::endl;
            return -1;
        }
        
        // 创建速度平滑器
        smoother_ = std::make_unique<VelocitySmoother>("velocity_smoother");

        // 设置基本参数
        smoother_->setSpeedLimits(2.0, 1.5);
        smoother_->setAccelLimits(1.0, 0.8);
        smoother_->setDecelFactor(2.0);
        smoother_->setFrequency(50.0);
        smoother_->setQuiet(false);
        smoother_->setRobotFeedback(0);

        // 创建订阅器和发布器
        twist_data_subscriber_ptr = communication_ptr->CreateTwistDataSubscriber("/twist/data");
        cmd_vel_subscriber_ptr = communication_ptr->CreateTwistDataSubscriber("/cmd_vel");
        odometry_subscriber_ptr = communication_ptr->CreateOdometrySubscriber("/Odometry");
        twist_data_publisher_ptr = communication_ptr->CreateTwistDataPublisher("/twist/data", "map", 1);
        cmd_vel_publisher_ptr = communication_ptr->CreateTwistDataPublisher("/cmd_vel", "map", 1);
        // 注册回调函数
        smoother_->setVelocityCallback([](const std::shared_ptr<TwistMsg>& msg) {});
        smoother_->setOdometryCallback([](const std::shared_ptr<OdometryMsg>& msg) {});
        smoother_->setRobotVelCallback([](const std::shared_ptr<TwistMsg>& msg) {});
        smoother_->setSmoothVelPublishCallback([](const std::shared_ptr<TwistMsg>& msg) {});
     

        // 启动数据接收和发送线程
        std::thread data_receive_thread(DataReceiveThread);
        std::thread data_send_thread(DataSendThread);
        data_receive_thread.detach();
        data_send_thread.detach();
        
        // 初始化速度平滑器
        if (!smoother_->init()) {
            std::cerr << " 速度平滑器初始化失败" << std::endl;
            return -1;
        }
        // 启动速度平滑器
        smoother_->start();

        // 最后运行通信模块，阻塞主线程
        communication_ptr->Run();  
        std::cout << "Stop communication module..." << std::endl;
        std::cout<<"Stop Velocity Smoother..."<<std::endl;

    } catch (const std::exception& e) {
        std::cerr << " 程序执行出错: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
