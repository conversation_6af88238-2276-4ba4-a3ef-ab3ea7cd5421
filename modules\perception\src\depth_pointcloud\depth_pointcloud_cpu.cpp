#include <pcl/common/transforms.h>

#include "depth_pointcloud_cpu.h"

namespace perception{


    DepthPointCloudConvertCpu::DepthPointCloudConvertCpu():
        DepthPointCloudConvertImpl(){

    }
    DepthPointCloudConvertCpu::~DepthPointCloudConvertCpu(){

    }


    bool DepthPointCloudConvertCpu::Convert(cv::Mat depth_image,PointCloudT::Ptr& cloud){
        if(!init_){
            return false;
        }

        size_t  depth_grid = parameter_.grid;
        cloud->height = depth_image.rows/depth_grid +1;
        cloud->width = depth_image.cols /depth_grid +1;
        cloud->is_dense = false;
        cloud->points.resize(cloud->height * cloud->width);

        float depth_scale = 0.001;          // 深度值单位：米（D435i默认毫米）
        float max_distance = parameter_.max_depth;    // 最大深度值，单位：米
        float fx    =parameter_.camera_info.fx;
        float fy    =parameter_.camera_info.fy;
        float ppx   =parameter_.camera_info.ppx;
        float ppy   =parameter_.camera_info.ppy;
        // 生成3D点
        for (int y = 0; y < depth_image.rows; y+=depth_grid) {
            for (int x = 0; x < depth_image.cols; x+=depth_grid) {
                uint16_t depth_value = depth_image.at<uint16_t>(y, x);
                float depth = depth_value * depth_scale;

                if (depth <= 0 || depth > max_distance) {
                    continue;
                }

                PointT& pt = cloud->points[(y/depth_grid) * cloud->width + (x/depth_grid)];
                pt.x = (x - ppx) * depth / fx;
                pt.y = (y - ppy) * depth / fy;
                pt.z = depth;

                pt.intensity=0;
            }
        }
        // 变换到camera_color_optical_frame -> 雷达坐标系
        PointCloudT::Ptr cloud_in_lidar(new PointCloudT);
        pcl::transformPointCloud(*cloud, *cloud_in_lidar, parameter_.transform.cast<float>());//
        std::swap(cloud,cloud_in_lidar);
    
        return true;

    }



} // namespace 