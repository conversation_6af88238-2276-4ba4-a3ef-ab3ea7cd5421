module: decision_node
version: v1.0

topics:
  terrain: /terrain_map
  global_path: /global_path
  trajectories: /trajectories
  localization: /Odometry
  imu: /imu
  decision_out: /decision/decision_out

# 决策频率
frequency: 10.0

# 安全参数
min_safe_distance: 0.6
collision_time_threshold: 2.0
emergency_stop_distance: 0.3

# 走廊检测
corridor_min_width: 0.8
corridor_max_width: 2.5
min_wall_length: 1.5
min_points_per_side: 15
max_angle_deviation: 0.2

# 速度控制
max_speed: 1.5
normal_speed: 1.0
slow_speed: 0.4
stair_speed: 0.2
turn_speed_reduction: 0.6

# 步态控制
default_gait: "TROT"
stair_gait: "WALK"
obstacle_gait: "CRAWL"
narrow_gait: "CRAWL"
yield_gait: "STAND"

# 地形适应
slope_threshold: 0.15
step_height_threshold: 0.15
step_width_threshold: 0.25
min_steps: 2
rough_terrain_threshold: 0.08

# 避障参数
frontal_angle: 60.0
side_angle: 45.0
min_avoidance_distance: 0.8
clearance_margin: 0.2

# 动态障碍物
prediction_horizon: 3.0
yield_timeout: 5.0
pedestrian_speed: 1.2
vehicle_speed: 2.5