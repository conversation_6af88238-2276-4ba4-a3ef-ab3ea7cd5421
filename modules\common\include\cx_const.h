﻿#ifndef _ME_CONST_H_
#define _ME_CONST_H_

#include "cx_types.h"
namespace common_lib {
#define IN
#define OUT
#define INOUT

#define MAX_PATH 260

constexpr hash_t prime = 0x100000001B3ull;
constexpr hash_t basis = 0xCBF29CE484222325ull;

#ifdef MEM_CHECK
void CM_FreeMemory(void *p);
#else
#define CM_FreeMemory(...)
#endif

#define DELETE_S(object_ptr)   \
  if (NULL != object_ptr) {    \
    CM_FreeMemory(object_ptr); \
    delete (object_ptr);       \
    (object_ptr) = NULL;       \
  }
#define DELETE_SG(object_ptr)  \
  if (NULL != object_ptr) {    \
    CM_FreeMemory(object_ptr); \
    delete[] (object_ptr);     \
    (object_ptr) = NULL;       \
  }
}  // namespace common_lib
#endif  // !_ME_CONST_H_
