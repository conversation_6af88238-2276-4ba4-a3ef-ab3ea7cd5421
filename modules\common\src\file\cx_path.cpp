﻿
#include "cx_path.h"

#ifdef WIN32
#include "windows.h"
#endif

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef WIN32
#include <direct.h>
#include <io.h>
#else

#include <dirent.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

#endif

#include <errno.h>
namespace common_lib {
static cx_char s_szExePath[MAX_PATH] = {0};
static cx_char s_szMapPath[MAX_PATH] = {0};
static cx_char s_szModuleFileName[MAX_PATH] = {0};
static cx_string s_RecordPath;

cx_int SetModuleFileName(cx_char *pszFile) {
  if (s_szModuleFileName[0] == 0) {
    size_t len = strlen(pszFile);
    if (len > MAX_PATH) {
      len = MAX_PATH - 1;
    }

    // printf("SetModuleFileName pszFile  = %s, len = %d\n", pszFile, len);
    strncpy(s_szModuleFileName, pszFile, len);
    // printf("s_szModuleFileName  = %s\n", s_szModuleFileName);
  }

  return 0;
}

#ifdef LINUX

cx_dword GetModuleFileName(
    cx_handle hModule,  // [in] Handle to the module whose executable file name is being requested.
                        // If this parameter is NULL, GetModuleFileName returns the path for the
                        // file used to create the calling process.
    cx_char *lpFilename,  // [out] Pointer to a buffer that is filled in with the path and file name
                          // of the module.
    cx_dword nSize  // [in] Specifies the length, in characters, of the lpFilename buffer. If the
                    // length of the path and file name exceeds this limit, the string is truncated.
) {
#if 0
    size_t len = strlen(s_szModuleFileName);
    if (len > nSize)
    {
        len = nSize;
    }

    //	linux目录不能包含中文，linux下的中文路径为uft-8编码，非GBK编码
    mbstowcs(lpFilename, s_szModuleFileName, len);
#endif

  strcpy_s(lpFilename, s_szModuleFileName);

  return 0;
}

#endif  // LINUX

cx_int SetExePath(const cx_char *const szPath) {
#ifdef _MSC_VER
  strcpy_s(s_szMapPath, szPath);
  sprintf_s(s_szMapPath, "%s%smap", szPath, PATH_SEPARATOR);
#else
  strcpy_s(s_szExePath, szPath);
  sprintf_s(s_szMapPath, "%s%smap", szPath, PATH_SEPARATOR);

#endif

  return 0;
}

cx_int GetExePath(cx_string &strPath) {
  strPath = s_szExePath;
  return 0;
}

cx_int SetMapPath(const cx_char *const szPath) {
#ifdef _MSC_VER
  strcpy_s(s_szMapPath, szPath);
#else
  strncpy(s_szMapPath, szPath, sizeof(s_szMapPath) - 1);
  s_szMapPath[sizeof(s_szMapPath) - 1] = '\0';  // 确保终止
#endif
  return 0;
}

cx_int GetMapPath(cx_string &strPath) {
  strPath = s_szMapPath;
  return 0;
}

#ifdef WIN32
void DeleteDirectory(const char *szPath) {
  WIN32_FIND_DATA data;
  HANDLE hFind;
  char cFullPath[100];
  char cNewPath[100];
  sprintf_s(cFullPath, "%s\\*.*", szPath);
  hFind = FindFirstFile(cFullPath, &data);
  if (INVALID_HANDLE_VALUE == hFind) {
    return;
  }

  do {
    if ((!strcmp(".", data.cFileName)) || (!strcmp("..", data.cFileName))) {
      continue;
    }

    if (data.dwFileAttributes == FILE_ATTRIBUTE_DIRECTORY) {
      sprintf_s(cNewPath, "%s\\%s", szPath, data.cFileName);
      DeleteDirectory(cNewPath);  // 递归
    }

    sprintf_s(cFullPath, "%s\\%s", szPath, data.cFileName);
    DeleteFile(cFullPath);

  } while (FindNextFile(hFind, &data));
}

// 判断是否是".."目录和"."目录
bool is_special_dir(const char *path) {
  return strcmp(path, "..") == 0 || strcmp(path, ".") == 0;
}

// 判断文件属性是目录还是文件
bool is_dir(int attrib) {
  return attrib == 16 || attrib == 18 || attrib == 20;
}

// 显示删除失败原因
void show_error(const char *file_name = NULL) {
  errno_t err;
  _get_errno(&err);
  switch (err) {
  case ENOTEMPTY:
    printf("Given path is not a directory, the directory is not empty, or the directory is either "
           "the current working directory or the root directory.\n");
    break;
  case ENOENT:
    printf("Path is invalid.\n");
    break;
  case EACCES:
    printf("%s had been opend by some application, can't delete.\n", file_name);
    break;
  }
}

void get_file_path(const char *path, const char *file_name, char *file_path) {
  strcpy_s(file_path, sizeof(char) * _MAX_PATH, path);
  file_path[strlen(file_path) - 1] = '\0';
  strcat_s(file_path, sizeof(char) * _MAX_PATH, file_name);
  strcat_s(file_path, sizeof(char) * _MAX_PATH, "\\*");
}

// 递归搜索目录中文件并删除
void RemoveFiles(const char *path) {
  _finddata_t dir_info;
  _finddata_t file_info;
  intptr_t f_handle;
  char tmp_path[_MAX_PATH];
  if ((f_handle = _findfirst(path, &dir_info)) != -1) {
    while (_findnext(f_handle, &file_info) == 0) {
      if (is_special_dir(file_info.name)) {
        continue;
      }

      if (is_dir(file_info.attrib))  // 如果是目录，生成完整的路径
      {
        get_file_path(path, file_info.name, tmp_path);
        RemoveFiles(tmp_path);  // 开始递归删除目录中的内容
        tmp_path[strlen(tmp_path) - 2] = '\0';
        if (file_info.attrib == 20)
          printf("This is system file, can't delete!\n");
        else {
          // 删除空目录，必须在递归返回前调用_findclose,否则无法删除目录
          if (_rmdir(tmp_path) == -1) {
            show_error();  // 目录非空则会显示出错原因
          }
        }
      } else {
        strcpy_s(tmp_path, path);
        tmp_path[strlen(tmp_path) - 1] = '\0';
        strcat_s(tmp_path, file_info.name);  // 生成完整的文件路径
        if (remove(tmp_path) == -1) {
          show_error(file_info.name);
        }
      }
    }
    _findclose(f_handle);  // 关闭打开的文件句柄，并释放关联资源，否则无法删除空目录
  } else {
    show_error();  // 若路径不存在，显示错误信息
  }
  return;
}
#endif

cx_bool IsFileExist(const cx_string &strFileName) {
  cx_bool bExist = false;

  FILE *pFile = fopen(strFileName.c_str(), "rb");

  if (pFile != NULL) {
    bExist = true;
    fclose(pFile);
  }

  return bExist;
}

void TransformSlash(char *szFileName) {
#ifdef WIN32
  char *p = szFileName;
  while (*p != 0) {
    if (*p == '/') {
      *p = '\\';
    }
    p++;
  }
#else
  char *p = szFileName;
  while (*p != 0) {
    if (*p == '\\') {
      *p = '/';
    }
    p++;
  }
#endif
}

cx_int CleanDir(const char *szDir) {
  DIR *pDir = nullptr;
  struct dirent *pEnt = nullptr;
  struct stat statEnt;

  pDir = opendir(szDir);

  if (pDir == nullptr) {
    return -1;
  }

  while ((pEnt = readdir(pDir)) != nullptr) {
    char szFileName[MAX_PATH] = {0};
    sprintf(szFileName, "%s/%s", szDir, pEnt->d_name);

    lstat(szFileName, &statEnt);

    if (S_ISDIR(statEnt.st_mode)) {
      if (strcmp(pEnt->d_name, "..") == 0 || strcmp(pEnt->d_name, ".") == 0) {
        continue;
      }
      strcat(szFileName, "/");
      CleanDir(szFileName);
    } else {
      unlink(szFileName);
    }
  }

  closedir(pDir);

  return 0;
}

#ifdef LINUX

// 遍历指定文件夹下的所有文件，不包括指定文件夹内的文件夹
std::vector<std::string> GetListFiles(const std::string &path, const std::string &exten) {
  std::vector<std::string> list;
  list.clear();

  DIR *dp = nullptr;
  struct dirent *dirp = nullptr;
  if ((dp = opendir(path.c_str())) == nullptr) {
    return list;
  }

  while ((dirp = readdir(dp)) != nullptr) {
    if (dirp->d_type == DT_REG) {
      if (exten.compare("*") == 0)
        list.emplace_back(static_cast<std::string>(dirp->d_name));
      else if (std::string(dirp->d_name).find(exten) != std::string::npos)
        list.emplace_back(static_cast<std::string>(dirp->d_name));
    }
  }

  closedir(dp);

  return list;
}

// 遍历指定文件夹下的所有文件夹，不包括指定文件夹下的文件
std::vector<std::string> GetListFolders(const std::string &path, const std::string &exten) {
  std::vector<std::string> list;
  list.clear();

  DIR *dp = nullptr;
  struct dirent *dirp = nullptr;
  if ((dp = opendir(path.c_str())) == nullptr) {
    return list;
  }

  while ((dirp = readdir(dp)) != nullptr) {
    if (dirp->d_type == DT_DIR && strcmp(dirp->d_name, ".") != 0 &&
        strcmp(dirp->d_name, "..") != 0) {
      if (exten.compare("*") == 0)
        list.emplace_back(static_cast<std::string>(dirp->d_name));
      else if (std::string(dirp->d_name).find(exten) != std::string::npos)
        list.emplace_back(static_cast<std::string>(dirp->d_name));
    }
  }

  closedir(dp);

  return list;
}

// 遍历指定文件夹下的所有文件，包括指定文件夹内的文件夹
std::vector<std::string> GetListFilesR(const std::string &path, const std::string &exten) {
  std::vector<std::string> list = GetListFiles(path, exten);

  std::vector<std::string> dirs = GetListFolders(path, exten);

  for (auto it = dirs.cbegin(); it != dirs.cend(); ++it) {
    std::vector<std::string> cl = GetListFiles(*it, exten);
    for (auto file : cl) {
      list.emplace_back(*it + "/" + file);
    }
  }

  return list;
}

cx_int is_dir(const char *filename) {
  struct stat buf;
  int ret = stat(filename, &buf);
  if (0 == ret) {
    if (buf.st_mode & S_IFDIR) {
      // printf("%s is folder\n",filename);
      return 0;
    } else {
      // printf("%s is file\n",filename);
      return 1;
    }
  }

  return -1;
}

cx_int delete_dir(const char *dirname) {
  char chBuf[256];
  DIR *dir = NULL;
  struct dirent *ptr;
  int ret = 0;
  dir = opendir(dirname);
  if (NULL == dir) {
    return -1;
  }

  while ((ptr = readdir(dir)) != NULL) {
    ret = strcmp(ptr->d_name, ".");
    if (0 == ret) {
      continue;
    }

    ret = strcmp(ptr->d_name, "..");
    if (0 == ret) {
      continue;
    }

    snprintf(chBuf, 256, "%s/%s", dirname, ptr->d_name);
    ret = is_dir(chBuf);
    if (0 == ret) {
      // printf("%s is dir\n", chBuf);
      ret = delete_dir(chBuf);
      if (0 != ret) {
        return -1;
      }
    } else if (1 == ret) {
      // printf("%s is file\n", chBuf);
      ret = remove(chBuf);
      if (0 != ret) {
        return -1;
      }
    }
  }

  (void)closedir(dir);

  ret = remove(dirname);
  if (0 != ret) {
    return -1;
  }

  return 0;
}

#else

std::vector<std::string> GetListFiles(const std::string &path, const std::string &exten) {
  std::vector<std::string> list;
  return list;
}

std::vector<std::string> GetListFolders(const std::string &path, const std::string &exten) {
  std::vector<std::string> list;
  return list;
}

std::vector<std::string> GetListFilesR(const std::string &path, const std::string &exten) {
  std::vector<std::string> list;
  return list;
}

cx_int is_dir(const char *filename) {
  return -1;
}

cx_int delete_dir(const char *dirname) {
  //    return -1;
  DeleteDirectory(dirname);
  return 0;
}

#endif

std::vector<std::string> SearchListFiles(const std::string &path, const std::string &name,
                                         bool bIsFile) {
  std::vector<std::string> list;
  if (bIsFile) {
    std::vector<std::string> files = GetListFiles(path);

    for (auto file : files) {
      if (file.find(name) != file.npos) {
        list.emplace_back(file);
      }
    }
  } else {
    std::vector<std::string> dirs = GetListFolders(path);
    for (auto dir : dirs) {
      if (dir.find(name) != dir.npos) {
        list.emplace_back(dir);
      }
    }
  }

  return list;
}

void CreatPath(cx_string &strPath) {
  cx_string sExe;
  GetExePath(sExe);
  strPath = sExe + "/" + strPath;
  if (!opendir(strPath.c_str())) {
    mkdir(strPath.c_str(), S_IRWXU | S_IRWXG | S_IRWXO);
  }
}

FILE *CreateFile(const cx_string &strPath, const char *filename) {
  cx_string FileDir = strPath + "/" + filename;
  FILE *pFile = NULL;
  if (!IsFileExist(FileDir.c_str())) {
    pFile = fopen(FileDir.c_str(), "w+");
  }
  return pFile;
}

void SetRecordPath(const cx_string &strPath) {
  s_RecordPath = strPath;
}

cx_string GetRecordPath() {
  return s_RecordPath;
}
}  // namespace common_lib