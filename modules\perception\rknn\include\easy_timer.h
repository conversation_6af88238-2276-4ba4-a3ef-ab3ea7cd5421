
#ifndef __EASY_TIMER__HH__
#define __EASY_TIMER__HH__

#include <chrono>
#include <fstream>
#include <iostream>
#include <map>
#include <numeric>
#include <string>
#include <vector>
#include <iomanip>


class Timer {
    public:
     struct TimerRecord {
         TimerRecord() = default;
         TimerRecord(const std::string& name, double time_usage) {
             func_name_ = name;
             time_usage_in_ms_.emplace_back(time_usage);
         }
         std::string func_name_;
         std::vector<double> time_usage_in_ms_;
     };
 
     /**
      * call F and save its time usage
      * @tparam F
      * @param func
      * @param func_name
      */
     template <class F>
     static void Evaluate(F&& func, const std::string& func_name) {
         auto t1 = std::chrono::high_resolution_clock::now();
         std::forward<F>(func)();
         auto t2 = std::chrono::high_resolution_clock::now();
         auto time_used = std::chrono::duration_cast<std::chrono::duration<double>>(t2 - t1).count() * 1000;
 
         if (records_.find(func_name) != records_.end()) {
             records_[func_name].time_usage_in_ms_.emplace_back(time_used);
         } else {
             records_.insert({func_name, TimerRecord(func_name, time_used)});
         }
     }
 
     /// print the run time
     static void PrintTime() {
         std::cout << "> ----------------- Printing run time ----------"<<std::endl;
         for (const auto& r : records_) {
             std::cout << "> [ " << std::left << std::setw(50)<<r.first << " ] average time usage: " << std::setw(10) 
                       << std::accumulate(r.second.time_usage_in_ms_.begin(), r.second.time_usage_in_ms_.end(), 0.0) /
                              double(r.second.time_usage_in_ms_.size())
                       << " ms , called times: " << std::setw(8) << r.second.time_usage_in_ms_.size() <<std::endl;
         }
         std::cout << "> ----------------- Printing run time end -------" <<std::endl;
     }
 
     /// get the average time usage of a function
     static double GetMeanTime(const std::string& func_name) {
         if (records_.find(func_name) == records_.end()) {
             return 0.0;
         }
 
         auto r = records_[func_name];
         return std::accumulate(r.time_usage_in_ms_.begin(), r.time_usage_in_ms_.end(), 0.0) /
                double(r.time_usage_in_ms_.size());
     }
 
     /// clean the records
     static void Clear() { records_.clear(); }
 
    private:
     static std::map<std::string, TimerRecord> records_;
 };
 

 #endif