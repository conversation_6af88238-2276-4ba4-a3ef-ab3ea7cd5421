#pragma once

#if COMMUNICATION_TYPE == ROS1

#include <ros/ros.h>
// #include <deque>
// #include <mutex>
#include <image_transport/image_transport.h>
#include <cv_bridge/cv_bridge.h>

// #include "lidar_point_cloud.h"
#include "publisher_base.h"

namespace communication::ros1
{

    class ImageDataPublisherRos1 : public ImageDataPublisherBase
    {
    public:
        ImageDataPublisherRos1(image_transport::ImageTransport &it,
                               const std::string &topic, const std::string &pixel_Type = "bgr8",
                               size_t max_buffer_size = 10);

        virtual ~ImageDataPublisherRos1() = default;

    protected:
        virtual void PublishMsg() override
        {

            publisher_.publish(msg_);
        }

        virtual void ToMsg() override
        {
            std_msgs::Header header;
            header.stamp = ros::Time(data_.time);
            header.frame_id = frame_id_;

            msg_ = cv_bridge::CvImage(header, pixel_type_, data_.image).toImageMsg();
        }

        virtual int GetSubscriberCount() override
        {
            return publisher_.getNumSubscribers();
        }

    private:
        image_transport::ImageTransport &it_;
        std::string pixel_type_;
        image_transport::Publisher publisher_;
        sensor_msgs::ImagePtr msg_; // ROS message type for point cloud data
        std::string frame_id_;
    };

} // namespace communication::ros1{

#endif