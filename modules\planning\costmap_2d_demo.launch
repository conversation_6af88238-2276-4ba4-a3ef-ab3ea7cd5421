<launch>
    <!-- 发布静态TF变换 -->
    <node pkg="tf2_ros" type="static_transform_publisher" name="map_to_base_link"
          args="0 0 0 0 0 0 map base_link 100" />
    
    <!-- 启动costmap_2d主节点 -->
    <node pkg="costmap_2d" type="costmap_2d_node" name="costmap_2d_node" 
          output="screen" />
    
    <!-- 启动costmap_2d可视化节点 -->
    <node pkg="costmap_2d" type="costmap_2d_cloud" name="costmap_2d_cloud" 
          output="screen" />
    
    <node pkg="costmap_2d" type="costmap_2d_markers" name="costmap_2d_markers" 
          output="screen" />
    
    <!-- 启动RViz -->
    <node pkg="rviz" type="rviz" name="rviz" 
          args="-d $(find costmap_2d)/rviz/costmap_2d.rviz" />
</launch> 