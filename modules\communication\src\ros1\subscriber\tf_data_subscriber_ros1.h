/**
 *  @file tf_data_subscriber_ros1.h
 *  @brief This file contains the ROS1 implementation of the tf subscriber.
 * *  It subscribes to tf data from a specified ROS topic and processes the incoming messages.
 * *  It inherits from the TFDataSubscriberBase class.
 */
#pragma once

#if COMMUNICATION_TYPE == ROS1

#include <ros/ros.h>
#include <tf2_ros/transform_listener.h>
#include <tf2_ros/buffer.h>
#include <geometry_msgs/TransformStamped.h>

#include "subscriber_base.h"

namespace communication::ros1
{

    class TFDataSubscriberRos1 : public TFDataSubscriberBase
    {
    public:
        TFDataSubscriberRos1(ros::NodeHandle &nh, const std::string &target_frame_id, const std::string &source_frame_id);

        ~TFDataSubscriberRos1() = default;

        virtual std::deque<PoseData> GetBuffer(bool clear_buffer = true) override
        {

            PoseData tf;
            data_buffer_.clear();
            if (GetTFData(tf))
            {
                data_buffer_.push_back(tf);
            }

            // ROS_WARN("TFDataSubscriberRos1::GetBuffer: %d", data_buffer_.size());
            return data_buffer_;
        }

        virtual PoseData GetBufferFront() override
        {

            PoseData tf;
            if (GetTFData(tf))
            {
                // ROS_WARN("TFDataSubscriberRos1::GetBufferFront: %.3f", tf.time);
                // std::cout << "TFDataSubscriberRos1::GetBufferFront: " << tf. << std::endl;
            }
            else
            {
                // std::cout << "TFDataSubscriberRos1::GetBufferFront: No valid TF data received." << std::endl;
            }
            return tf;
        }

    private:
        bool GetTFData(PoseData &tf)
        {
            // std::lock_guard<std::mutex> lock(buffer_mutex_);
            try
            {

                // 获取最新的变换（立即返回当前最新的数据, 从 source_frame 到 target_frame）
                geometry_msgs::TransformStamped transformStamped =
                    tfBuffer_.lookupTransform(target_frame_id_, source_frame_id_, ros::Time(0), ros::Duration(10));

                tf.time = transformStamped.header.stamp.toSec();
                // tf.time = ros::Time::now().toSec(); // 使用当前时间作为时间戳
                // 提取数据
                auto &trans = transformStamped.transform.translation;
                auto &rot = transformStamped.transform.rotation;

                // 提取平移
                tf.position[0] = trans.x;
                tf.position[1] = trans.y;
                tf.position[2] = trans.z;

                // 提取旋转（四元数）
                tf.orientation[0] = rot.x;
                tf.orientation[1] = rot.y;
                tf.orientation[2] = rot.z;
                tf.orientation[3] = rot.w;

                return true;
            }
            catch (tf2::TransformException &ex)
            {
                ROS_ERROR("search tf failed: %s", ex.what());
                return false;
            }
        }

        ros::NodeHandle &nh_;
        // 创建 Buffer（存储变换数据）
        tf2_ros::Buffer tfBuffer_;

        // 创建 TransformListener（自动订阅 /tf 和 /tf_static，填充 Buffer）
        tf2_ros::TransformListener tfListener_;

        std::string target_frame_id_;
        std::string source_frame_id_;
    };

} // namespace communication::ros1{

#endif
