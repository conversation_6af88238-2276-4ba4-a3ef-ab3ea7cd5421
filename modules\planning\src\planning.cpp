#include "planning.h"
#include "config.h"
#include "cx_path.h"
namespace planning
{

  Planning::Planning(const std::string &config_file_path)
  {
    // Load parameters
  }

  Planning::~Planning()
  {
  }
  void Planning::GetCommonConfig()
  {
    // Load parameters
    common_lib::CommonConfig::GetCommonConfig(common_config_info_);
  }

  cx_int Planning::GetIndexIncrement(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "indexIncrement";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_int index_increment = common_lib::Config::GetInstance().getParam<cx_int>(heading, 20);
    return index_increment;
  }

  cx_int Planning::GetIndexNum(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "indexIncrement";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_int index_num = common_lib::Config::GetInstance().getParam<cx_int>(heading, 10);
    return index_num;
  }

  cx_double Planning::GetObstacleHeightThre(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "obstacleHeightThre";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double obstacle_height_thre = common_lib::Config::GetInstance().getParam<cx_double>(heading, 0.15);
    return obstacle_height_thre;
  }

  cx_double Planning::GetVehicleLength(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "vehicleLength";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double vehicle_length = common_lib::Config::GetInstance().getParam<cx_double>(heading, 1.0);
    return vehicle_length;
  }

  cx_double Planning::GetVehicleWidth(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "vehicleWidth";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double vehicle_width = common_lib::Config::GetInstance().getParam<cx_double>(heading, 0.5);
    return vehicle_width;
  }

  cx_int Planning::GetObsnumThre(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "obsnumThre";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_int obsnum_thre = common_lib::Config::GetInstance().getParam<cx_int>(heading, 2);
    return obsnum_thre;
  }

  cx_double Planning::GetAdjacentRange(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "adjacentRange";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double adjacent_range = common_lib::Config::GetInstance().getParam<cx_double>(heading, 3.0);
    return adjacent_range;
  }

  cx_double Planning::GetReplanTime(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "replan_time";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double replan_time = common_lib::Config::GetInstance().getParam<cx_double>(heading, 3.0);
    return replan_time;
  }
  cx_double Planning::GetSensorOffsetX(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "sensorOffsetX";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double sensor_offset_x = common_lib::Config::GetInstance().getParam<cx_double>(heading, 3.0);
    return sensor_offset_x;
  }

  cx_double Planning::GetSensorOffsetY(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "sensorOffsetY";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double sensor_offset_y = common_lib::Config::GetInstance().getParam<cx_double>(heading, 3.0);
    return sensor_offset_y;
  }

  cx_int Planning::GetPointId(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "point_id";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double point_id = common_lib::Config::GetInstance().getParam<cx_double>(heading, 3.0);
    return point_id;
  }
  cx_int Planning::GetPointInfo(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "point_info";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double point_info = common_lib::Config::GetInstance().getParam<cx_double>(heading, 3.0);
    return point_info;
  }
  cx_int Planning::GetGait(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "gait";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double gait = common_lib::Config::GetInstance().getParam<cx_double>(heading, 3.0);
    return gait;
  }
  cx_int Planning::GetSpeed(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "speed";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double speed = common_lib::Config::GetInstance().getParam<cx_double>(heading, 3.0);
    return speed;
  }
  cx_int Planning::GetManner(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "manner";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double manner = common_lib::Config::GetInstance().getParam<cx_double>(heading, 3.0);
    return manner;
  }
  cx_int Planning::GetObsmode(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "obsmode";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double obsmode = common_lib::Config::GetInstance().getParam<cx_double>(heading, 3.0);
    return obsmode;
  }
  cx_int Planning::GetNavmode(const cx_string &h1, const cx_string &h2)
  {
    cx_string default_heading = "navmode";
    cx_string heading = "";
    if (!h2.empty() && h2[0] != '\0')
    {
      heading = h2 + "/" + default_heading;
    }

    if (!h1.empty() && h1[0] != '\0')
    {
      heading = h1 + "/" + heading;
    }
    cx_double navmode = common_lib::Config::GetInstance().getParam<cx_double>(heading, 3.0);
    return navmode;
  }
  void Planning::GetGlobalTrajGenerateConfig()
  {
    bool success = false;
    cx_string config_file;
    common_lib::GetExePath(config_file);
    config_file += "/config/global_traj_generate.yaml";
    fprintf(stderr, ">>>> planning debug >>>> config file path: %s  \n", config_file.c_str());
    success = common_lib::IsFileExist(config_file);
    if (success)
    {
      common_lib::Config::GetInstance().Init(config_file);
    }

    index_increment_ = GetIndexIncrement("", "");
    index_num_ = GetIndexNum("", "");
  }

  void Planning::GetLocalPlannerConfig()
  {
    bool success = false;
    cx_string config_file;
    common_lib::GetExePath(config_file);
    config_file += "/config/local_planner.yaml";
    fprintf(stderr, ">>>> planning debug >>>> config file path: %s  \n", config_file.c_str());
    success = common_lib::IsFileExist(config_file);
    if (success)
    {
      common_lib::Config::GetInstance().Init(config_file);
    }
  }

  void Planning::GetObstacleStopConfig()
  {
    bool success = false;
    cx_string config_file;
    common_lib::GetExePath(config_file);
    config_file += "/config/obstacle_stop.yaml";
    fprintf(stderr, ">>>> planning debug >>>> config file path: %s  \n", config_file.c_str());
    success = common_lib::IsFileExist(config_file);
    if (success)
    {
      common_lib::Config::GetInstance().Init(config_file);
    }

    obstacle_height_thre_ = GetObstacleHeightThre("", ""); // 障碍物高度阈值 (原有默认值)
    vehicle_length_ = GetVehicleLength("", "");            // 载体长度 (原有默认值)
    vehicle_width_ = GetVehicleWidth("", "");              // 载体宽度 (原有默认值)
    obsnum_thre_ = GetObsnumThre("", "");                  // 障碍物数量阈值 (原有默认值)
    adjacent_range_ = GetAdjacentRange("", "");            // 邻近范围 (原有默认值)
    replan_time_ = GetReplanTime("", "");                  // 重规划时间 (原有默认值)
    sensor_offset_x_ = GetSensorOffsetX("", "");           // 传感器X偏移
    sensor_offset_y_ = GetSensorOffsetY("", "");           // 传感器Y偏移
  }

  void Planning::GetPointPublishConfig()
  {
    bool success = false;
    cx_string config_file;
    common_lib::GetExePath(config_file);
    config_file += "/config/obstacle_stop.yaml";
    fprintf(stderr, ">>>> planning debug >>>> config file path: %s  \n", config_file.c_str());
    success = common_lib::IsFileExist(config_file);
    if (success)
    {
      common_lib::Config::GetInstance().Init(config_file);
    }

    point_id_ = GetPointId("", "");     // 点位ID，用于标识不同的导航点
    point_info_ = GetPointInfo("", ""); // 点位信息，附加的点位描述信息
    gait_ = GetGait("", "");            // 步态模式，控制机器人的行走方式
    speed_ = GetSpeed("", "");          // 速度等级，控制机器人的移动速度
    manner_ = GetManner("", "");        // 行为方式，控制机器人的行为模式
    obsmode_ = GetObsmode("", "");      // 避障模式，控制避障行为
    navmode_ = GetNavmode("", "");      // 导航模式，控制导航策略
  }
} // namespace planning {
