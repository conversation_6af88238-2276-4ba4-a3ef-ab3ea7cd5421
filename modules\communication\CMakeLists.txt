# communication/CMakeLists.txt
cmake_minimum_required(VERSION 3.5)
project(communication)

# 全局设置库文件输出目录
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
# set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)

set(CMAKE_CXX_STANDARD 17)
#set(CMAKE_CXX_FLAGS "-std=c++17 -march=native")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall")

if (NOT DEFINED CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE "Release")
endif()

if(NOT DEFINED PLATFORM)
  set(PLATFORM "x86_64")
endif()

# if(NOT DEFINED COMMUNICATION_TYPE)
#   set(COMMUNICATION_TYPE "ROS2")
# endif()

# Find required packages
find_package(PCL REQUIRED)
find_package(OpenCV REQUIRED)

include_directories(
    ${PCL_INCLUDE_DIRS}
    ${OpenCV_INCLUDE_DIRS}
    include
    src
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include
)

message(STATUS "COMMUNICATION_TYPE: ${COMMUNICATION_TYPE}")

if(COMMUNICATION_TYPE STREQUAL "ROS1")

  add_definitions(-DCOMMUNICATION_TYPE_ROS1)     

  find_package(catkin REQUIRED COMPONENTS
    roscpp
    rospy
    std_msgs
    cv_bridge
    image_transport
  )

  include_directories(
    /usr/include/opencv4/
    ${catkin_INCLUDE_DIRS}
    src/ros1
  )
  add_subdirectory(src/ros1)

elseif(COMMUNICATION_TYPE STREQUAL "ROS2")
  add_definitions(-DCOMMUNICATION_TYPE_ROS2) 
  # target_compile_definitions(${PROJECT_NAME}_core PRIVATE COMMUNICATION_TYPE)

  # ROS2 specific setup
  find_package(ament_cmake REQUIRED)
  find_package(rclcpp REQUIRED)
  find_package(std_msgs REQUIRED)
  find_package(geometry_msgs REQUIRED)
  find_package(nav_msgs REQUIRED)
  find_package(sensor_msgs REQUIRED)

  include_directories(
    ${rclcpp_INCLUDE_DIRS}
    ${std_msgs_INCLUDE_DIRS}
    ${geometry_msgs_INCLUDE_DIRS}
    ${nav_msgs_INCLUDE_DIRS}
    ${sensor_msgs_INCLUDE_DIRS}
    /usr/include/opencv4/
    src/ros2
  )
  add_subdirectory(src/ros2)

endif()

add_library(${PROJECT_NAME}_core SHARED
  src/communication.cpp
  src/communication_impl.cpp
)

link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../common/lib
              ${CMAKE_CURRENT_SOURCE_DIR}/lib
)

if(COMMUNICATION_TYPE STREQUAL "ROS1")
  target_link_libraries(${PROJECT_NAME}_core
                      common_lib 
                      ros1_comm             
  )
elseif(COMMUNICATION_TYPE STREQUAL "ROS2")
  target_link_libraries(${PROJECT_NAME}_core
  common_lib 
  ros2_comm             
  )
endif()
# add_executable(communication_test
#   src/communication_node.cpp
# )
# target_link_libraries(communication_test
#   ${PROJECT_NAME}_core
#   # ros1_comm
#   # common_lib
# )
