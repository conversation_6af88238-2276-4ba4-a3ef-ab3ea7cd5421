/**
 * @file path_data.h    
 * @brief This file contains the definition of the PathData class, which stores a sequence of PoseData objects representing a path.
 */
#pragma once
#include "data_types/pose_data.h"

namespace communication {
// 路径数据类，存储路径上的位姿数据
class PathData {
    public:
        double time_ = 0.0; // 路径数据的时间戳，单位为秒
        std::vector<PoseData> poses_;  // 存储路径上的位姿数据

};


}  // namespace communication

