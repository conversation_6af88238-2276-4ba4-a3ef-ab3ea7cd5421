#include "common/utils.h"


namespace perception{

    BoolCondition::BoolCondition(){
        condition_=false;
    }
    BoolCondition::~BoolCondition(){}

    void BoolCondition::NotifyOne(){
        {
            std::lock_guard<std::mutex> lock(mtx_);
            condition_=true;
        }
        cv_.notify_one();
    }
    void BoolCondition::Wait(){
        std::unique_lock<std::mutex> lock(mtx_);
        cv_.wait(lock, [this] { return condition_; });
    }
    void BoolCondition::Reset(){
        std::unique_lock<std::mutex> lock(mtx_);
        condition_ = false;
    }


    namespace utils{


    Eigen::MatrixXf PointCloudToEigenMat(PointCloudT::Ptr &cloud){
        Eigen::MatrixXf points;
        size_t num_points = cloud->points.size();
        points.resize(num_points, 3);

        for (size_t i = 0; i < num_points; ++i) {
            points.row(i) << cloud->points[i].x,cloud->points[i].y,cloud->points[i].z;
        }
        return points;

    }
    PointCloudT::Ptr EigenMatToPointCloud(Eigen::MatrixXf mat){
        PointCloudT::Ptr cloud(new PointCloudT);
        for(size_t i=0;i<mat.rows();++i){
            PointT pt;
            pt.x=mat(i,0);
            pt.y=mat(i,1);
            pt.z=mat(i,2);
            cloud->push_back(pt);
        }
        return cloud;
    }

    } // utils

} // perception