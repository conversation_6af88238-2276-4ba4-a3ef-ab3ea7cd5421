#ifndef __DEPTH_POINTCLOUD_CONVERT_CPU__H_
#define __DEPTH_POINTCLOUD_CONVERT_CPU__H_

#include "depth_pointcloud_impl.h"

namespace perception{

class DepthPointCloudConvertCpu : public DepthPointCloudConvertImpl{

public:
    DepthPointCloudConvertCpu();
    virtual ~DepthPointCloudConvertCpu();

    bool Convert(cv::Mat depth,PointCloudT::Ptr& cloud) override ; 

protected:

};


}   //namespace

#endif
