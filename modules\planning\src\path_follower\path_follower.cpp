#include "path_follower.h"
#include <thread>
#include <chrono>
#include <iomanip> 

namespace path_follower {

// 构造函数
PathFollower::PathFollower(const std::string& configPath)
    : configPath_(configPath)
    , running_(false)
    , initialized_(false)
{
    // 初始化变量
    initializeVariables();
}

// 析构函数
PathFollower::~PathFollower() {
    stop();
}

// 初始化函数
bool PathFollower::initialize() {
    std::lock_guard<std::mutex> lock(dataMutex_);
    
    if (initialized_) {
        return true;
    }
    
    try {
        // 加载配置
        if (!configPath_.empty()) {
            if (!loadConfig(configPath_)) {
                std::cerr << "Failed to load config from: " << configPath_ << std::endl;
                return false;
            }
        }
        
        initialized_ = true;
        std::cout << "PathFollower initialization complete." << std::endl;
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "PathFollower initialization failed: " << e.what() << std::endl;
        return false;
    }
}

// 加载配置
bool PathFollower::loadConfig(const std::string& configPath) {
    if (configPath.empty()) {
        std::cout << "Using default configuration." << std::endl;
        return true;
    }
    
    try {
        if (configPath.find(".yaml") != std::string::npos || 
            configPath.find(".yml") != std::string::npos) {
            return loadYamlConfig(configPath);
        } else {
            std::cerr << "Unsupported config file format. Only YAML is supported." << std::endl;
            return false;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Failed to load config: " << e.what() << std::endl;
        return false;
    }
}

// 加载YAML配置
bool PathFollower::loadYamlConfig(const std::string& configPath) {
    try {
        YAML::Node config = YAML::LoadFile(configPath);
        
        // 直接从根节点读取参数（适配新的扁平化YAML结构）
        if (config["sensorOffsetX"]) config_.sensorOffsetX = config["sensorOffsetX"].as<double>();
        if (config["sensorOffsetY"]) config_.sensorOffsetY = config["sensorOffsetY"].as<double>();
        if (config["pubSkipNum"]) config_.pubSkipNum = config["pubSkipNum"].as<int>();
        if (config["lookAheadDis"]) config_.lookAheadDis = config["lookAheadDis"].as<double>();
        if (config["yawRateGain"]) config_.yawRateGain = config["yawRateGain"].as<double>();
        if (config["stopYawRateGain"]) config_.stopYawRateGain = config["stopYawRateGain"].as<double>();
        if (config["maxYawRate"]) config_.maxYawRate = config["maxYawRate"].as<double>();
        if (config["maxAccel"]) config_.maxAccel = config["maxAccel"].as<double>();
        if (config["switchTimeThre"]) config_.switchTimeThre = config["switchTimeThre"].as<double>();
        if (config["dirDiffThre"]) config_.dirDiffThre = config["dirDiffThre"].as<double>();
        if (config["stopDisThre"]) config_.stopDisThre = config["stopDisThre"].as<double>();
        if (config["slowDwnDisThre"]) config_.slowDwnDisThre = config["slowDwnDisThre"].as<double>();
        if (config["useInclRateToSlow"]) config_.useInclRateToSlow = config["useInclRateToSlow"].as<bool>();
        if (config["inclRateThre"]) config_.inclRateThre = config["inclRateThre"].as<double>();
        if (config["slowRate1"]) config_.slowRate1 = config["slowRate1"].as<double>();
        if (config["slowRate2"]) config_.slowRate2 = config["slowRate2"].as<double>();
        if (config["slowTime1"]) config_.slowTime1 = config["slowTime1"].as<double>();
        if (config["slowTime2"]) config_.slowTime2 = config["slowTime2"].as<double>();
        if (config["useInclToStop"]) config_.useInclToStop = config["useInclToStop"].as<bool>();
        if (config["inclThre"]) config_.inclThre = config["inclThre"].as<double>();
        if (config["stopTime"]) config_.stopTime = config["stopTime"].as<double>();
        if (config["noRotAtGoal"]) config_.noRotAtGoal = config["noRotAtGoal"].as<bool>();
        
        // 设置默认值（如果YAML中没有定义）
        if (!config["maxSpeed"]) config_.maxSpeed = 2.0; // 默认最大速度
        if (!config["twoWayDrive"]) config_.twoWayDrive = false; // 默认单向驱动
        
        std::cout << "YAML configuration loaded successfully from: " << configPath << std::endl;
        return true;
    }
    catch (const YAML::Exception& e) {
        std::cerr << "YAML parsing error: " << e.what() << std::endl;
        return false;
    }
    catch (const std::exception& e) {
        std::cerr << "Error loading YAML config: " << e.what() << std::endl;
        return false;
    }
}

// 初始化变量
void PathFollower::initializeVariables() {
    // 初始化所有原始变量（与原pathFollower.cpp一致）
    adjustmode = false;
    goalX = goalY = goalZ = 0.0;
    nav_start = 0;
    modifySpeed = 0.0;
    joyYaw = 0.0;
    vehicleX = vehicleY = vehicleZ = 0.0;
    vehicleRoll = vehiclePitch = vehicleYaw = 0.0;
    vehicleXRec = vehicleYRec = vehicleZRec = 0.0;
    vehicleRollRec = vehiclePitchRec = vehicleYawRec = 0.0;
    vehicleYawRate = vehicleSpeed = 0.0;
    odomTime = 0.0;
    slowInitTime = 0.0;
    stopInitTime = 0.0;
    pathPointID = 0;
    pathInit = false;
    navFwd = true;
    switchTime = 0.0;
    safetyStop = 0;
    rotinit = false;
    odometryTime = 0.0;
    pubSkipCount = 0;
}

// 获取当前时间
double PathFollower::getCurrentTime() {
    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration<double>(duration).count();
}

// 数据输入接口实现
void PathFollower::updateOdometry(const OdometryData& odom) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    odomTime = odom.header.stamp;
    // 计算姿态角
    double roll, pitch, yaw;
    odom.pose.pose.orientation.toRPY(roll, pitch, yaw);
    vehicleRoll = roll;
    vehiclePitch = pitch;
    vehicleYaw = yaw;
    vehicleX = odom.pose.pose.position.x - cos(yaw) * config_.sensorOffsetX + sin(yaw) * config_.sensorOffsetY;
    vehicleY = odom.pose.pose.position.y - sin(yaw) * config_.sensorOffsetX - cos(yaw) * config_.sensorOffsetY;
    vehicleZ = odom.pose.pose.position.z;
    if ((fabs(roll) > config_.inclThre * PI / 180.0 || fabs(pitch) > config_.inclThre * PI / 180.0) && config_.useInclToStop) {
        stopInitTime = odom.header.stamp;
    }
    if ((fabs(odom.twist.twist.angular.x) > config_.inclRateThre * PI / 180.0 || 
         fabs(odom.twist.twist.angular.y) > config_.inclRateThre * PI / 180.0) && config_.useInclRateToSlow) {
        slowInitTime = odom.header.stamp;
    }
    odometryTime = getCurrentTime();
}

void PathFollower::updatePath(const PathData& path) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    
    int pathSize = path.poses_.size();
    this->path.poses_.resize(pathSize);
    
    for (int i = 0; i < pathSize; i++) {
        this->path.poses_[i].pose.position.x = path.poses_[i].pose.position.x;
        this->path.poses_[i].pose.position.y = path.poses_[i].pose.position.y;
        this->path.poses_[i].pose.position.z = path.poses_[i].pose.position.z;
    }

    // 记录接收到轨迹时刻的载体位姿信息
    vehicleXRec = vehicleX;
    vehicleYRec = vehicleY;
    vehicleZRec = vehicleZ;
    vehicleRollRec = vehicleRoll;
    vehiclePitchRec = vehiclePitch;
    vehicleYawRec = vehicleYaw;
    
    // 轨迹初始化完成
    pathPointID = 0;
    pathInit = true;
}

void PathFollower::updateMode(const BoolMsg& mode) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    modeHandler(std::make_shared<BoolMsg>(mode));
}

void PathFollower::updateGoal(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    goalHandler(std::make_shared<PoseStamped>(goal));
}

void PathFollower::updateWebGoal(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    webgoalHandler(std::make_shared<PoseStamped>(goal));
}

void PathFollower::updateStop(const Int8Msg& stop) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    stopHandler(std::make_shared<Int8Msg>(stop));
}

// 输出回调设置
void PathFollower::setSpeedPublishCallback(TwistCallback callback) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    speedPublishCallback_ = callback;
}

// 控制接口
void PathFollower::start() {
    if (!initialized_ || running_) return;
    running_ = true;
    worker_thread_ = std::thread(&PathFollower::controlLoop, this);
}

void PathFollower::stop() {
    running_ = false;
    if (worker_thread_.joinable()) worker_thread_.join();
}

// 参数设置
void PathFollower::setMaxSpeed(double speed) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    config_.maxSpeed = speed;
}

void PathFollower::setLookAheadDistance(double distance) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    config_.lookAheadDis = distance;
}

void PathFollower::setTwoWayDrive(bool enable) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    config_.twoWayDrive = enable;
}

void PathFollower::setNoRotationAtGoal(bool enable) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    config_.noRotAtGoal = enable;
}

void PathFollower::setYawRateGain(double gain) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    config_.yawRateGain = gain;
}

void PathFollower::setMaxYawRate(double rate) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    config_.maxYawRate = rate;
}

void PathFollower::setMaxAcceleration(double accel) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    config_.maxAccel = accel;
}

// 状态查询
bool PathFollower::isInitialized() const {
    return initialized_;
}

bool PathFollower::isRunning() const {
    return running_;
}

bool PathFollower::isPathValid() const {
    return pathInit && !path.empty();
}

bool PathFollower::hasReachedGoal() const {
    double distance = sqrt(pow(vehicleX - goalX, 2) + pow(vehicleY - goalY, 2));
    return distance < config_.stopDisThre;
}

double PathFollower::getDistanceToGoal() const {
    return sqrt(pow(vehicleX - goalX, 2) + pow(vehicleY - goalY, 2));
}

double PathFollower::getCurrentSpeed() const {
    return vehicleSpeed;
}

double PathFollower::getCurrentYawRate() const {
    return vehicleYawRate;
}

int PathFollower::getCurrentPathPointID() const {
    return pathPointID;
}

PathFollowerConfig PathFollower::getConfig() const {
    return config_;
}

// 原始回调函数实现 (保留原有算法逻辑)
void PathFollower::odomHandler(const std::shared_ptr<OdometryData>& odomIn) {
    odomTime = odomIn->header.stamp;
    double roll, pitch, yaw;
    odomIn->pose.pose.orientation.toRPY(roll, pitch, yaw);
    vehicleRoll = roll;
    vehiclePitch = pitch;
    vehicleYaw = yaw;
    vehicleX = odomIn->pose.pose.position.x - cos(yaw) * config_.sensorOffsetX + sin(yaw) * config_.sensorOffsetY;
    vehicleY = odomIn->pose.pose.position.y - sin(yaw) * config_.sensorOffsetX - cos(yaw) * config_.sensorOffsetY;
    vehicleZ = odomIn->pose.pose.position.z;
    if ((fabs(roll) > config_.inclThre * PI / 180.0 || fabs(pitch) > config_.inclThre * PI / 180.0) && config_.useInclToStop) {
        stopInitTime = odomIn->header.stamp;
    }
    if ((fabs(odomIn->twist.twist.angular.x) > config_.inclRateThre * PI / 180.0 ||
         fabs(odomIn->twist.twist.angular.y) > config_.inclRateThre * PI / 180.0) && config_.useInclRateToSlow) {
        slowInitTime = odomIn->header.stamp;
    }
    odometryTime = getCurrentTime();
}

void PathFollower::pathHandler(const std::shared_ptr<PathData>& pathIn) {
    std::lock_guard<std::mutex> lock(dataMutex_);
    
    int pathSize = pathIn->poses_.size();
    path.poses_.resize(pathSize);
    
    for (int i = 0; i < pathSize; i++) {
        path.poses_[i].pose.position.x = pathIn->poses_[i].pose.position.x;
        path.poses_[i].pose.position.y = pathIn->poses_[i].pose.position.y;
        path.poses_[i].pose.position.z = pathIn->poses_[i].pose.position.z;
    }
    
    // 记录接收到轨迹时刻的载体位姿信息
    vehicleXRec = vehicleX;
    vehicleYRec = vehicleY;
    vehicleZRec = vehicleZ;
    vehicleRollRec = vehicleRoll;
    vehiclePitchRec = vehiclePitch;
    vehicleYawRec = vehicleYaw;

    // 轨迹初始化完成
    pathPointID = 0;
    pathInit = true;
}

void PathFollower::modeHandler(const std::shared_ptr<BoolMsg>& mode) {
    std::cout<<"adjustmode"<<std::endl;
    adjustmode = mode->data;
}

void PathFollower::goalHandler(const std::shared_ptr<PoseStamped>& goal) {

    std::cout<<"  goal receive ok "<<std::endl;
    goalX = goal->pose.position.x;
    goalY = goal->pose.position.y;
    // goalZ = goal->pose.position.z;
    nav_start = 1;

    // 前进后退切换
    config_.twoWayDrive = false;
    modifySpeed = config_.maxSpeed;
    navFwd = true;
    switchTime = 0;
    rotinit = false;
    config_.dirDiffThre = 0.1;
}

void PathFollower::webgoalHandler(const std::shared_ptr<PoseStamped>& goal) {
    goalX = goal->pose.position.x;
    goalY = goal->pose.position.y;
    // goalZ = goal->pose.position.z;
    nav_start = 1;

    // 前进后退切换
    config_.twoWayDrive = false;
    modifySpeed = config_.maxSpeed;
    navFwd = true;
    switchTime = 0;
    rotinit = false;
    config_.dirDiffThre = 0.1;
}

void PathFollower::stopHandler(const std::shared_ptr<Int8Msg>& stop) {
    std::cout<<"safetystomode"<<std::endl;
    safetyStop = stop->data;
}

// 主循环方法
void PathFollower::controlLoop() {
    const double loop_rate = 100.0; // 100Hz
    const auto sleep_duration = std::chrono::duration<double>(1.0 / loop_rate);
    while (running_) {
        try {
            performPathFollowing();
            std::this_thread::sleep_for(sleep_duration);
        }
        catch (const std::exception& e) {
            std::cerr << "Control loop error: " << e.what() << std::endl;
            break;
        }
    }
}

// 主要的路径跟随算法 (保留原pathFollower.cpp的核心逻辑)
void PathFollower::performPathFollowing() {

    // 安全停止处理
    if (safetyStop == 1) {
        cmd_vel.setZero();
        if (speedPublishCallback_) {
            speedPublishCallback_(std::make_shared<TwistData>(cmd_vel));
        }
        return;
    }

    // 路径跟随主逻辑
    if (pathInit && !adjustmode && nav_start == 1 && safetyStop != 1) {
        // 计算接收到轨迹时的位置在当前载体系下的坐标
        float vehicleXRel = cos(vehicleYawRec) * (vehicleX - vehicleXRec) + sin(vehicleYawRec) * (vehicleY - vehicleYRec);
        float vehicleYRel = -sin(vehicleYawRec) * (vehicleX - vehicleXRec) + cos(vehicleYawRec) * (vehicleY - vehicleYRec);

        // 计算接收到轨迹时的位置距轨迹最后一个点的距离
        int pathSize = path.poses_.size();
        float endDisX = path.poses_[pathSize - 1].pose.position.x - vehicleXRel;
        float endDisY = path.poses_[pathSize - 1].pose.position.y - vehicleYRel;
        float endDis = sqrt(endDisX * endDisX + endDisY * endDisY);

        // 寻找前视距离范围内的最后一个点
        float disX, disY, dis;
        while (pathPointID < pathSize - 1) {
            disX = path.poses_[pathPointID].pose.position.x - vehicleXRel;
            disY = path.poses_[pathPointID].pose.position.y - vehicleYRel;
            dis = sqrt(disX * disX + disY * disY);
            if (dis < config_.lookAheadDis) pathPointID++;
            else break;
        }
 
        // 计算接收到轨迹时的位置距前视距离内最后一个点的距离以及角度
       
        disX = path.poses_[pathPointID].pose.position.x - vehicleXRel;
        disY = path.poses_[pathPointID].pose.position.y - vehicleYRel;
        dis = sqrt(disX * disX + disY * disY);
        float pathDir = atan2(disY, disX);

        // 计算当前位置与前视距离最后一个点的夹角
        float dirDiff = vehicleYaw - vehicleYawRec - pathDir;
        if (!rotinit) dirDiff = vehicleYaw - atan2(goalY - vehicleY, goalX - vehicleX);

        // 保证夹角在-pi到pi内
        if (dirDiff > PI) dirDiff -= 2 * PI;
        else if (dirDiff < -PI) dirDiff += 2 * PI;
        if (dirDiff > PI) dirDiff -= 2 * PI;
        else if (dirDiff < -PI) dirDiff += 2 * PI;

        // 判断是否进行双向驱动
        if (config_.twoWayDrive) {
            double time = getCurrentTime();
            // 夹角大于90度且当前处于前向运动转换为后向运动
            if (fabs(dirDiff) > PI / 2 && navFwd && time - switchTime > config_.switchTimeThre) {
                navFwd = false;
                switchTime = time;
            }
            // 夹角小于90度且当前处于后向运动转换为前向运动
            else if (fabs(dirDiff) < PI / 2 && !navFwd && time - switchTime > config_.switchTimeThre) {
                navFwd = true;
                switchTime = time;
            }
        }

        // 计算载体距离终点距离
        double distance = sqrt(pow(vehicleX - goalX, 2) + pow(vehicleY - goalY, 2));

        // 计算载体在三米内减速到最小速度所需的加速度
        double a = (config_.maxSpeed * config_.maxSpeed - 0.1) / (2 * 3);

        // 判断是否进入减速范围
        if (distance < 3) modifySpeed = sqrt(2 * a * distance + 0.1);
        else modifySpeed = config_.maxSpeed;

        float joySpeed2 = modifySpeed;

        // 判断前后向运动，若后向将角度设置在90度内，速度设置为负
        if (!navFwd) {
            dirDiff += PI;
            if (dirDiff > PI) dirDiff -= 2 * PI;
            joySpeed2 *= -1;
        }

        // 判断线速度是否为0，若为0则采用停止转角增益，若不为0则采用普通转角增益
        if (fabs(vehicleSpeed) < 2.0 * config_.maxAccel / 100.0)
            vehicleYawRate = -config_.stopYawRateGain * dirDiff;
        else
            vehicleYawRate = -config_.yawRateGain * dirDiff;

        // 对载体角速度进行限幅
        if (vehicleYawRate > config_.maxYawRate * PI / 180.0)
            vehicleYawRate = config_.maxYawRate * PI / 180.0;
        else if (vehicleYawRate < -config_.maxYawRate * PI / 180.0)
            vehicleYawRate = -config_.maxYawRate * PI / 180.0;

        // 若局部距离小于阈值角速度为0
        if (pathSize <= 1 || (dis < config_.stopDisThre && config_.noRotAtGoal))
            vehicleYawRate = 0;

        // 若局部距离小于减速距离则进行减速
        if (pathSize <= 1)
            joySpeed2 = 0;
        else if (endDis < config_.slowDwnDisThre)
            joySpeed2 *= endDis / config_.slowDwnDisThre;

        // 倾斜减速处理
        float joySpeed3 = joySpeed2;
        if (odomTime < slowInitTime + config_.slowTime1 && slowInitTime > 0)
            joySpeed3 *= config_.slowRate1;
        else if (odomTime < slowInitTime + config_.slowTime1 + config_.slowTime2 && slowInitTime > 0)
            joySpeed3 *= config_.slowRate2;

        // 若夹角小于阈值且再停止范围外则正常加减速
        if (fabs(dirDiff) < config_.dirDiffThre && dis > config_.stopDisThre) {
            if (vehicleSpeed < joySpeed3) vehicleSpeed += config_.maxAccel / 100.0;
            else if (vehicleSpeed > joySpeed3) vehicleSpeed -= config_.maxAccel / 100.0;
            rotinit = true;
            config_.dirDiffThre = 0.1;
        }
        // 若夹角大于阈值，则先考虑转弯再前进
        else {
            if (vehicleSpeed > 0) vehicleSpeed -= config_.maxAccel / 100.0;
            else if (vehicleSpeed < 0) vehicleSpeed += config_.maxAccel / 100.0;
        }

        // 倾斜停止处理
        if (odomTime < stopInitTime + config_.stopTime && stopInitTime > 0) {
            vehicleSpeed = 0;
            vehicleYawRate = 0;
        }

        // 改变速度输出频率
        pubSkipCount--;
        if (pubSkipCount < 0) {
            // 发布用于驱动的规划速度信息
            if (fabs(vehicleSpeed) <= config_.maxAccel / 100.0)
                cmd_vel.linear.x = 0;
            else
                cmd_vel.linear.x = vehicleSpeed;

            if (fabs(vehicleYawRate) <= 0.01)
                cmd_vel.angular.z = 0;
            else
                cmd_vel.angular.z = vehicleYawRate;

            // 停止信息速度清零
            if (safetyStop == 1 || getCurrentTime() - odometryTime > 0.5) {
                cmd_vel.linear.x = 0;
                cmd_vel.angular.z = 0;
            }

            // 发布速度命令
            if (speedPublishCallback_) {
                speedPublishCallback_(std::make_shared<TwistData>(cmd_vel));
            }

            pubSkipCount = config_.pubSkipNum;
        }
    }
}

// 打印状态
void PathFollower::printStatus() {
    std::cout << "PathFollower Status:" << std::endl;
    std::cout << "  Initialized: " << (initialized_ ? "Yes" : "No") << std::endl;
    std::cout << "  Running: " << (running_ ? "Yes" : "No") << std::endl;
    std::cout << "  Vehicle Position: (" << vehicleX << ", " << vehicleY << ", " << vehicleZ << ")" << std::endl;
    std::cout << "  Vehicle Orientation: " << vehicleYaw * 180.0 / PI << " deg" << std::endl;
    std::cout << "  Goal Position: (" << goalX << ", " << goalY << ", " << goalZ << ")" << std::endl;
    std::cout << "  Current Speed: " << vehicleSpeed << " m/s" << std::endl;
    std::cout << "  Current Yaw Rate: " << vehicleYawRate * 180.0 / PI << " deg/s" << std::endl;
    std::cout << "  Path Valid: " << (isPathValid() ? "Yes" : "No") << std::endl;
    std::cout << "  Navigation Started: " << (nav_start ? "Yes" : "No") << std::endl;
    std::cout << "  Adjust Mode: " << (adjustmode ? "Yes" : "No") << std::endl;
    std::cout << "  Safety Stop: " << safetyStop << std::endl;
    std::cout << "  Distance to Goal: " << getDistanceToGoal() << " m" << std::endl;
}

} // namespace path_follower
