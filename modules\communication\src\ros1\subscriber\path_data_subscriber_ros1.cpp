#include "subscriber/path_data_subscriber_ros1.h"

#if COMMUNICATION_TYPE == ROS1
namespace communication::ros1 {
     
PathDataSubscriberRos1::PathDataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size)
    : PathDataSubscriberBase(topic, max_buffer_size), nh_(nh) {
    subscriber_ = nh_.subscribe(topic, max_buffer_size, &PathDataSubscriberRos1::PathDataCallbackRos1, this);
}

// Callback function for path data
void PathDataSubscriberRos1::PathDataCallbackRos1(const nav_msgs::Path::ConstPtr &msg) {
    // Handle incoming path data message
    PathData path_data;
    int pathSize = msg->poses.size();
    path_data.poses_.resize(pathSize);

    path_data.time_ = msg->header.stamp.toSec(); // Get the timestamp from the message

    for (int i = 0; i < pathSize; ++i) {
        path_data.poses_[i].position[0] = msg->poses[i].pose.position.x; // Extract position data
        path_data.poses_[i].position[1] = msg->poses[i].pose.position.y; // Extract position data
        path_data.poses_[i].position[2] = msg->poses[i].pose.position.z; // Extract position data

        path_data.poses_[i].orientation[0] = msg->poses[i].pose.orientation.x; // Extract orientation data
        path_data.poses_[i].orientation[1] = msg->poses[i].pose.orientation.y; // Extract orientation data
        path_data.poses_[i].orientation[2] = msg->poses[i].pose.orientation.z; // Extract orientation data
        path_data.poses_[i].orientation[3] = msg->poses[i].pose.orientation.w; // Extract orientation data
   
    }

     // 将转换后的数据存入缓冲区
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    data_buffer_.push_back(path_data);
    // 检查缓冲区是否超过最大大小
    if (data_buffer_.size() > max_buffer_size_) {
        data_buffer_.pop_front(); // 如果缓冲区已满，删除最旧的数据
    }

}

}// namespace communication::ros1

#endif // COMMUNICATION_TYPE == ROS1