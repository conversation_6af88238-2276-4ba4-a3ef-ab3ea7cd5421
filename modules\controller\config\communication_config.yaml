# Controller Module Communication Configuration
# 控制器模块通信配置文件

# 通信类型
communication_type: "ROS1"

# 模块名称
module_name: "controller"

# 话题配置
topics:
  # 输入话题
  input:
    # 里程计话题
    odometry: "/Odometry"
    
    # 目标位姿话题
    goal: "/move_base_simple/goal"
    web_goal: "/web_goal"
    
    # 模式控制话题
    mode: "/calibration_mode"
    
    # 速度命令话题
    cmd_vel: "/cmd_vel"
    twist_data: "/twist/data"
    
    # 地形点云话题
    terrain_cloud: "/perception/output"
    
  # 输出话题
  output:
    # 速度命令话题
    cmd_vel_raw: "/cmd_vel_raw"
    cmd_vel_smooth: "/cmd_vel_smooth"
    
    # 停止命令话题
    emergency_stop: "/emergency_stop"
    inner_emergency_stop: "/inner_emergency_stop"
    
    # 模式状态话题
    calibration_mode_status: "/calibration_mode_status"
    
    # 调试话题
    debug_twist: "/debug/twist"
    debug_odometry: "/debug/odometry"

# 坐标系配置
frames:
  map: "map"
  odom: "odom"
  base_link: "base_link"
  camera_link: "camera_link"
  imu_link: "imu_link"
  lidar_link: "lidar_link"

# 缓冲区配置
buffer:
  # 默认缓冲区大小
  default_size: 10
  
  # 各话题缓冲区大小
  sizes:
    odometry: 50
    goal: 5
    cmd_vel: 10
    twist_data: 10
    terrain_cloud: 5

# 发布频率配置
publish_rate:
  # 默认发布频率 (Hz)
  default: 20
  
  # 各话题发布频率
  rates:
    cmd_vel_smooth: 50
    emergency_stop: 10
    calibration_mode_status: 5
    debug_twist: 20
    debug_odometry: 20

# 超时配置
timeout:
  # 默认超时时间 (秒)
  default: 1.0
  
  # 各话题超时时间
  times:
    odometry: 0.5
    goal: 5.0
    cmd_vel: 1.0
    terrain_cloud: 2.0

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  
  # 是否启用调试日志
  debug: false
  
  # 日志文件路径
  log_file: "/tmp/controller_communication.log" 