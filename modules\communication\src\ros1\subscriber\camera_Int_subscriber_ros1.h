#pragma once

#if COMMUNICATION_TYPE == ROS1
#include <ros/ros.h>

#include "subscriber_base.h"
namespace communication::ros1 {
class CameraIntSubscriberRos1 : public CameraIntSubscriberBase {
 public:
  CameraIntSubscriberRos1(ros::NodeHandle &nh, const std::string &topic,
                               size_t max_buffer_size = 10) : CameraIntSubscriberBase(topic, max_buffer_size), nh_(nh) {
    subscriber_ = nh_.subscribe(topic, max_buffer_size, &CameraIntSubscriberRos1::CameraIntCallbackRos1, this); 
  }

  ~CameraIntSubscriberRos1() = default;

  void CameraIntCallbackRos1(
      const sensor_msgs::CameraInfo::ConstPtr &camera_int_msg){
    // Convert the ROS message to CameraInfo and store it in the buffer
    CameraInfo camera_info;
    camera_info.width = camera_int_msg->width;
    camera_info.height = camera_int_msg->height;
    camera_info.fx = camera_int_msg->K[0];  
    camera_info.fy = camera_int_msg->K[4];
    camera_info.ppx = camera_int_msg->K[2];
    camera_info.ppy = camera_int_msg->K[5];
    camera_info.valid = true; // Assuming the camera info is valid, you can adjust this based on your requirements

    // Store the camera_info in the data buffer
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    data_buffer_.push_back(camera_info);
    if (data_buffer_.size() > max_buffer_size_) {
      data_buffer_.pop_front(); // Remove oldest data if buffer exceeds max size
    }
  }

 private:
  ros::NodeHandle &nh_;
  ros::Subscriber subscriber_;
};

} // namespace communication::ros1

#endif // COMMUNICATION_TYPE == ROS1