#include "publisher/camera_data_publisher_ros1.h"
// #include <pcl_conversions/pcl_conversions.h>

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1{

ImageDataPublisherRos1::ImageDataPublisherRos1(image_transport::ImageTransport &it, 
                                                const std::string &topic,
                                                const std::string& pixel_type,
                                                size_t max_buffer_size )
            : ImageDataPublisherBase(topic, max_buffer_size), it_(it), pixel_type_(pixel_type)
{
    publisher_ = it_.advertise(topic, max_buffer_size);
}

}   // namespace communication::ros1{

#endif
    