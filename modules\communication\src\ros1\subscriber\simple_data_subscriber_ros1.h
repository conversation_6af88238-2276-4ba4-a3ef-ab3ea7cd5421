/**
 * @file simple_data_subscriber_ros1.h
 * @brief This file contains the implementation of a simple data subscriber for ROS1.
 * It provides a template class for subscribing to different data types (int, double, string, bool) using ROS1 messages.
 */
#pragma once

#if COMMUNICATION_TYPE == ROS1

#include <ros/ros.h>

#include <std_msgs/Int32.h>
#include <std_msgs/Float32.h>
#include <std_msgs/Float64.h>
#include <std_msgs/String.h>
#include <std_msgs/Bool.h>

#include "subscriber_base.h"

namespace communication::ros1{

template<typename T, typename T_MSG>
class SimpleDataSubscriberRos1 :  public SubscriberBase<T>{
    public:
        SimpleDataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size = 100)
            : SubscriberBase<T>(topic, max_buffer_size), nh_(nh)
        {
            // Initialize the ROS subscriber with the specified topic and queue size
            subscriber_ = nh_.subscribe(topic, max_buffer_size, &SimpleDataSubscriberRos1::SimpleDataCallBackRos1, this);
        }

        ~SimpleDataSubscriberRos1() = default;

        void SimpleDataCallBackRos1(const typename T_MSG::ConstPtr &msg){
            SubscriberBase<T>::data_buffer_.push_back(msg->data); // Assuming T_MSG has a field 'data' that holds the value of type T
            if(SubscriberBase<T>::data_buffer_.size() > SubscriberBase<T>::max_buffer_size_) {
                SubscriberBase<T>::data_buffer_.pop_front(); // Remove the oldest data if buffer is full
            }
        }

    private:
        ros::NodeHandle& nh_;
        ros::Subscriber subscriber_;
    };

using IntDataSubscriberRos1 = SimpleDataSubscriberRos1<int, std_msgs::Int32>;
using DoubleDataSubscriberRos1 = SimpleDataSubscriberRos1<double, std_msgs::Float64>;
using StringDataSubscriberRos1 = SimpleDataSubscriberRos1<std::string, std_msgs::String>;
using BoolDataSubscriberRos1 = SimpleDataSubscriberRos1<bool, std_msgs::Bool>;
using FloatDataSubscriberRos1 = SimpleDataSubscriberRos1<float, std_msgs::Float32>;
} // namespace communication::ros1{

#endif
