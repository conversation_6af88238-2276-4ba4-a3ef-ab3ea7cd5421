#include "publisher/map_data_publisher_ros1.h"

#if COMMUNICATION_TYPE == ROS1
namespace communication::ros1 {

MapDataPublisherRos1::MapDataPublisherRos1(ros::NodeHandle& nh, const std::string& topic, size_t max_buffer_size)
    : OccupancyGridPublisherBase(topic, max_buffer_size), nh_(nh) {
    publisher_ = nh_.advertise<nav_msgs::OccupancyGrid>(topic, max_buffer_size);
}

} // namespace communication::ros1
#endif // COMMUNICATION_TYPE == ROS1 