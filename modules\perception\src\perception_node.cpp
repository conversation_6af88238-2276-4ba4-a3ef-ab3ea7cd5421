/*
 * @Author: greymaner dd
 * @Date: 2025-06-24 17:01:15
 * @LastEditors: greymaner dd
 * @LastEditTime: 2025-06-24 17:11:06
 * @FilePath: /cx-fusion-core-dc200/modules/perception/src/perception_node.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#include <memory>
#include <thread>
#include <iomanip>
#include "perception.h"
#include "communication.h"

std::shared_ptr<communication::CloudDataSubscriberBase>   lidar_pointcloud_subcriber_;
std::shared_ptr<communication::CloudDataPublisherBase>    output_pointcloud_publisher_;

std::shared_ptr<communication::ImageDataSubscriberBase>   color_image_subcriber_;
std::shared_ptr<communication::ImageDataSubscriberBase>   depth_image_subcriber_;


std::shared_ptr<communication::CameraIntSubscriberBase>   color_camera_info_subcriber_;
std::shared_ptr<communication::CameraIntSubscriberBase>   depth_camera_info_subcriber_;
std::shared_ptr<communication::CameraExtSubscriberBase>   depth2color_extrinsics_subcriber_;

std::unique_ptr<perception::Perception>                   perception_;

#ifdef OUTPUT_DEBUG
  std::shared_ptr<communication::CloudDataPublisherBase>    depth_pointcloud_publisher_;
  std::shared_ptr<communication::ImageDataPublisherBase>    segment_image_publisher_;
  std::shared_ptr<communication::ImageDataPublisherBase>    mask_image_publisher_;
  std::shared_ptr<communication::ImageDataPublisherBase>    preject_image_publisher_;
  
#endif

// 数据接收线程
void DataReceiveThread() {


  while (true) {
     // Example usage of subscribers and publishers
    
    if (!lidar_pointcloud_subcriber_->IsBufferEmpty()) {
      auto cloud_data = lidar_pointcloud_subcriber_->GetBuffer();
      perception_->PushCloud(cloud_data.front().cloud_ptr);
    }

    if (!color_image_subcriber_->IsBufferEmpty()) {
      cv::Mat color_image = color_image_subcriber_->GetBuffer().front().image;
      perception_->PushRgbImage(color_image);
    }

    if (!color_camera_info_subcriber_->IsBufferEmpty()) {
      auto camera_info = color_camera_info_subcriber_->GetBuffer().front();
      perception_->SetRgbIntrinsic(camera_info);
    }

    if (!depth_image_subcriber_->IsBufferEmpty()) {
      cv::Mat depth_image = depth_image_subcriber_->GetBuffer().front().image;
      perception_->PushDepthImage(depth_image);
    }

    if (!depth_camera_info_subcriber_->IsBufferEmpty()) {
      auto camera_info = depth_camera_info_subcriber_->GetBuffer().front();
      perception_->SetDepthIntrinsic(camera_info);
    }

    if (!depth2color_extrinsics_subcriber_->IsBufferEmpty()) {
      auto transform = depth2color_extrinsics_subcriber_->GetBuffer().front();
      perception_->SetDepth2RgbTransform(transform);
    }
  
    // Simulate some work being done in this thread
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    
  }
}


void OutputCallback(perception::PerceptionOut output){
  if(output.result){
    communication::CloudData cloud;
    cloud.cloud_ptr=output.result;
    output_pointcloud_publisher_->Publish(cloud);
  }

#ifdef OUTPUT_DEBUG
  if( !output.debug_info.mask.empty()){
    ImageData  image_data;
    image_data.image=output.debug_info.mask*80;
    mask_image_publisher_->Publish(image_data);
  }

  if( !output.debug_info.yolo_segment_image.empty()){
    ImageData  image_data;
    image_data.image=output.debug_info.yolo_segment_image;
    segment_image_publisher_->Publish(image_data);
  }

  if( !output.debug_info.repreject_image.empty()){
    ImageData  image_data;
    image_data.image=output.debug_info.repreject_image;
    preject_image_publisher_->Publish(image_data);
  }

  if(output.debug_info.depth_cloud){
    communication::CloudData depth_cloud;
    depth_cloud.cloud_ptr=output.debug_info.depth_cloud;
    depth_pointcloud_publisher_->Publish(depth_cloud);
  }
  
#endif
}

int main(int argc, char **argv) {

  std::string config_file_path = "/home/<USER>/modules/perception/config/perception.yaml";

  
  perception_ = std::make_unique<perception::Perception>(config_file_path);
  perception_->SetOutputCallback(OutputCallback);
  auto communication_ptr = std::make_shared<communication::Communication>("perception");

  communication_ptr->Initialize(config_file_path);
  lidar_pointcloud_subcriber_         = communication_ptr->CreateCloudDataSubscriber("/cloud_registered_body");
  color_image_subcriber_              = communication_ptr->CreateImageDataSubscriber("/camera/color/image_raw","bgr8");
  color_camera_info_subcriber_        = communication_ptr->CreateCameraIntSubscriber("/camera/color/camera_info");
  depth_image_subcriber_              = communication_ptr->CreateImageDataSubscriber("/camera/depth/image_rect_raw","16UC1");
  depth_camera_info_subcriber_        = communication_ptr->CreateCameraIntSubscriber("/camera/depth/camera_info");
  depth2color_extrinsics_subcriber_   = communication_ptr->CreateCameraExtSubscriber("/camera/extrinsics/depth_to_color");

  output_pointcloud_publisher_=communication_ptr->CreateCloudDataPublisher("/perception/output");
  
#ifdef OUTPUT_DEBUG
  depth_pointcloud_publisher_   = communication_ptr->CreateCloudDataPublisher("/perception/depth_cloud");
  segment_image_publisher_      = communication_ptr->CreateImageDataPublisher("/perception/segment","bgr8");
  mask_image_publisher_         = communication_ptr->CreateImageDataPublisher("/perception/mask","mono8");
  preject_image_publisher_      = communication_ptr->CreateImageDataPublisher("/perception/preject","bgr8");
#endif

  // Start the data receive thread
  std::thread data_receive_thread(&DataReceiveThread);
  data_receive_thread.detach(); // Detach the thread to run independently

  //std::thread data_send_thread(&DataSendThread);
  //data_send_thread.detach(); // Detach the thread to run independently
  
  communication_ptr->Run();
  return 0;
}
