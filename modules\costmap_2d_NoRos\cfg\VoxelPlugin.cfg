#!/usr/bin/env python

from dynamic_reconfigure.parameter_generator_catkin import ParameterGenerator, bool_t, double_t, int_t

gen = ParameterGenerator()

gen.add("enabled", bool_t, 0, "Whether to use this plugin or not", True)
gen.add("footprint_clearing_enabled", bool_t, 0, "Whether to clear the robot's footprint of lethal obstacles", True)
gen.add("max_obstacle_height", double_t, 0, "Max Obstacle Height", 2.0, 0, 50)
gen.add("origin_z", double_t, 0, "The z origin of the map in meters.", 0, 0)
gen.add("z_resolution", double_t, 0, "The z resolution of the map in meters/cell.", 0.2, 0, 50)
gen.add("z_voxels", int_t, 0, "The number of voxels to in each vertical column.", 10, 0, 16)
gen.add("unknown_threshold", int_t, 0, 'The number of unknown cells allowed in a column considered to be known', 15, 0, 16)
gen.add("mark_threshold", int_t, 0, 'The maximum number of marked cells allowed in a column considered to be free', 0, 0, 16)

combo_enum = gen.enum([ gen.const("Overwrite", int_t, 0, "b"),
                        gen.const("Maximum",   int_t, 1, "a") ],
                      "Method for combining layers enum")

gen.add("combination_method", int_t, 0, "Method for combining two layers", 1, 0, 2, edit_method=combo_enum)

exit(gen.generate("costmap_2d", "costmap_2d", "VoxelPlugin"))
