#include "publisher/pose_data_publisher_ros1.h"

#if COMMUNICATION_TYPE == ROS1
namespace communication::ros1 {
PoseDataPublisherRos1::PoseDataPublisherRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size)
    : PoseDataPublisherBase(topic, max_buffer_size), nh_(nh) {
    publisher_ = nh_.advertise<geometry_msgs::PoseWithCovarianceStamped>(topic, max_buffer_size, true);//保存最后一条发布的消息
}

} // namespace communication::ros1

#endif // COMMUNICATION_TYPE == ROS1