# perception/CMakeLists.txt
cmake_minimum_required(VERSION 3.5)
project(decision)

# 全局设置库文件输出目录
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)

#set(CMAKE_BUILD_TYPE "Release")
set(CMAKE_BUILD_TYPE "Debug")
set(CMAKE_CXX_STANDARD 14)
#set(CMAKE_CXX_FLAGS "-std=c++17 -march=native")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall")

# find_package(OpenCV REQUIRED)

include_directories(
    include
    # ${OpenCV_INCLUDE_DIRS}
    # ${CMAKE_CURRENT_SOURCE_DIR}/../communication/include 
)

add_library(${PROJECT_NAME}_core
  src/decision.cpp
)

target_include_directories(${PROJECT_NAME}_core
  PRIVATE
  ${CMAKE_CURRENT_SOURCE_DIR}/../communication/include 
  ${CMAKE_CURRENT_SOURCE_DIR}/../common/include
)


add_executable(${PROJECT_NAME}_node
  src/decision_node.cpp
)

link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../communication/lib
                ${CMAKE_CURRENT_SOURCE_DIR}/../common/lib

)

target_link_libraries(${PROJECT_NAME}_node 
                    common_lib
                    communication_core
                    ${PROJECT_NAME}_core   
)




