#include "image_segment_rknn.h"

using namespace perception;

ImageSegmentRknn::ImageSegmentRknn():
    ImageSegmentImpl(){
        yolo_=std::make_unique<Yolov8Rknn>();
}
ImageSegmentRknn::~ImageSegmentRknn(){

}
bool ImageSegmentRknn::Init(const std::string& model_path){
    return yolo_->Init(model_path);
}
bool ImageSegmentRknn::Inference(cv::Mat src,float box_thresh,float nms_thresh,SegmentDetectResult& out) {
    
    input_source source;
    source.image = src;
    source.box_thresh = box_thresh;
    source.nms_thresh = nms_thresh;

    segment_out segment;
    if(yolo_->Seg(source, segment)){
        out.mask = segment.mask;
        for(int i=0;i<segment.boxes.size();++i){
            ObjectDetectResult  result;
            result.cls_id = segment.boxes[i].cls_id;
            result.prop = segment.boxes[i].prop;
            result.box.bottom = segment.boxes[i].box.bottom;
            result.box.left = segment.boxes[i].box.left;
            result.box.right = segment.boxes[i].box.right;
            result.box.top = segment.boxes[i].box.top;

            out.boxes.push_back(result);
        }
        return true;
    }else{
        out.mask=cv::Mat::zeros(src.cols,src.rows,CV_8UC1);
        return false;
    }
    
}   