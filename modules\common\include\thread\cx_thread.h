#ifndef _CX_THREAD_H_
#define _CX_THREAD_H_

#include "cx_event.h"
#include "cx_pubhead.h"
#include "cx_rwlock.h"
#include "cx_work.h"

#ifdef LINUX
#include <pthread.h>
#include <unistd.h>
#endif
namespace common_lib {
enum CM_THREAD_STATE {
  CM_TS_TERMINATED = 0,
  CM_TS_WAITING,
  CM_TS_RUNNING,
};

class CXThread {
 public:
  CXThread();
  ~CXThread();

 public:
  cx_int AssignWork(CXWork *pWork);
  ThreadProc *GetThreadProc();
  LPVOID GetContext();

  cx_int Create(ThreadProc pfnThreadProc, LPVOID const pContext);
#ifdef LINUX
#endif

 public:
  cx_int Start();
  cx_int Pause();
  cx_int Resume();
  cx_int Stop();

 public:
  cx_int SetState(CM_THREAD_STATE eState);
  CM_THREAD_STATE GetState();

  CXWork *GetWork();

  const cx_bool IsTerminate();
  cx_int Reset();
  cx_int FinishWork();

 public:
  cx_int GetPriority(int &priority) const;
  cx_int SetPriority(const int iPriority);

 private:
  CXWork *work_ptr_;
  CM_THREAD_STATE state_;
  CXEvent terminate_event_;
  CXEvent finished_event_;
  CXEvent run_event_;

#ifdef LINUX

  pthread_t m_pthread;
  pthread_attr_t m_attr;

#endif
};

class CMThreadSet : public std::set<CXThread *>, public CXRWLock {};

cx_int SleepS(cx_dword milliseconds);
cx_int SleepMS(cx_dword mlliseconds);
cx_int SleepUS(cx_dword milliseconds);
}  // namespace common_lib
#endif  // !_CX_THREAD_H_
