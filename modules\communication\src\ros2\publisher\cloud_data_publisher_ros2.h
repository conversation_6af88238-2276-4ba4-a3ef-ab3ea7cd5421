#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <pcl_conversions/pcl_conversions.h>

#include "ros2/core/publisher_base_ros2.h"

namespace communication::ros2 {

class CloudDataPublisherRos2 : public PublisherBaseRos2<CloudData, sensor_msgs::msg::PointCloud2> {
public:
    CloudDataPublisherRos2(rclcpp::Node::SharedPtr node, 
                          const std::string& topic,
                          const std::string& frame_id = "map",
                          size_t max_buffer_size = 10)
        : PublisherBaseRos2<CloudData, sensor_msgs::msg::PointCloud2>(node, topic, max_buffer_size),
          frame_id_(frame_id) {}

protected:
    virtual void ToMsg() override {
        pcl::toROSMsg(*this->data_.cloud_ptr, this->msg_);
        this->msg_.header.stamp = rclcpp::Time(static_cast<int64_t>(this->data_.time * 1e9));
        this->msg_.header.frame_id = frame_id_;
    }

private:
    std::string frame_id_;
};

} // namespace communication::ros2

#endif
