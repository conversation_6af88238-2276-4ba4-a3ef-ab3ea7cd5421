// Generated by gencpp from file nav_msgs/Odometry.msg
// DO NOT EDIT!


#ifndef NAV_MSGS_MESSAGE_ODOMETRY_H
#define NAV_MSGS_MESSAGE_ODOMETRY_H


#include <string>
#include <vector>
#include <map>

// #include <ros/types.h>
// #include <ros/serialization.h>
// #include <ros/builtin_message_traits.h>
// #include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/PoseWithCovariance.h>
#include <geometry_msgs/TwistWithCovariance.h>
#include <boost/shared_ptr.hpp>

namespace nav_msgs
{
template <class ContainerAllocator>
struct Odometry_
{
  typedef Odometry_<ContainerAllocator> Type;

  Odometry_()
    : header()
    , child_frame_id()
    , pose()
    , twist()  {
    }
  Odometry_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , child_frame_id(_alloc)
    , pose(_alloc)
    , twist(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::basic_string<char, std::char_traits<char>, typename ContainerAllocator::template rebind<char>::other >  _child_frame_id_type;
  _child_frame_id_type child_frame_id;

   typedef  ::geometry_msgs::PoseWithCovariance_<ContainerAllocator>  _pose_type;
  _pose_type pose;

   typedef  ::geometry_msgs::TwistWithCovariance_<ContainerAllocator>  _twist_type;
  _twist_type twist;





  typedef boost::shared_ptr< ::nav_msgs::Odometry_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::nav_msgs::Odometry_<ContainerAllocator> const> ConstPtr;

}; // struct Odometry_

typedef ::nav_msgs::Odometry_<std::allocator<void> > Odometry;

typedef boost::shared_ptr< ::nav_msgs::Odometry > OdometryPtr;
typedef boost::shared_ptr< ::nav_msgs::Odometry const> OdometryConstPtr;

// constants requiring out of line definition



//template<typename ContainerAllocator>
//std::ostream& operator<<(std::ostream& s, const ::nav_msgs::Odometry_<ContainerAllocator> & v)
//{
//ros::message_operations::Printer< ::nav_msgs::Odometry_<ContainerAllocator> >::stream(s, "", v);
//return s;
//}

} // namespace nav_msgs

#if 0
namespace ros
{
namespace message_traits
{



// BOOLTRAITS {'IsFixedSize': False, 'IsMessage': True, 'HasHeader': True}
// {'nav_msgs': ['/tmp/binarydeb/ros-kinetic-nav-msgs-1.12.6/msg', '/tmp/binarydeb/ros-kinetic-nav-msgs-1.12.6/obj-x86_64-linux-gnu/devel/share/nav_msgs/msg'], 'std_msgs': ['/opt/ros/kinetic/share/std_msgs/cmake/../msg'], 'actionlib_msgs': ['/opt/ros/kinetic/share/actionlib_msgs/cmake/../msg'], 'geometry_msgs': ['/opt/ros/kinetic/share/geometry_msgs/cmake/../msg']}

// !!!!!!!!!!! ['__class__', '__delattr__', '__dict__', '__doc__', '__eq__', '__format__', '__getattribute__', '__hash__', '__init__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_parsed_fields', 'constants', 'fields', 'full_name', 'has_header', 'header_present', 'names', 'package', 'parsed_fields', 'short_name', 'text', 'types']




template <class ContainerAllocator>
struct IsFixedSize< ::nav_msgs::Odometry_<ContainerAllocator> >
 : FalseType
 { };

template <class ContainerAllocator>
struct IsFixedSize< ::nav_msgs::Odometry_<ContainerAllocator> const>
 : FalseType
 { };

template <class ContainerAllocator>
struct IsMessage< ::nav_msgs::Odometry_<ContainerAllocator> >
 : TrueType
 { };

template <class ContainerAllocator>
struct IsMessage< ::nav_msgs::Odometry_<ContainerAllocator> const>
 : TrueType
 { };

template <class ContainerAllocator>
struct HasHeader< ::nav_msgs::Odometry_<ContainerAllocator> >
 : TrueType
 { };

template <class ContainerAllocator>
struct HasHeader< ::nav_msgs::Odometry_<ContainerAllocator> const>
 : TrueType
 { };


template<class ContainerAllocator>
struct MD5Sum< ::nav_msgs::Odometry_<ContainerAllocator> >
{
 static const char* value()
 {
   return "cd5e73d190d741a2f92e81eda573aca7";
 }

 static const char* value(const ::nav_msgs::Odometry_<ContainerAllocator>&) { return value(); }
 static const uint64_t static_value1 = 0xcd5e73d190d741a2ULL;
 static const uint64_t static_value2 = 0xf92e81eda573aca7ULL;
};

template<class ContainerAllocator>
struct DataType< ::nav_msgs::Odometry_<ContainerAllocator> >
{
 static const char* value()
 {
   return "nav_msgs/Odometry";
 }

 static const char* value(const ::nav_msgs::Odometry_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::nav_msgs::Odometry_<ContainerAllocator> >
{
 static const char* value()
 {
   return "# This represents an estimate of a position and velocity in free space.  \n\
# The pose in this message should be specified in the coordinate frame given by header.frame_id.\n\
# The twist in this message should be specified in the coordinate frame given by the child_frame_id\n\
Header header\n\
string child_frame_id\n\
geometry_msgs/PoseWithCovariance pose\n\
geometry_msgs/TwistWithCovariance twist\n\
\n\
================================================================================\n\
MSG: std_msgs/Header\n\
# Standard metadata for higher-level stamped data types.\n\
# This is generally used to communicate timestamped data \n\
# in a particular coordinate frame.\n\
# \n\
# sequence ID: consecutively increasing ID \n\
uint32 seq\n\
#Two-integer timestamp that is expressed as:\n\
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n\
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n\
# time-handling sugar is provided by the client library\n\
time stamp\n\
#Frame this data is associated with\n\
# 0: no frame\n\
# 1: global frame\n\
string frame_id\n\
\n\
================================================================================\n\
MSG: geometry_msgs/PoseWithCovariance\n\
# This represents a pose in free space with uncertainty.\n\
\n\
Pose pose\n\
\n\
# Row-major representation of the 6x6 covariance matrix\n\
# The orientation parameters use a fixed-axis representation.\n\
# In order, the parameters are:\n\
# (x, y, z, rotation about X axis, rotation about Y axis, rotation about Z axis)\n\
float64[36] covariance\n\
\n\
================================================================================\n\
MSG: geometry_msgs/Pose\n\
# A representation of pose in free space, composed of position and orientation. \n\
Point position\n\
Quaternion orientation\n\
\n\
================================================================================\n\
MSG: geometry_msgs/Point\n\
# This contains the position of a point in free space\n\
float64 x\n\
float64 y\n\
float64 z\n\
\n\
================================================================================\n\
MSG: geometry_msgs/Quaternion\n\
# This represents an orientation in free space in quaternion form.\n\
\n\
float64 x\n\
float64 y\n\
float64 z\n\
float64 w\n\
\n\
================================================================================\n\
MSG: geometry_msgs/TwistWithCovariance\n\
# This expresses velocity in free space with uncertainty.\n\
\n\
Twist twist\n\
\n\
# Row-major representation of the 6x6 covariance matrix\n\
# The orientation parameters use a fixed-axis representation.\n\
# In order, the parameters are:\n\
# (x, y, z, rotation about X axis, rotation about Y axis, rotation about Z axis)\n\
float64[36] covariance\n\
\n\
================================================================================\n\
MSG: geometry_msgs/Twist\n\
# This expresses velocity in free space broken into its linear and angular parts.\n\
Vector3  linear\n\
Vector3  angular\n\
\n\
================================================================================\n\
MSG: geometry_msgs/Vector3\n\
# This represents a vector in free space. \n\
# It is only meant to represent a direction. Therefore, it does not\n\
# make sense to apply a translation to it (e.g., when applying a \n\
# generic rigid transformation to a Vector3, tf2 will only apply the\n\
# rotation). If you want your data to be translatable too, use the\n\
# geometry_msgs/Point message instead.\n\
\n\
float64 x\n\
float64 y\n\
float64 z\n\
";
 }

 static const char* value(const ::nav_msgs::Odometry_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

 template<class ContainerAllocator> struct Serializer< ::nav_msgs::Odometry_<ContainerAllocator> >
 {
   template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
   {
     stream.next(m.header);
     stream.next(m.child_frame_id);
     stream.next(m.pose);
     stream.next(m.twist);
   }

   ROS_DECLARE_ALLINONE_SERIALIZER
 }; // struct Odometry_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::nav_msgs::Odometry_<ContainerAllocator> >
{
 template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::nav_msgs::Odometry_<ContainerAllocator>& v)
 {
   s << indent << "header: ";
   s << std::endl;
   Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
   s << indent << "child_frame_id: ";
   Printer<std::basic_string<char, std::char_traits<char>, typename ContainerAllocator::template rebind<char>::other > >::stream(s, indent + "  ", v.child_frame_id);
   s << indent << "pose: ";
   s << std::endl;
   Printer< ::geometry_msgs::PoseWithCovariance_<ContainerAllocator> >::stream(s, indent + "  ", v.pose);
   s << indent << "twist: ";
   s << std::endl;
   Printer< ::geometry_msgs::TwistWithCovariance_<ContainerAllocator> >::stream(s, indent + "  ", v.twist);
 }
};

} // namespace message_operations
} // namespace ros
#endif

#endif // NAV_MSGS_MESSAGE_ODOMETRY_H
