#ifndef _CX_RWLOCK_H_
#define _CX_RWLOCK_H_

#include "cx_pubhead.h"

#ifdef WIN32
// #include <process.h>
#include <windows.h>
#else
#include <pthread.h>
#endif
namespace common_lib {
class CXRWLock {
 public:
  CXRWLock();
  virtual ~CXRWLock();

 public:
  cx_int Initialize();
  cx_int Destroy();
  cx_int LockR();
  cx_int UnlockR();
  cx_int LockW();
  cx_int UnlockW();

 private:
#ifdef WIN32
  SRWLOCK m_srwLock;
#else
  pthread_rwlock_t rwlock_;
#endif  // WIN32
};

template <typename T>
class CXSharedData : public CXRWLock {
 public:
  T data_;
};
}  // namespace common_lib

#endif  // _CX_RWLOCK_H_
