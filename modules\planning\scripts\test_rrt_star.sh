#!/bin/bash

# RRT*全局规划器测试启动脚本
# 用于启动RRT*规划器测试节点

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# 默认配置
DEFAULT_CONFIG="config/test_config.yaml"
DEFAULT_BUILD_DIR="build_x86_64_release_ros1_cpu"
DEFAULT_LOG_DIR="logs"

# 函数定义
print_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --config FILE     指定配置文件 (默认: $DEFAULT_CONFIG)"
    echo "  -b, --build-dir DIR   指定构建目录 (默认: $DEFAULT_BUILD_DIR)"
    echo "  -l, --log-dir DIR     指定日志目录 (默认: $DEFAULT_LOG_DIR)"
    echo "  -r, --ros-test        仅运行ROS测试"
    echo "  -s, --sim-test        仅运行模拟测试"
    echo "  -v, --visualization   启用可视化"
    echo "  -d, --debug           启用调试输出"
    echo "  -t, --duration TIME   设置测试持续时间(秒)"
    echo "  -h, --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置运行完整测试"
    echo "  $0 -c my_config.yaml                  # 使用自定义配置文件"
    echo "  $0 -r -v                              # 仅运行ROS测试并启用可视化"
    echo "  $0 -s -t 600                          # 运行模拟测试，持续10分钟"
    echo "  $0 -d -l /tmp/test_logs              # 启用调试输出并指定日志目录"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查构建目录
    if [ ! -d "$BUILD_DIR" ]; then
        print_error "构建目录不存在: $BUILD_DIR"
        print_info "请先运行构建脚本: ./build.sh"
        exit 1
    fi
    
    # 检查可执行文件
    EXECUTABLE="$BUILD_DIR/modules/planning/rrt_star_test_node"
    if [ ! -f "$EXECUTABLE" ]; then
        print_error "可执行文件不存在: $EXECUTABLE"
        print_info "请先编译项目"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        print_warning "配置文件不存在: $CONFIG_FILE"
        print_info "将使用默认配置"
    fi
    
    print_success "依赖检查完成"
}

# 创建日志目录
create_log_dir() {
    if [ ! -d "$LOG_DIR" ]; then
        print_info "创建日志目录: $LOG_DIR"
        mkdir -p "$LOG_DIR"
    fi
}

# 设置环境变量
setup_environment() {
    print_info "设置环境变量..."
    
    # 设置ROS环境
    if [ -f "/opt/ros/noetic/setup.bash" ]; then
        source /opt/ros/noetic/setup.bash
        print_info "ROS Noetic 环境已加载"
    elif [ -f "/opt/ros/melodic/setup.bash" ]; then
        source /opt/ros/melodic/setup.bash
        print_info "ROS Melodic 环境已加载"
    else
        print_warning "未找到ROS环境，可能影响ROS话题通信"
    fi
    
    # 设置项目环境
    export LD_LIBRARY_PATH="$BUILD_DIR/modules/common/lib:$LD_LIBRARY_PATH"
    export LD_LIBRARY_PATH="$BUILD_DIR/modules/communication/lib:$LD_LIBRARY_PATH"
    
    print_success "环境变量设置完成"
}

# 运行测试
run_test() {
    print_info "启动RRT*规划器测试..."
    
    # 构建命令行参数
    CMD_ARGS=()
    
    if [ "$ROS_TEST_ONLY" = true ]; then
        CMD_ARGS+=("--ros-test-only")
    fi
    
    if [ "$SIM_TEST_ONLY" = true ]; then
        CMD_ARGS+=("--sim-test-only")
    fi
    
    if [ "$ENABLE_VISUALIZATION" = true ]; then
        CMD_ARGS+=("--enable-visualization")
    fi
    
    if [ "$ENABLE_DEBUG" = true ]; then
        CMD_ARGS+=("--enable-debug")
    fi
    
    if [ -n "$TEST_DURATION" ]; then
        CMD_ARGS+=("--duration" "$TEST_DURATION")
    fi
    
    # 设置日志文件
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    LOG_FILE="$LOG_DIR/rrt_star_test_${TIMESTAMP}.log"
    
    print_info "日志文件: $LOG_FILE"
    print_info "启动命令: $EXECUTABLE ${CMD_ARGS[*]}"
    
    # 运行测试
    cd "$PROJECT_ROOT"
    "$EXECUTABLE" "${CMD_ARGS[@]}" 2>&1 | tee "$LOG_FILE"
    
    EXIT_CODE=${PIPESTATUS[0]}
    
    if [ $EXIT_CODE -eq 0 ]; then
        print_success "测试完成"
    else
        print_error "测试失败，退出码: $EXIT_CODE"
    fi
    
    return $EXIT_CODE
}

# 分析结果
analyze_results() {
    print_info "分析测试结果..."
    
    # 查找最新的日志文件
    LATEST_LOG=$(ls -t "$LOG_DIR"/rrt_star_test_*.log 2>/dev/null | head -1)
    
    if [ -n "$LATEST_LOG" ]; then
        print_info "分析日志文件: $LATEST_LOG"
        
        # 提取关键信息
        echo ""
        echo "=== 测试结果摘要 ==="
        
        # 成功率
        SUCCESS_RATE=$(grep -o "成功率: [0-9.]*%" "$LATEST_LOG" | tail -1 || echo "成功率: 未知")
        echo "$SUCCESS_RATE"
        
        # 平均规划时间
        AVG_TIME=$(grep -o "平均规划时间: [0-9.]* 秒" "$LATEST_LOG" | tail -1 || echo "平均规划时间: 未知")
        echo "$AVG_TIME"
        
        # 平均节点数
        AVG_NODES=$(grep -o "平均节点数: [0-9]*" "$LATEST_LOG" | tail -1 || echo "平均节点数: 未知")
        echo "$AVG_NODES"
        
        # 平均路径长度
        AVG_LENGTH=$(grep -o "平均路径长度: [0-9.]* 米" "$LATEST_LOG" | tail -1 || echo "平均路径长度: 未知")
        echo "$AVG_LENGTH"
        
        # 错误信息
        ERROR_COUNT=$(grep -c "ERROR\|错误" "$LATEST_LOG" || echo "0")
        echo "错误数量: $ERROR_COUNT"
        
        # 警告信息
        WARNING_COUNT=$(grep -c "WARNING\|警告" "$LATEST_LOG" || echo "0")
        echo "警告数量: $WARNING_COUNT"
        
    else
        print_warning "未找到日志文件"
    fi
}

# 清理
cleanup() {
    print_info "清理资源..."
    
    # 停止相关进程
    pkill -f "rrt_star_test_node" 2>/dev/null || true
    
    print_success "清理完成"
}

# 主函数
main() {
    # 解析命令行参数
    CONFIG_FILE="$DEFAULT_CONFIG"
    BUILD_DIR="$DEFAULT_BUILD_DIR"
    LOG_DIR="$DEFAULT_LOG_DIR"
    ROS_TEST_ONLY=false
    SIM_TEST_ONLY=false
    ENABLE_VISUALIZATION=false
    ENABLE_DEBUG=false
    TEST_DURATION=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -b|--build-dir)
                BUILD_DIR="$2"
                shift 2
                ;;
            -l|--log-dir)
                LOG_DIR="$2"
                shift 2
                ;;
            -r|--ros-test)
                ROS_TEST_ONLY=true
                shift
                ;;
            -s|--sim-test)
                SIM_TEST_ONLY=true
                shift
                ;;
            -v|--visualization)
                ENABLE_VISUALIZATION=true
                shift
                ;;
            -d|--debug)
                ENABLE_DEBUG=true
                shift
                ;;
            -t|--duration)
                TEST_DURATION="$2"
                shift 2
                ;;
            -h|--help)
                print_usage
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                print_usage
                exit 1
                ;;
        esac
    done
    
    # 检查互斥选项
    if [ "$ROS_TEST_ONLY" = true ] && [ "$SIM_TEST_ONLY" = true ]; then
        print_error "不能同时指定 --ros-test 和 --sim-test"
        exit 1
    fi
    
    # 显示配置信息
    echo "=== RRT*规划器测试配置 ==="
    echo "配置文件: $CONFIG_FILE"
    echo "构建目录: $BUILD_DIR"
    echo "日志目录: $LOG_DIR"
    echo "ROS测试: $([ "$ROS_TEST_ONLY" = true ] && echo "仅ROS" || echo "全部")"
    echo "模拟测试: $([ "$SIM_TEST_ONLY" = true ] && echo "仅模拟" || echo "全部")"
    echo "可视化: $([ "$ENABLE_VISUALIZATION" = true ] && echo "启用" || echo "禁用")"
    echo "调试输出: $([ "$ENABLE_DEBUG" = true ] && echo "启用" || echo "禁用")"
    if [ -n "$TEST_DURATION" ]; then
        echo "测试持续时间: ${TEST_DURATION}秒"
    fi
    echo ""
    
    # 执行测试流程
    check_dependencies
    create_log_dir
    setup_environment
    
    # 设置信号处理
    trap cleanup EXIT
    trap 'print_info "收到中断信号，正在退出..."; exit 1' INT TERM
    
    # 运行测试
    run_test
    TEST_EXIT_CODE=$?
    
    # 分析结果
    analyze_results
    
    exit $TEST_EXIT_CODE
}

# 运行主函数
main "$@" 