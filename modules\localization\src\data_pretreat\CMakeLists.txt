add_library(${PROJECT_NAME}_pretreat SHARED
    IMU_Processing.cpp
    lidar_preprocess.cpp
)

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/
    ${CMAKE_CURRENT_SOURCE_DIR}/../common
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/eskf
    ${CMAKE_CURRENT_SOURCE_DIR}/../map_manager
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include

    ${CMAKE_CURRENT_SOURCE_DIR}/../../../communication/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../communication/include/data_types
)

target_link_libraries(${PROJECT_NAME}_pretreat
    ${PROJECT_NAME}_common    
    ${EIGEN3_LIBRARIES}
    ${PCL_LIBRARIES}
    #fmt::fmt
    common_lib
    communication_core
)