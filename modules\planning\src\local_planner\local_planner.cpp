#include "local_planner.h"
#include "common/common_types.h"

namespace local_planner {

#define PLOTPATHSET 1

// 构造函数
LocalPlanner::LocalPlanner(const std::string& config_path)
    : config_path_(config_path)
    , initialized_(false)
    , running_(false)
    , paused_(false)
    , nav_start(0)
    , info(1)
    , id(0)
    , goalX(0), goalY(0), goalZ(0)
    , targetX(0), targetY(0), targetZ(0), targetYaw(0)
    , joyDir(0)
    , newLaserCloud(false)
    , newTerrainCloud(false)
    , odomTime(0)
    , vehicleRoll(0), vehiclePitch(0), vehicleYaw(0)
    , vehicleX(0), vehicleY(0), vehicleZ(0)
    , init(false)
    , start_time(0), end_time(0)
    , location_failed(false)
    , laserCloudCount(0)
{
    // 初始化数组
    for (int i = 0; i < pathNum; i++) {
        pathList[i] = 0;
    }
    for (int i = 0; i < 36 * pathNum; i++) {
        clearPathList[i] = 0;
        pathPenaltyList[i] = 0;
    }
    for (int i = 0; i < 36 * groupNum; i++) {
        clearPathPerGroupScore[i] = 0;
    }
    
    // 初始化消息
    arrive_inf.data = 0;
    adjustmode.data = false;
    
    // 初始化点云指针
    initializePointClouds();
}

// 析构函数
LocalPlanner::~LocalPlanner() {
    stopPlanning();
}

// 初始化函数
bool LocalPlanner::initialize() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    
    if (initialized_) {
        return true;
    }
    
    try {
        // 加载配置
        if (!config_path_.empty()) {
            if (!loadConfig(config_path_)) {
                std::cerr << "Failed to load config from: " << config_path_ << std::endl;
                return false;
            }
        }
        
        // 初始化滤波器
        initializeFilters();
        
        // 初始化体素网格对应关系
        for (int i = 0; i < gridVoxelNum; i++) {
            correspondences[i].resize(0);
        }
        
        // 读取路径文件
        std::cout << "Reading path files..." << std::endl;
        readStartPaths();
        #if PLOTPATHSET == 1
        readPaths();
        #endif
        readPathList();
        readCorrespondences();
        
        initialized_ = true;
        std::cout << "LocalPlanner initialization complete." << std::endl;
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "LocalPlanner initialization failed: " << e.what() << std::endl;
        return false;
    }
}

// 加载配置
bool LocalPlanner::loadConfig(const std::string& config_path) {
    if (config_path.empty()) {
        std::cout << "Using default configuration." << std::endl;
        return true;
    }
    
    try {
        if (config_path.find(".yaml") != std::string::npos || 
            config_path.find(".yml") != std::string::npos) {
            return loadYamlConfig(config_path);
        } else {
            std::cerr << "Unsupported config file format. Only YAML is supported." << std::endl;
            return false;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Failed to load config: " << e.what() << std::endl;
        return false;
    }
}

// 加载YAML配置
bool LocalPlanner::loadYamlConfig(const std::string& config_path) {
    try {
        YAML::Node config = YAML::LoadFile(config_path);
        
        // 路径配置
        if (config["path_config"]) {
            auto path_config = config["path_config"];
            if (path_config["path_folder"]) {
                config_.pathFolder = path_config["path_folder"].as<std::string>();
            }
        }
        
        // 车辆配置
        if (config["vehicle_config"]) {
            auto vehicle_config = config["vehicle_config"];
            if (vehicle_config["length"]) config_.vehicleLength = vehicle_config["length"].as<double>();
            if (vehicle_config["width"]) config_.vehicleWidth = vehicle_config["width"].as<double>();
            if (vehicle_config["sensor_offset_x"]) config_.sensorOffsetX = vehicle_config["sensor_offset_x"].as<double>();
            if (vehicle_config["sensor_offset_y"]) config_.sensorOffsetY = vehicle_config["sensor_offset_y"].as<double>();
            if (vehicle_config["two_way_drive"]) config_.twoWayDrive = vehicle_config["two_way_drive"].as<bool>();
            if (vehicle_config["max_speed"]) config_.maxSpeed = vehicle_config["max_speed"].as<double>();
        }
        
        // 点云处理配置
        if (config["pointcloud_config"]) {
            auto pc_config = config["pointcloud_config"];
            if (pc_config["laser_voxel_size"]) config_.laserVoxelSize = pc_config["laser_voxel_size"].as<double>();
            if (pc_config["terrain_voxel_size"]) config_.terrainVoxelSize = pc_config["terrain_voxel_size"].as<double>();
            if (pc_config["use_terrain_analysis"]) config_.useTerrainAnalysis = pc_config["use_terrain_analysis"].as<bool>();
            if (pc_config["check_rot_obstacle"]) config_.checkRotObstacle = pc_config["check_rot_obstacle"].as<bool>();
            if (pc_config["adjacent_range"]) config_.adjacentRange = pc_config["adjacent_range"].as<double>();
            if (pc_config["obstacle_height_thre"]) config_.obstacleHeightThre = pc_config["obstacle_height_thre"].as<double>();
            if (pc_config["ground_height_thre"]) config_.groundHeightThre = pc_config["ground_height_thre"].as<double>();
            if (pc_config["cost_height_thre"]) config_.costHeightThre = pc_config["cost_height_thre"].as<double>();
            if (pc_config["cost_score"]) config_.costScore = pc_config["cost_score"].as<double>();
            if (pc_config["use_cost"]) config_.useCost = pc_config["use_cost"].as<bool>();
            if (pc_config["point_per_path_thre"]) config_.pointPerPathThre = pc_config["point_per_path_thre"].as<int>();
            if (pc_config["min_rel_z"]) config_.minRelZ = pc_config["min_rel_z"].as<double>();
            if (pc_config["max_rel_z"]) config_.maxRelZ = pc_config["max_rel_z"].as<double>();
        }
        
        // 路径规划配置
        if (config["planning_config"]) {
            auto plan_config = config["planning_config"];
            if (plan_config["dir_weight"]) config_.dirWeight = plan_config["dir_weight"].as<double>();
            if (plan_config["dir_thre"]) config_.dirThre = plan_config["dir_thre"].as<double>();
            if (plan_config["dir_to_vehicle"]) config_.dirToVehicle = plan_config["dir_to_vehicle"].as<bool>();
            if (plan_config["path_scale"]) config_.pathScale = plan_config["path_scale"].as<double>();
            if (plan_config["min_path_scale"]) config_.minPathScale = plan_config["min_path_scale"].as<double>();
            if (plan_config["path_scale_step"]) config_.pathScaleStep = plan_config["path_scale_step"].as<double>();
            if (plan_config["path_scale_by_speed"]) config_.pathScaleBySpeed = plan_config["path_scale_by_speed"].as<bool>();
            if (plan_config["min_path_range"]) config_.minPathRange = plan_config["min_path_range"].as<double>();
            if (plan_config["path_range_step"]) config_.pathRangeStep = plan_config["path_range_step"].as<double>();
            if (plan_config["path_range_by_speed"]) config_.pathRangeBySpeed = plan_config["path_range_by_speed"].as<bool>();
            if (plan_config["path_crop_by_goal"]) config_.pathCropByGoal = plan_config["path_crop_by_goal"].as<bool>();
            if (plan_config["goal_clear_range"]) config_.goalClearRange = plan_config["goal_clear_range"].as<double>();
            if (plan_config["arrived_dis_threshold"]) config_.arrived_dis_threshold = plan_config["arrived_dis_threshold"].as<double>();
        }
        
        // 网格配置
        if (config["grid_config"]) {
            auto grid_config = config["grid_config"];
            if (grid_config["grid_voxel_size"]) config_.gridVoxelSize = grid_config["grid_voxel_size"].as<float>();
            if (grid_config["search_radius"]) config_.searchRadius = grid_config["search_radius"].as<float>();
            if (grid_config["grid_voxel_offset_x"]) config_.gridVoxelOffsetX = grid_config["grid_voxel_offset_x"].as<float>();
            if (grid_config["grid_voxel_offset_y"]) config_.gridVoxelOffsetY = grid_config["grid_voxel_offset_y"].as<float>();
        }
        
        std::cout << "YAML configuration loaded successfully from: " << config_path << std::endl;
        return true;
    }
    catch (const YAML::Exception& e) {
        std::cerr << "YAML parsing error: " << e.what() << std::endl;
        return false;
    }
    catch (const std::exception& e) {
        std::cerr << "Error loading YAML config: " << e.what() << std::endl;
        return false;
    }
}

// 初始化点云
void LocalPlanner::initializePointClouds() {
    cloudKeyPoses3D.reset(new pcl::PointCloud<pcl::PointXYZI>());
    pointview.reset(new pcl::PointCloud<pcl::PointXYZI>());
    laserCloud.reset(new pcl::PointCloud<pcl::PointXYZI>());
    laserCloudCrop.reset(new pcl::PointCloud<pcl::PointXYZI>());
    laserCloudDwz.reset(new pcl::PointCloud<pcl::PointXYZI>());
    terrainCloud.reset(new pcl::PointCloud<pcl::PointXYZI>());
    terrainCloudCrop.reset(new pcl::PointCloud<pcl::PointXYZI>());
    terrainCloudDwz.reset(new pcl::PointCloud<pcl::PointXYZI>());
    plannerCloud.reset(new pcl::PointCloud<pcl::PointXYZI>());
    plannerCloudCrop.reset(new pcl::PointCloud<pcl::PointXYZI>());
    boundaryCloud.reset(new pcl::PointCloud<pcl::PointXYZI>());
    addedObstacles.reset(new pcl::PointCloud<pcl::PointXYZI>());
    freePaths.reset(new pcl::PointCloud<pcl::PointXYZI>());
    
    // 初始化点云栈
    for (int i = 0; i < laserCloudStackNum; i++) {
        laserCloudStack[i].reset(new pcl::PointCloud<pcl::PointXYZI>());
    }
    
    // 初始化起始路径
    for (int i = 0; i < groupNum; i++) {
        startPaths[i].reset(new pcl::PointCloud<pcl::PointXYZ>());
    }
    
    // 初始化路径
    #if PLOTPATHSET == 1
    for (int i = 0; i < pathNum; i++) {
        paths[i].reset(new pcl::PointCloud<pcl::PointXYZI>());
    }
    #endif
}

// 初始化滤波器
void LocalPlanner::initializeFilters() {
    laserDwzFilter.setLeafSize(config_.laserVoxelSize, config_.laserVoxelSize, config_.laserVoxelSize);
    terrainDwzFilter.setLeafSize(config_.terrainVoxelSize, config_.terrainVoxelSize, config_.terrainVoxelSize);
}

// 获取当前时间
double LocalPlanner::getCurrentTime() {
    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration<double>(duration).count();
}

// 数据输入接口实现
void LocalPlanner::updateOdometry(const planning_common::OdometryData& odom) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    odometryHandler(std::make_shared<const planning_common::OdometryData>(odom));
}

void LocalPlanner::updateLaserCloud(const PointCloud2Data& cloud) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    laserCloudHandler(std::make_shared<const PointCloud2Data>(cloud));
}

void LocalPlanner::updateTerrainCloud(const PointCloud2Data& cloud) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    terrainCloudHandler(std::make_shared<const PointCloud2Data>(cloud));
}

void LocalPlanner::updateGoal(const planning_common::PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    goalHandler(std::make_shared<const planning_common::PoseStamped>(goal));
}

void LocalPlanner::updateTarget(const PoseStamped& target) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    targetHandler(std::make_shared<const PoseStamped>(target));
}

void LocalPlanner::updateWebTarget(const PoseStamped& target) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    webtargetHandler(std::make_shared<const PoseStamped>(target));
}

void LocalPlanner::updateBoundary(const PolygonStamped& boundary) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    boundaryHandler(std::make_shared<const PolygonStamped>(boundary));
}

void LocalPlanner::updateAddedObstacles(const PointCloud2Data& obstacles) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    addedObstaclesHandler(std::make_shared<const PointCloud2Data>(obstacles));
}

void LocalPlanner::updateLocationFailed(const BoolMsg& msg) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    locationFailedHandler(std::make_shared<const BoolMsg>(msg));
}

void LocalPlanner::updateCalibration(const Int8Msg& calibration) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    calibrationHandler(std::make_shared<const Int8Msg>(calibration));
}

// 输出回调设置
void LocalPlanner::setModePublishCallback(BoolCallback callback) {
    mode_publish_callback_ = [this, callback](const std::shared_ptr<const BoolMsg>& mode) {
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            adjustmode = *mode;
        }
        if (callback) callback(mode);
    };
}

void LocalPlanner::setPathPublishCallback(PathCallback callback) {
    path_publish_callback_ = [this, callback](const std::shared_ptr<const PathData>& path) {
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            last_path_data_ = *path;
        }
        if (callback) callback(path);
    };
}

void LocalPlanner::setStopPublishCallback(Int8Callback callback) {
    stop_publish_callback_ = [this, callback](const std::shared_ptr<const Int8Msg>& stop) {
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            last_stop_msg_ = *stop;
        }
        if (callback) callback(stop);
    };
}

void LocalPlanner::setInnerStopPublishCallback(Int8Callback callback) {
    inner_stop_publish_callback_ = [this, callback](const std::shared_ptr<const Int8Msg>& stop) {
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            last_inner_stop_msg_ = *stop;
        }
        if (callback) callback(stop);
    };
}

void LocalPlanner::setReplanPublishCallback(Int8Callback callback) {
    replan_publish_callback_ = [this, callback](const std::shared_ptr<const Int8Msg>& replan) {
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            last_replan_msg_ = *replan;
        }
        if (callback) callback(replan);
    };
}

void LocalPlanner::setNodeReadyPublishCallback(BoolCallback callback) {
    node_ready_publish_callback_ = [this, callback](const std::shared_ptr<const BoolMsg>& ready) {
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            last_node_ready_msg_ = *ready;
        }
        if (callback) callback(ready);
    };
}

void LocalPlanner::setFreePathsPublishCallback(std::function<void(const std::shared_ptr<PointCloud2Data>&)> callback) {
    free_paths_publish_callback_ = [this, callback](const std::shared_ptr<PointCloud2Data>& cloud) {
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            last_free_paths_ = *cloud;
        }
        if (callback) callback(cloud);
    };
}

// 控制接口
void LocalPlanner::startPlanning() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    running_ = true;
    paused_ = false;

    // 发布节点准备就绪标志
    if (node_ready_publish_callback_) {
        auto ready_flag = std::make_shared<BoolMsg>(true);
        node_ready_publish_callback_(ready_flag);
    }
}

void LocalPlanner::stopPlanning() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    running_ = false;
    paused_ = false;
}

void LocalPlanner::pausePlanning() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    paused_ = true;
}

void LocalPlanner::resumePlanning() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    paused_ = false;
}

// 参数设置
void LocalPlanner::setMaxSpeed(double speed) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.maxSpeed = speed;
}

void LocalPlanner::setVehicleDimensions(double length, double width) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.vehicleLength = length;
    config_.vehicleWidth = width;
}

void LocalPlanner::setPathScale(double scale) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.pathScale = scale;
}

void LocalPlanner::setAdjacentRange(double range) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.adjacentRange = range;
}

void LocalPlanner::setUseTerrainAnalysis(bool use) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    config_.useTerrainAnalysis = use;
}

// 状态查询
bool LocalPlanner::isInitialized() const {
    return initialized_;
}

bool LocalPlanner::isRunning() const {
    return running_;
}

bool LocalPlanner::isPaused() const {
    return paused_;
}

LocalPlannerConfig LocalPlanner::getConfig() const {
    return config_;
}

// 原始回调函数实现 (保留原有算法逻辑)
void LocalPlanner::odometryHandler(const std::shared_ptr<const planning_common::OdometryData>& odom) {
    odomTime = odom->header.stamp;
    double roll, pitch, yaw;
    odom->pose.pose.orientation.toRPY(roll, pitch, yaw);
    vehicleRoll = roll;
    vehiclePitch = pitch;
    vehicleYaw = yaw;
    vehicleX = odom->pose.pose.position.x - cos(yaw) * config_.sensorOffsetX + sin(yaw) * config_.sensorOffsetY;
    vehicleY = odom->pose.pose.position.y - sin(yaw) * config_.sensorOffsetX - cos(yaw) * config_.sensorOffsetY;
    vehicleZ = odom->pose.pose.position.z;
}

void LocalPlanner::laserCloudHandler(const std::shared_ptr<const PointCloud2Data>& laserCloud2) {
    if (!config_.useTerrainAnalysis) {
        // 不采用可通行区域检测
        laserCloud->clear();
        convertPointCloud2ToPCL(*laserCloud2, *laserCloud);

        pcl::PointXYZI point;
        laserCloudCrop->clear();
        int laserCloudSize = laserCloud->points.size();
        for (int i = 0; i < laserCloudSize; i++) {
            point = laserCloud->points[i];
            float pointX = point.x;
            float pointY = point.y;
            float pointZ = point.z;

            // 筛选一定范围内的点云
            float dis = sqrt((pointX - vehicleX) * (pointX - vehicleX) + (pointY - vehicleY) * (pointY - vehicleY));
            if (dis < config_.adjacentRange) {
                point.x = pointX;
                point.y = pointY;
                point.z = pointZ;
                laserCloudCrop->push_back(point);
            }
        }

        // 点云滤波
        laserCloudDwz->clear();
        laserDwzFilter.setInputCloud(laserCloudCrop);
        laserDwzFilter.filter(*laserCloudDwz);

        // 新点云接受完成
        newLaserCloud = true;
    }
}

void LocalPlanner::terrainCloudHandler(const std::shared_ptr<const PointCloud2Data>& terrainCloud2) {
    if (config_.useTerrainAnalysis) {
        // 采用可通行区域检测
        terrainCloud->clear();
        convertPointCloud2ToPCL(*terrainCloud2, *terrainCloud);

        pcl::PointXYZI point;
        terrainCloudCrop->clear();
        int terrainCloudSize = terrainCloud->points.size();
        for (int i = 0; i < terrainCloudSize; i++) {
            point = terrainCloud->points[i];
            float pointX = point.x;
            float pointY = point.y;
            float pointZ = point.z;

            // 筛选一定范围内的点云
            float dis = sqrt((pointX - vehicleX) * (pointX - vehicleX) + (pointY - vehicleY) * (pointY - vehicleY));
            if (dis < config_.adjacentRange && (point.intensity > config_.obstacleHeightThre || config_.useCost)) {
                point.x = pointX;
                point.y = pointY;
                point.z = pointZ;
                terrainCloudCrop->push_back(point);
            }
        }

        // 点云滤波
        terrainCloudDwz->clear();
        terrainDwzFilter.setInputCloud(terrainCloudCrop);
        terrainDwzFilter.filter(*terrainCloudDwz);

        // 新点云接受完成
        newTerrainCloud = true;
    }
}

void LocalPlanner::goalHandler(const std::shared_ptr<const planning_common::PoseStamped>& goal) {
    goalX = goal->pose.position.x;
    goalY = goal->pose.position.y;
    goalZ = goal->pose.position.z;
    adjustmode.data = false;
    if (init) {
        init = false;
    }
}

void LocalPlanner::targetHandler(const std::shared_ptr<const planning_common::PoseStamped>& target) {
    nav_start = 1;
    targetX = target->pose.position.x;
    targetY = target->pose.position.y;
    targetYaw = target->pose.orientation.getYaw();
    std::cout << "handpoint x:[" << targetX << "],y:[" << targetY << "],yaw:[" << targetYaw << "]" << std::endl;
    config_.maxSpeed = 0.8;
    config_.twoWayDrive = false;
    init = true;
    adjustmode.data = false;
    arrive_inf.data = 0;
}

void LocalPlanner::webtargetHandler(const std::shared_ptr<const PoseStamped>& target) {
    nav_start = 1;
    targetX = target->pose.position.x;
    targetY = target->pose.position.y;
    targetYaw = 0; // 简化处理

    config_.maxSpeed = 0.8;
    config_.twoWayDrive = false;
    init = true;
    adjustmode.data = false;
    arrive_inf.data = 0;

    // 目标点航向，deg
    info = (target->pose.orientation.z <= 360.0 && fabs(target->pose.orientation.z) > 1e-6);

    std::cout << "web target:" << targetX << "," << targetY << "," << target->pose.orientation.z << std::endl;
}

void LocalPlanner::boundaryHandler(const std::shared_ptr<const PolygonStamped>& boundary) {
    // 初始化
    boundaryCloud->clear();
    pcl::PointXYZI point, point1, point2;
    int boundarySize = boundary->points.size();

    // 若多边形不为空，赋值第一个点
    if (boundarySize >= 1) {
        point2.x = boundary->points[0].x;
        point2.y = boundary->points[0].y;
        point2.z = boundary->points[0].z;
    }

    // 根据多边形定点数进行循环
    for (int i = 0; i < boundarySize; i++) {
        point1 = point2;
        // 赋值下一个点
        point2.x = boundary->points[i].x;
        point2.y = boundary->points[i].y;
        point2.z = boundary->points[i].z;

        // 若两个点z方向相同，则计算其距离
        if (point1.z == point2.z) {
            float disX = point1.x - point2.x;
            float disY = point1.y - point2.y;
            float dis = sqrt(disX * disX + disY * disY);

            // 将距离栅格化，并生成障碍物点
            int pointNum = int(dis / config_.terrainVoxelSize) + 1;
            for (int pointID = 0; pointID < pointNum; pointID++) {
                point.x = float(pointID) / float(pointNum) * point1.x + (1.0 - float(pointID) / float(pointNum)) * point2.x;
                point.y = float(pointID) / float(pointNum) * point1.y + (1.0 - float(pointID) / float(pointNum)) * point2.y;
                point.z = 0;
                point.intensity = 100.0;

                // 将生成的障碍物点压入边界点云中
                for (int j = 0; j < config_.pointPerPathThre; j++) {
                    boundaryCloud->push_back(point);
                }
            }
        }
    }
}

void LocalPlanner::addedObstaclesHandler(const std::shared_ptr<const PointCloud2Data>& addedObstacles2) {
    addedObstacles->clear();
    convertPointCloud2ToPCL(*addedObstacles2, *addedObstacles);

    // 将接收到的点全部作为障碍物点云
    int addedObstaclesSize = addedObstacles->points.size();
    for (int i = 0; i < addedObstaclesSize; i++) {
        addedObstacles->points[i].intensity = 200.0;
    }
}

void LocalPlanner::locationFailedHandler(const std::shared_ptr<const BoolMsg>& msg) {
    // 处理接收到的bool消息
    if (msg->data) {
        // 定位成功
    } else {
        // 定位丢失，发布istop消息
        location_failed = true;
    }
}

void LocalPlanner::calibrationHandler(const std::shared_ptr<const Int8Msg>& calibration) {
    info = calibration->data; // 1为使用精调，0为不使用精调
}

// 文件读取方法 (保留原有实现)
int LocalPlanner::readPlyHeader(FILE *filePtr) {
    char str[50];
    int val, pointNum;
    std::string strCur, strLast;
    while (strCur != "end_header") {
        val = fscanf(filePtr, "%s", str);
        if (val != 1) {
            printf("\nError reading input files, exit.\n\n");
            exit(1);
        }
        strLast = strCur;
        strCur = std::string(str);
        if (strCur == "vertex" && strLast == "element") {
            val = fscanf(filePtr, "%d", &pointNum);
            if (val != 1) {
                printf("\nError reading input files, exit.\n\n");
                exit(1);
            }
        }
    }
    return pointNum;
}

void LocalPlanner::readStartPaths() {
    std::string fileName = config_.pathFolder + "/startPaths.ply";
    FILE *filePtr = fopen(fileName.c_str(), "r");
    if (filePtr == NULL) {
        printf("\nCannot read input files, exit.\n\n");
        exit(1);
    }
    int pointNum = readPlyHeader(filePtr);
    pcl::PointXYZ point;
    int val1, val2, val3, val4, groupID;
    for (int i = 0; i < pointNum; i++) {
        val1 = fscanf(filePtr, "%f", &point.x);
        val2 = fscanf(filePtr, "%f", &point.y);
        val3 = fscanf(filePtr, "%f", &point.z);
        val4 = fscanf(filePtr, "%d", &groupID);
        if (val1 != 1 || val2 != 1 || val3 != 1 || val4 != 1) {
            printf("\nError reading input files, exit.\n\n");
            exit(1);
        }
        if (groupID >= 0 && groupID < groupNum) {
            startPaths[groupID]->push_back(point);
        }
    }
    fclose(filePtr);
}

#if PLOTPATHSET == 1
void LocalPlanner::readPaths() {
    std::string fileName = config_.pathFolder + "/paths.ply";
    FILE *filePtr = fopen(fileName.c_str(), "r");
    if (filePtr == NULL) {
        printf("\nCannot read input files, exit.\n\n");
        exit(1);
    }
    int pointNum = readPlyHeader(filePtr);
    pcl::PointXYZI point;
    int pointSkipNum = 30;
    int pointSkipCount = 0;
    int val1, val2, val3, val4, val5, pathID;
    for (int i = 0; i < pointNum; i++) {
        val1 = fscanf(filePtr, "%f", &point.x);
        val2 = fscanf(filePtr, "%f", &point.y);
        val3 = fscanf(filePtr, "%f", &point.z);
        val4 = fscanf(filePtr, "%d", &pathID);
        val5 = fscanf(filePtr, "%f", &point.intensity);
        if (val1 != 1 || val2 != 1 || val3 != 1 || val4 != 1 || val5 != 1) {
            printf("\nError reading input files, exit.\n\n");
            exit(1);
        }
        if (pathID >= 0 && pathID < pathNum) {
            pointSkipCount++;
            if (pointSkipCount > pointSkipNum) {
                paths[pathID]->push_back(point);
                pointSkipCount = 0;
            }
        }
    }
    fclose(filePtr);
}
#endif

void LocalPlanner::readPathList() {
    std::string fileName = config_.pathFolder + "/pathList.ply";
    FILE *filePtr = fopen(fileName.c_str(), "r");
    if (filePtr == NULL) {
        printf("\nCannot read input files, exit.\n\n");
        exit(1);
    }
    if (pathNum != readPlyHeader(filePtr)) {
        printf("\nIncorrect path number, exit.\n\n");
        exit(1);
    }
    int val1, val2, val3, val4, val5, pathID, groupID;
    float endX, endY, endZ;
    for (int i = 0; i < pathNum; i++) {
        val1 = fscanf(filePtr, "%f", &endX);
        val2 = fscanf(filePtr, "%f", &endY);
        val3 = fscanf(filePtr, "%f", &endZ);
        val4 = fscanf(filePtr, "%d", &pathID);
        val5 = fscanf(filePtr, "%d", &groupID);
        if (val1 != 1 || val2 != 1 || val3 != 1 || val4 != 1 || val5 != 1) {
            printf("\nError reading input files, exit.\n\n");
            exit(1);
        }
        if (pathID >= 0 && pathID < pathNum && groupID >= 0 && groupID < groupNum) {
            pathList[pathID] = groupID;
            endDirPosPathList[pathID](0) = endX;
            endDirPosPathList[pathID](1) = endY;
            endDirPosPathList[pathID](2) = atan2(endY, endX) * 180 / PI;
        }
    }
    fclose(filePtr);
}

void LocalPlanner::readCorrespondences() {
    std::string fileName = config_.pathFolder + "/correspondences.txt";
    FILE *filePtr = fopen(fileName.c_str(), "r");
    if (filePtr == NULL) {
        printf("\nCannot read input files, exit.\n\n");
        exit(1);
    }
    int val1, gridVoxelID, pathID;
    for (int i = 0; i < gridVoxelNum; i++) {
        val1 = fscanf(filePtr, "%d", &gridVoxelID);
        if (val1 != 1) {
            printf("\nError reading input files, exit.\n\n");
            exit(1);
        }
        while (1) {
            val1 = fscanf(filePtr, "%d", &pathID);
            if (val1 != 1) {
                printf("\nError reading input files, exit.\n\n");
                exit(1);
            }
            if (pathID != -1) {
                if (gridVoxelID >= 0 && gridVoxelID < gridVoxelNum && pathID >= 0 && pathID < pathNum) {
                    correspondences[gridVoxelID].push_back(pathID);
                }
            } else {
                break;
            }
        }
    }
    fclose(filePtr);
}

// PCL点云转换方法
void LocalPlanner::convertPointCloud2ToPCL(const PointCloud2Data& cloud2, pcl::PointCloud<pcl::PointXYZI>& pcl_cloud) {
    // 简化的点云转换实现
    // 实际使用时需要完整的PCL转换逻辑
    pcl_cloud.clear();

    // 假设每个点16字节 (x,y,z,intensity各4字节)
    size_t point_size = 16;
    size_t num_points = cloud2.data.size() / point_size;

    for (size_t i = 0; i < num_points; i++) {
        pcl::PointXYZI point;
        // 简化的数据解析，实际需要根据PointCloud2格式正确解析
        size_t offset = i * point_size;
        if (offset + 15 < cloud2.data.size()) {
            memcpy(&point.x, &cloud2.data[offset], 4);
            memcpy(&point.y, &cloud2.data[offset + 4], 4);
            memcpy(&point.z, &cloud2.data[offset + 8], 4);
            memcpy(&point.intensity, &cloud2.data[offset + 12], 4);
            pcl_cloud.push_back(point);
        }
    }
}

void LocalPlanner::convertPCLToPointCloud2(const pcl::PointCloud<pcl::PointXYZI>& pcl_cloud, PointCloud2Data& cloud2) {
    // 简化的点云转换实现
    cloud2.data.clear();
    cloud2.height = 1;
    cloud2.width = pcl_cloud.points.size();
    cloud2.header.setCurrentTime();

    // 假设每个点16字节
    size_t point_size = 16;
    cloud2.data.resize(pcl_cloud.points.size() * point_size);

    for (size_t i = 0; i < pcl_cloud.points.size(); i++) {
        const auto& point = pcl_cloud.points[i];
        size_t offset = i * point_size;
        memcpy(&cloud2.data[offset], &point.x, 4);
        memcpy(&cloud2.data[offset + 4], &point.y, 4);
        memcpy(&cloud2.data[offset + 8], &point.z, 4);
        memcpy(&cloud2.data[offset + 12], &point.intensity, 4);
    }
}

// 主循环方法
void LocalPlanner::controlLoop() {
    const double loop_rate = 100.0; // 100Hz
    const auto sleep_duration = std::chrono::duration<double>(1.0 / loop_rate);

    while (running_ && !paused_) {
        try {
            processOnce();
            std::this_thread::sleep_for(sleep_duration);
        }
        catch (const std::exception& e) {
            std::cerr << "Control loop error: " << e.what() << std::endl;
            break;
        }
    }
}

// 处理一次规划 (保留原有主要算法逻辑)
void LocalPlanner::processOnce() {
    std::lock_guard<std::mutex> lock(data_mutex_);

    if (!initialized_ || !running_ || paused_) {
        return;
    }

    // 处理定位失败
    if (location_failed) {
        // 可以在这里发布内部停止信号
        // location_failed = false;
    }

    if (newLaserCloud || newTerrainCloud) {
        if (newLaserCloud) {
            // 不采用可通行区域检测
            newLaserCloud = false;
            laserCloudStack[laserCloudCount]->clear();
            *laserCloudStack[laserCloudCount] = *laserCloudDwz;
            laserCloudCount = (laserCloudCount + 1) % laserCloudStackNum;
            plannerCloud->clear();
            for (int i = 0; i < laserCloudStackNum; i++) {
                *plannerCloud += *laserCloudStack[i];
            }
        }

        if (newTerrainCloud) {
            // 采用可通行区域检测
            newTerrainCloud = false;
            plannerCloud->clear();
            *plannerCloud = *terrainCloudDwz;
        }

        // 方便后续程序使用
        float sinVehicleRoll = sin(vehicleRoll);
        float cosVehicleRoll = cos(vehicleRoll);
        float sinVehiclePitch = sin(vehiclePitch);
        float cosVehiclePitch = cos(vehiclePitch);
        float sinVehicleYaw = sin(vehicleYaw);
        float cosVehicleYaw = cos(vehicleYaw);

        // 将规划点云转到载体系下
        pcl::PointXYZI point;
        plannerCloudCrop->clear();
        int plannerCloudSize = plannerCloud->points.size();
        for (int i = 0; i < plannerCloudSize; i++) {
            float pointX1 = plannerCloud->points[i].x - vehicleX;
            float pointY1 = plannerCloud->points[i].y - vehicleY;
            float pointZ1 = plannerCloud->points[i].z - vehicleZ;
            point.x = pointX1 * cosVehicleYaw + pointY1 * sinVehicleYaw;
            point.y = -pointX1 * sinVehicleYaw + pointY1 * cosVehicleYaw;
            point.z = pointZ1;
            point.intensity = plannerCloud->points[i].intensity;

            // 若不使用可通行区域检测需对点云范围进行筛选，并存入规划点云中
            float dis = sqrt(point.x * point.x + point.y * point.y);
            if (dis < config_.adjacentRange && ((point.z > config_.minRelZ && point.z < config_.maxRelZ) || config_.useTerrainAnalysis)) {
                plannerCloudCrop->push_back(point);
            }
        }

        // 将导航边界点云转到载体系下
        int boundaryCloudSize = boundaryCloud->points.size();
        for (int i = 0; i < boundaryCloudSize; i++) {
            point.x = ((boundaryCloud->points[i].x - vehicleX) * cosVehicleYaw + (boundaryCloud->points[i].y - vehicleY) * sinVehicleYaw);
            point.y = (-(boundaryCloud->points[i].x - vehicleX) * sinVehicleYaw + (boundaryCloud->points[i].y - vehicleY) * cosVehicleYaw);
            point.z = boundaryCloud->points[i].z;
            point.intensity = boundaryCloud->points[i].intensity;

            // 对导航边界点云范围进行筛选，并存入规划点云中
            float dis = sqrt(point.x * point.x + point.y * point.y);
            if (dis < config_.adjacentRange) {
                plannerCloudCrop->push_back(point);
            }
        }

        // 将新增障碍物点云转到载体系下
        int addedObstaclesSize = addedObstacles->points.size();
        for (int i = 0; i < addedObstaclesSize; i++) {
            point.x = ((addedObstacles->points[i].x - vehicleX) * cosVehicleYaw + (addedObstacles->points[i].y - vehicleY) * sinVehicleYaw);
            point.y = (-(addedObstacles->points[i].x - vehicleX) * sinVehicleYaw + (addedObstacles->points[i].y - vehicleY) * cosVehicleYaw);
            point.z = addedObstacles->points[i].z;
            point.intensity = addedObstacles->points[i].intensity;

            // 对新增障碍物点云范围进行筛选，并存入规划点云中
            float dis = sqrt(point.x * point.x + point.y * point.y);
            if (dis < config_.adjacentRange) {
                plannerCloudCrop->push_back(point);
            }
        }

        // 根据速度设置轨迹范围
        float pathRange = config_.adjacentRange;
        if (config_.pathRangeBySpeed) pathRange = config_.adjacentRange * config_.maxSpeed;
        if (pathRange < config_.minPathRange) pathRange = config_.minPathRange;
        float relativeGoalDis = config_.adjacentRange;

        // 进入避障模式导航
        if (!adjustmode.data && nav_start == 1 && !init) {
            // 根据当前载体位置与终点位置判断进入的导航模式
            double distance = sqrt((targetX - vehicleX) * (targetX - vehicleX) + (targetY - vehicleY) * (targetY - vehicleY));
            Int8Msg safetystop;

            // 导航模式
            adjustmode.data = false;
            safetystop.data = 0;
            if (arrive_inf.data == 0) {
                if (distance < config_.arrived_dis_threshold) { // 进入精调模式距离
                    if (info == 1) { // 任务点需切精调
                        adjustmode.data = true;
                        safetystop.data = 0;
                        std::cout << "进入精调模式" << std::endl;
                        std::cout << "target:" << targetX << "," << targetY << "," << targetYaw << std::endl;
                    } else if (info == 0) { // 任务点不需切精调
                        // 停止标志位
                        adjustmode.data = false;
                        safetystop.data = 1;
                        nav_start = 0;
                        std::cout << "到达终点" << std::endl;
                        std::cout << "target:" << targetX << "," << targetY << "," << targetYaw << std::endl;
                        std::cout << "vehicle:" << vehicleX << "," << vehicleY << "," << vehicleYaw << std::endl;

                        // 发布停止标志位
                        if (stop_publish_callback_) {
                            stop_publish_callback_(std::make_shared<Int8Msg>(safetystop));
                        }
                        if (inner_stop_publish_callback_) {
                            inner_stop_publish_callback_(std::make_shared<Int8Msg>(safetystop));
                        }
                        arrive_inf.data = 1; // 保证仅发布一次到点信息
                    }
                }
            }

            // 发布导航模式
            if (mode_publish_callback_) {
                mode_publish_callback_(std::make_shared<BoolMsg>(adjustmode));
            }

            // 执行路径规划算法 (简化版本，完整版本需要更多代码)
            performPathPlanning(pathRange, relativeGoalDis);
        } else if (adjustmode.data && nav_start == 1 && !init) {
            // 精调模式处理
        }
    }
}

// 打印状态
void LocalPlanner::printStatus() {
    std::cout << "LocalPlanner Status:" << std::endl;
    std::cout << "  Initialized: " << (initialized_ ? "Yes" : "No") << std::endl;
    std::cout << "  Running: " << (running_ ? "Yes" : "No") << std::endl;
    std::cout << "  Paused: " << (paused_ ? "Yes" : "No") << std::endl;
    std::cout << "  Vehicle Position: (" << vehicleX << ", " << vehicleY << ", " << vehicleZ << ")" << std::endl;
    std::cout << "  Vehicle Orientation: " << vehicleYaw * 180.0 / PI << " deg" << std::endl;
    std::cout << "  Goal Position: (" << goalX << ", " << goalY << ", " << goalZ << ")" << std::endl;
    std::cout << "  Target Position: (" << targetX << ", " << targetY << ", " << targetZ << ")" << std::endl;
    std::cout << "  Max Speed: " << config_.maxSpeed << " m/s" << std::endl;
    std::cout << "  Navigation Started: " << (nav_start ? "Yes" : "No") << std::endl;
    std::cout << "  Adjust Mode: " << (adjustmode.data ? "Yes" : "No") << std::endl;
}

// 路径规划核心算法 (简化实现，保留主要逻辑结构)
void LocalPlanner::performPathPlanning(float pathRange, float relativeGoalDis) {
    // 将局部目标点转到载体系下并计算距离的夹角
    float sinVehicleYaw = sin(vehicleYaw);
    float cosVehicleYaw = cos(vehicleYaw);
    float relativeGoalX = ((goalX - vehicleX) * cosVehicleYaw + (goalY - vehicleY) * sinVehicleYaw);
    float relativeGoalY = (-(goalX - vehicleX) * sinVehicleYaw + (goalY - vehicleY) * cosVehicleYaw);
    relativeGoalDis = sqrt(relativeGoalX * relativeGoalX + relativeGoalY * relativeGoalY);
    joyDir = atan2(relativeGoalY, relativeGoalX) * 180 / PI;

    // 若不是双向驱动则把夹角转到-90度到90度内
    if (!config_.twoWayDrive) {
        if (joyDir > 90.0) joyDir = 90.0;
        else if (joyDir < -90.0) joyDir = -90.0;
    }

    // 根据速度设置轨迹尺度，速度越快尺度越大
    bool pathFound = false;
    float defPathScale = config_.pathScale;
    if (config_.pathScaleBySpeed) config_.pathScale = defPathScale * config_.maxSpeed;
    if (config_.pathScale < config_.minPathScale) config_.pathScale = config_.minPathScale;

    // 简化的路径搜索循环
    while (config_.pathScale >= config_.minPathScale && pathRange >= config_.minPathRange) {
        // 轨迹列表和得分初始化
        for (int i = 0; i < 36 * pathNum; i++) {
            clearPathList[i] = 0;
            pathPenaltyList[i] = 0;
        }
        for (int i = 0; i < 36 * groupNum; i++) {
            clearPathPerGroupScore[i] = 0;
        }

        // 简化的障碍物检测和路径评分
        // (这里省略了复杂的障碍物检测算法，实际使用时需要完整实现)

        // 模拟找到一条路径
        int selectedGroupID = 0; // 简化：总是选择第一组路径

        if (selectedGroupID >= 0) {
            // 生成路径数据
            PathData path;
            path.header.setCurrentTime();
            path.header.frame_id = "vehicle";

            // 简化的路径生成
            int selectedPathLength = std::min(10, static_cast<int>(startPaths[selectedGroupID]->points.size()));
            path.poses_.resize(selectedPathLength);

            for (int i = 0; i < selectedPathLength; i++) {
                if (i < startPaths[selectedGroupID]->points.size()) {
                    float x = startPaths[selectedGroupID]->points[i].x;
                    float y = startPaths[selectedGroupID]->points[i].y;
                    float z = startPaths[selectedGroupID]->points[i].z;

                    path.poses_[i].pose.position.x = config_.pathScale * x;
                    path.poses_[i].pose.position.y = config_.pathScale * y;
                    path.poses_[i].pose.position.z = config_.pathScale * z;
                } else {
                    path.poses_[i].pose.position.x = 0.0;
                    path.poses_[i].pose.position.y = 0.0;
                    path.poses_[i].pose.position.z = 0.0;
                }
            }

            // 发布局部路径信息
            if (path_publish_callback_) {
                path_publish_callback_(std::make_shared<PathData>(path));
            }

            // 发布自由路径信息 (简化)
            #if PLOTPATHSET == 1
            if (free_paths_publish_callback_) {
                PointCloud2Data freePaths2;
                convertPCLToPointCloud2(*freePaths, freePaths2);
                freePaths2.header.setCurrentTime();
                freePaths2.header.frame_id = "vehicle";
                free_paths_publish_callback_(std::make_shared<PointCloud2Data>(freePaths2));
            }
            #endif

            pathFound = true;
            break;
        } else {
            // 缩小路径范围和路径尺度进行下一次循环
            if (config_.pathScale >= config_.minPathScale + config_.pathScaleStep) {
                config_.pathScale -= config_.pathScaleStep;
                pathRange = config_.adjacentRange * config_.pathScale / defPathScale;
            } else {
                pathRange -= config_.pathRangeStep;
            }
        }
    }

    config_.pathScale = defPathScale;

    // 如果路径尺度到最小了还没有找到可走路径
    if (!pathFound) {
        end_time = pcl::getTime();
        if (end_time - start_time > 5) {
            Int8Msg replan;
            replan.data = 1;
            if (replan_publish_callback_) {
                replan_publish_callback_(std::make_shared<Int8Msg>(replan));
            }
        }

        // 发布空的局部路径
        PathData path;
        path.header.setCurrentTime();
        path.header.frame_id = "vehicle";
        path.poses_.resize(1);
        path.poses_[0].pose.position.x = 0;
        path.poses_[0].pose.position.y = 0;
        path.poses_[0].pose.position.z = 0;

        if (path_publish_callback_) {
            path_publish_callback_(std::make_shared<PathData>(path));
        }

        // 发布空的可选择路径
        #if PLOTPATHSET == 1
        if (free_paths_publish_callback_) {
            PointCloud2Data freePaths2;
            freePaths2.header.setCurrentTime();
            freePaths2.header.frame_id = "vehicle";
            free_paths_publish_callback_(std::make_shared<PointCloud2Data>(freePaths2));
        }
        #endif
    } else {
        start_time = pcl::getTime();
        end_time = pcl::getTime();
        Int8Msg replan;
        replan.data = 0;
        if (replan_publish_callback_) {
            replan_publish_callback_(std::make_shared<Int8Msg>(replan));
        }
    }
}

BoolMsg LocalPlanner::getCurrentMode() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    return adjustmode;
}
PathData LocalPlanner::getCurrentPath() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    return last_path_data_;
}
Int8Msg LocalPlanner::getCurrentStop() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    return last_stop_msg_;
}
Int8Msg LocalPlanner::getCurrentInnerStop() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    return last_inner_stop_msg_;
}
Int8Msg LocalPlanner::getCurrentReplan() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    return last_replan_msg_;
}
BoolMsg LocalPlanner::getCurrentNodeReady() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    return last_node_ready_msg_;
}
PointCloud2Data LocalPlanner::getCurrentFreePaths() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    return last_free_paths_;
}

void LocalPlanner::updatePath(const planning_common::PathData& path) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    if (path_publish_callback_) {
        last_path_data_ = path;
        path_publish_callback_(std::make_shared<planning_common::PathData>(path));
    }
}

void LocalPlanner::updateStop(const planning_common::Int8Msg& stop) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    if (stop_publish_callback_) {
        last_stop_msg_ = stop;
        stop_publish_callback_(std::make_shared<planning_common::Int8Msg>(stop));
    }
}

} // namespace local_planner
