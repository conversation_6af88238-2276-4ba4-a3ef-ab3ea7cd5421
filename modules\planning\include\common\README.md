# Common Types for Planning Module

这个目录包含了planning模块中所有子模块共享的公共数据结构定义。

## 文件结构

- `common_types.h` - 公共结构体定义头文件
- `test_common_types.cpp` - 测试文件
- `README.md` - 本说明文件

## 包含的结构体

### 基础结构体
- `TimeStamp` - 时间戳结构
- `Header` - 消息头结构
- `Point` - 3D点结构
- `Vector3` - 3D向量结构
- `Quaternion` - 四元数结构

### 复合结构体
- `Pose` - 位姿结构（位置+方向）
- `PoseStamped` - 带时间戳的位姿
- `TwistMsg` - 速度消息结构
- `OdometryData` - 里程计数据结构
- `PathData` - 路径数据结构
- `TwistData` - 速度命令结构

### 消息结构体
- `BoolMsg` - 布尔消息
- `Int8Msg` - 整数消息

## 使用方法

### 1. 在头文件中包含

```cpp
#include "common_types.h"
```

### 2. 使用命名空间

```cpp
namespace your_module {
    // 使用公共命名空间中的类型
    using planning_common::TimeStamp;
    using planning_common::Point;
    using planning_common::Quaternion;
    using planning_common::Pose;
    using planning_common::PoseStamped;
    // ... 其他需要的类型
}
```

### 3. 示例代码

```cpp
#include "common_types.h"

void example() {
    // 创建时间戳
    planning_common::TimeStamp ts = planning_common::TimeStamp::now();
    
    // 创建3D点
    planning_common::Point p(1.0, 2.0, 3.0);
    
    // 从Yaw角创建四元数
    planning_common::Quaternion q = planning_common::Quaternion::fromYaw(1.57);
    
    // 创建位姿
    planning_common::Pose pose(p, q);
    
    // 创建带时间戳的位姿
    planning_common::PoseStamped ps(ts.toSec(), p, q);
    
    // 创建路径数据
    planning_common::PathData path;
    path.poses_.push_back(ps);
}
```

## 已更新的模块

以下模块已经更新为使用公共结构体：

1. **path_follower** - 路径跟随器模块
   - 移除了重复的结构体定义
   - 使用 `planning_common` 命名空间中的类型

2. **global_traj_generate** - 全局轨迹生成模块
   - 移除了重复的结构体定义
   - 使用 `planning_common` 命名空间中的类型

## 编译

确保在CMakeLists.txt中包含了common目录：

```cmake
include_directories(
    include/common
    # ... 其他路径
)
```

## 测试

运行测试可执行文件来验证公共结构体是否正常工作：

```bash
cd modules/planning/build
make test_common_types
./test_common_types
```

## 注意事项

1. 所有模块都应该使用公共结构体，避免重复定义
2. 如果需要添加新的公共结构体，请在此文件中定义
3. 保持向后兼容性，不要随意修改现有结构体的接口
4. 使用 `planning_common` 命名空间来避免命名冲突 