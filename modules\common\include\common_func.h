/*
 * @Description: 通用函数
 */

#pragma once

namespace common_lib {

const double CHINA_LONGITUDE_MIN   = 73.666667;    ///< 经度的min值, degree
const double CHINA_LONGITUDE_MAX   = 135.041667;   ///< 经度的max值, degree
const double CHINA_LATITUDE_MIN    = 3.866667;     ///< 纬度的min值, degree
const double CHINA_LATITUDE_MAX    = 53.550000;    ///< 纬度的max值, degree

#define M_PI 3.1415926535

inline double RadToDeg(double rad){
    return rad / M_PI * 180.0;
}

bool OutOfChina(double lat, double lon);


} // namespace COMMON_LIB
