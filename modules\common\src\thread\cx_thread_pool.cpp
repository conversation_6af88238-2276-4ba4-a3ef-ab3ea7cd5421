#include "cx_thread_pool.h"

#include "cx_singleton.h"
#include "cx_thread.h"
#include "cx_work.h"
namespace common_lib {
CXThreadPool *GetSingleton4ThreadPool() {
  static CXSingleton<CXThreadPool> s_ThreadPool;

  return s_ThreadPool.GetSingletonInstance();
}

cx_bool CXThreadPool::g_initialized_thread_pool = false;

CXThreadPool::CXThreadPool() {
  thread_minimum_ = 0;
  thread_maximum_ = 0;
  g_initialized_thread_pool = false;

#ifdef WIN32
  m_pCleanupGroup = NULL;
  m_pThreadPool = NULL;
#endif  // WIN32

  terminate_event_.Create();

  Create();
}

CXThreadPool::~CXThreadPool() {
  Close();

  (void)terminate_event_.Destroy();
}

cx_int CXThreadPool::Create() {
  terminate_event_.Reset();

#ifdef WIN32
  // ��Ч�Լ���
  if (m_pThreadPool != NULL) {
    Close();
    m_TerminateEvent.Reset();
  }

  // ����˽���̳߳�
  m_pThreadPool = CreateThreadpool(NULL);
  if (m_pThreadPool == NULL) {
    MessageBox(NULL, TEXT("�޷�����˽���̳߳أ�"), TEXT("��ʼ��ʧ�ܣ�"),
               MB_OK | MB_ICONERROR);
    return -1;
  }

  // �����̳߳أ���С�߳���Ϊ2�����Ϊ4.
  SetThreadpoolThreadMaximum(m_pThreadPool, 4);
  if (!SetThreadpoolThreadMinimum(m_pThreadPool, 2)) {
    MessageBox(NULL, TEXT("�����̳߳���С�߳���ʧ�ܣ�"), TEXT("��ʼ��ʧ�ܣ�"), MB_OK | MB_ICONERROR);
    return -1;
  }

  // ���ûص�������
  // ע�⣺����һ�����������������Ժ���ֶ�����
  //     CallbackEnviron->Version =1;
  //     CallbackEnviron->Pool    = NULL;
  //     CallbackEnviron->CleanupGroup = NULL;
  //     CallbackEnviron->CleanupGroupCancelCallback = NULL;
  //     CallbackEnviron->RaceDll = NULL;
  //     CallbackEnviron->ActivationContext = NULL;
  //     CallbackEnviron->FinalizationCallback = NULL;
  //     CallbackEnviron->u.Flags = 0;
  // ��ʼ���ص�������
  InitializeThreadpoolEnvironment(&m_cbEnv);

  // ���̳߳���ص���������
  // ע�⣺�õ��û�ʹ��CallbackEnviro->Pool = Pool;
  SetThreadpoolCallbackPool(&m_cbEnv, m_pThreadPool);

  // ������Դ������
  m_pCleanupGroup = CreateThreadpoolCleanupGroup();
  // ���������뻷�������
  SetThreadpoolCallbackCleanupGroup(&m_cbEnv, m_pCleanupGroup, NULL);

#else
  idle_threads_.LockW();

  g_initialized_thread_pool = false;
  CXThread *pNewThread = NULL;
  for (cx_dword i = 0U; i < thread_minimum_; ++i) {
    pNewThread =
        CreateThread();  // ��ʼ��ʱ�������̳߳ػ�δ������ϣ�����ʹ��CREATE_SUSPENDED������߳��������У����ܵ����̳߳ض��γ�ʼ����
    (void)idle_threads_.insert(pNewThread);
  }
  g_initialized_thread_pool = true;

  idle_threads_.UnlockW();

#endif  // WIN32

  return 0;
}

cx_int CXThreadPool::Close() {
  terminate_event_.Set();

#ifdef WIN32
  // ע�⣺DestroyThreadpoolEnviroment������û��ʲô��
  // Ҫ�������Լ��ͷ��̳߳�
  CloseThreadpoolCleanupGroupMembers(m_pCleanupGroup, FALSE, NULL);
  CloseThreadpoolCleanupGroup(m_pCleanupGroup);
  m_pCleanupGroup = NULL;

  if (m_pThreadPool != NULL) {
    CloseThreadpool(m_pThreadPool);
    m_pThreadPool = NULL;
  }

  // ���ٻ����飬�˰汾��DestroyThreadpoolEnviroment��û��ʲô
  // Ҳ����һ���汾���ṩ������ֱ�ӵ��ü���
  DestroyThreadpoolEnvironment(&m_cbEnv);

#else

  DecreateThreads(static_cast<cx_dword>(idle_threads_.size()));

  (void)alive_threads_.LockR();
  std::set<CXThread *> setAliveThreadsCone;
  for (CMThreadSet::iterator itr = alive_threads_.begin(); itr != alive_threads_.end(); ++itr) {
    (void)setAliveThreadsCone.insert(*itr);
  }
  (void)alive_threads_.UnlockR();

  for (std::set<CXThread *>::iterator itr = setAliveThreadsCone.begin();
       itr != setAliveThreadsCone.end(); ++itr) {
    (*itr)->Stop();
  }

  (void)killed_threads_.LockW();
  for (CMThreadSet::iterator itr = killed_threads_.begin(); itr != killed_threads_.end(); ++itr) {
    CXThread *pThread = *itr;
    DELETE_S(pThread);
  }
  killed_threads_.clear();
  (void)killed_threads_.UnlockW();

#endif  // WIN32

  return 0;
}

cx_bool CXThreadPool::IsTerminate() {
  return terminate_event_.IsTriggered();
}

cx_int CXThreadPool::SetThreadMinimum(cx_dword dwMin) {
#ifdef WIN32
  SetThreadpoolThreadMinimum(m_pThreadPool, dwMin);
#endif  // WIN32

  return 0;
}

cx_int CXThreadPool::SetThreadMaximum(cx_dword dwMax) {
#ifdef WIN32
  SetThreadpoolThreadMaximum(m_pThreadPool, dwMax);
#endif  // WIN32

  return 0;
}

#ifdef WIN32
VOID NTAPI CXThreadPool::WorkThread(PTP_CALLBACK_INSTANCE Instance, PVOID pContext)
#else
void *CXThreadPool::WorkThread(void *const pContext)
#endif
{
  CXThread *pThread = NULL;
  if (NULL != pContext) {
    pThread = static_cast<CXThread *>(pContext);
  }

  while (1) {
    if (!pThread->IsTerminate()) {
      pThread->Pause();
      ASSERT(pThread->GetWork() != NULL);
      if (pThread->GetWork() != NULL) {
        (void)pThread->Start();
      }

      pThread->AssignWork(NULL);
      pThread->FinishWork();
    }

    const cx_bool bTerminate = GetSingleton4ThreadPool()->ThreadFinishWork(pThread);
    if (CXThreadPool::g_initialized_thread_pool && bTerminate) {
      pThread->SetState(CM_TS_TERMINATED);
      break;
    } else {
      pThread->Pause();
    }
  }

#ifdef LINUX
  return NULL;
#endif
}

cx_int CXThreadPool::SubmitThread(CXThread *pThread) {
  ASSERT(pThread);
  if (NULL == pThread) {
    return -1;
  }

#ifdef WIN32
  cx_bool bSuccess = false;
  bSuccess = TrySubmitThreadpoolCallback(pThread->GetThreadProc(), pThread->GetContext(), &m_cbEnv);
#else

#endif  // WIN32

  return 0;
}

CXThread *CXThreadPool::CreateThread() {
  // #ifdef LINUX
  CXThread *const pNewThread = new CXThread;

  pNewThread->Create(&WorkThread, static_cast<LPVOID>(pNewThread));
  return pNewThread;

  // #else
  //     return NULL;
  // #endif
}

cx_int CXThreadPool::DecreateThreads(const cx_dword count) {
  (void)idle_threads_.LockR();
  CMThreadSet::iterator itr = idle_threads_.begin();
  std::set<CXThread *> setIdleThreadClone;
  for (cx_dword i = 0U; i < count; ++i) {
    if (itr != idle_threads_.end()) {
      (void)setIdleThreadClone.insert(*itr);
      ++itr;
    }
  }
  (void)idle_threads_.UnlockR();

  for (CMThreadSet::iterator itrClone = setIdleThreadClone.begin();
       itrClone != setIdleThreadClone.end(); ++itrClone) {
    (*itrClone)->Stop();
  }

  return 0;
}

cx_bool CXThreadPool::ThreadFinishWork(CXThread *const pThread) {
  cx_bool bExit = false;
  if (NULL != pThread) {
    static cx_bool bThreadPoolExit = false;

    (void)pThread->Reset();

    if (IsTerminate()) {
      bThreadPoolExit = true;
    }

    bExit = bThreadPoolExit;

    if (!IsTerminate()) {
      ReleaseThread(pThread);
    } else {
      bExit = true;
    }

    if (!bExit) {
      bExit = pThread->IsTerminate();
    }

    if (bExit) {
      (void)idle_threads_.LockW();
      (void)idle_threads_.erase(pThread);
      (void)idle_threads_.UnlockW();

      (void)alive_threads_.LockW();
      (void)alive_threads_.erase(pThread);
      (void)alive_threads_.UnlockW();

      (void)killed_threads_.LockW();
      (void)killed_threads_.insert(pThread);
      (void)killed_threads_.UnlockW();
    }
  } else {
    bExit = true;
  }

  return bExit;
}

cx_int CXThreadPool::ReleaseThread(CXThread *const pThread) {
  ASSERT(pThread);

  if (NULL != pThread) {
    (void)alive_threads_.LockW();
    (void)alive_threads_.erase(pThread);
    (void)alive_threads_.UnlockW();

    (void)idle_threads_.LockW();
    (void)idle_threads_.insert(pThread);
    (void)idle_threads_.UnlockW();
  }

  return 0;
}

CXThread *CXThreadPool::GetIdleThread() {
  CXThread *pThread = NULL;

  (void)idle_threads_.LockW();

  if (!idle_threads_.empty()) {
    pThread = *idle_threads_.begin();
    (void)idle_threads_.erase(pThread);
  } else {
    pThread = CreateThread();
  }

  (void)idle_threads_.UnlockW();

  return pThread;
}

cx_int CXThreadPool::ExecuteTask(CXWork *const pWork, CXThread *&pReturnThread) {
  CXThread *const pThread = GetIdleThread();
  pReturnThread = pThread;

  if (NULL != pThread) {
    (void)alive_threads_.LockW();
    (void)alive_threads_.insert(pThread);
    (void)alive_threads_.UnlockW();

    pThread->AssignWork(pWork);

    pThread->Resume();
  }

  ASSERT(pThread);

  if (pThread) {
    SubmitThread(pThread);
  }

  return 0;
}
}  // namespace common_lib