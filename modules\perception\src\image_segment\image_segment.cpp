
#include "image_segment/image_segment.h"

#ifdef NEURAL_RKNN
    #include "image_segment/image_segment_rknn.h"
#elif defined NEURAL_CPU
    #include "image_segment/image_segment_cpu.h"
#elif defined NEURAL_CUDA
    #include "image_segment/image_segment_cuda.h"
#else
    #include error:No nerual type define
#endif

using namespace perception;

ImageSegment::ImageSegment(){

#ifdef NEURAL_RKNN
    image_segment_impl_ = std::make_unique<ImageSegmentRknn>();
#elif defined NEURAL_CPU
    image_segment_impl_ = std::make_unique<ImageSegmentCpu>();
#elif defined NEURAL_CUDA
    image_segment_impl_ = std::make_unique<ImageSegmentCuda>();
#else
    error : No nerual type define;   // 编译报错
#endif


}
 ImageSegment::~ImageSegment(){

 }

bool ImageSegment::Init(std::string model_path){
    return image_segment_impl_->Init(model_path);
}
bool ImageSegment::Inference(cv::Mat src,float box_thresh,float nms_thresh,SegmentDetectResult& out){
    return image_segment_impl_->Inference(src,box_thresh,nms_thresh,out);
}

cv::Mat ImageSegment::DebugImg(cv::Mat src,const SegmentDetectResult& segment) {
    return image_segment_impl_->DebugImg(src,segment);
}