#pragma once
#include <ros/ros.h>
#include <std_msgs/Int8.h>
#include "publisher_base.h"

namespace communication::ros1 {
class Int8DataPublisherRos1 : public Int8DataPublisherBase {
public:
    Int8DataPublisherRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size = 10);

    ~Int8DataPublisherRos1() = default;

protected:
    virtual void PublishMsg() override {
        publisher_.publish(msg_);
    }

    virtual void ToMsg() override {
        msg_.data = data_.data;
    }

    int GetSubscriberCount() const  {
        return publisher_.getNumSubscribers();
    }

private:
    ros::NodeHandle &nh_;
    ros::Publisher publisher_;
    std_msgs::Int8 msg_;
};

} // namespace communication::ros1 