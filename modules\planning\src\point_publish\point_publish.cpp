#include "point_publish.h"
#include <iostream>
#include <iomanip>

using namespace std;
using namespace point_publish;

/**
 * @brief 构造函数
 */
PointPublisher::PointPublisher(const std::string& name) 
    : name_(name), initialized_(false), running_(false) {
}

/**
 * @brief 析构函数
 */
PointPublisher::~PointPublisher() {
    stop();
}

/**
 * @brief 初始化
 */
bool PointPublisher::init() {
    if (initialized_) {
        std::cout << "PointPublisher already initialized" << std::endl;
        return true;
    }
    
    initialized_ = true;
    std::cout << "PointPublisher initialized successfully" << std::endl;
    return true;
}

/**
 * @brief 启动
 */
void PointPublisher::start() {
    if (!initialized_) {
        std::cerr << "PointPublisher not initialized" << std::endl;
        return;
    }
    
    if (running_) {
        std::cout << "PointPublisher already running" << std::endl;
        return;
    }
    
    running_ = true;
    worker_thread_ = std::thread(&PointPublisher::controlLoop, this);
    std::cout << "PointPublisher started" << std::endl;
}

/**
 * @brief 停止
 */
void PointPublisher::stop() {
    if (running_) {
        running_ = false;
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        std::cout << "PointPublisher stopped" << std::endl;
    }
}

/**
 * @brief 输入目标点 (替换ROS订阅)
 */
void PointPublisher::inputGoal(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    goalHandler(std::make_shared<const PoseStamped>(goal));
}

/**
 * @brief 输入Web目标点 (替换ROS订阅)
 */
void PointPublisher::inputWebGoal(const PoseStamped& webgoal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    webgoalHandler(std::make_shared<const PoseStamped>(webgoal));
}


void PointPublisher::setWebGoalTargetCallback(std::function<void(const std::shared_ptr<PoseStamped>&)> callback) {
    web_goal_callback_ = callback;
}

/**
 * @brief 状态查询函数
 */
bool PointPublisher::isInitialized() const {
    return initialized_;
}

bool PointPublisher::isRunning() const {
    return running_;
}

/**
 * @brief 控制循环 (替换原有的ROS主循环)
 */
void PointPublisher::controlLoop() {
    const double loop_rate = 100.0; // 100Hz，保持与原有频率一致
    const auto sleep_duration = std::chrono::duration<double>(1.0 / loop_rate);

    while (running_) {
        // 保持运行状态，等待输入
        std::this_thread::sleep_for(sleep_duration);
    }
}

void PointPublisher::goalHandler(const std::shared_ptr<const point_publish::PoseStamped>& goal) {

    std::cout << "[goalHandler] 接收到目标点: ("
              << goal->pose.position.x << ", "
              << goal->pose.position.y << ", "
              << goal->pose.position.z << ")" << std::endl;
}

void PointPublisher::webgoalHandler(const std::shared_ptr<const point_publish::PoseStamped>& webgoal) {

    std::cout << "[webgoalHandler] 接收到Web目标点: ("
              << webgoal->pose.position.x << ", "
              << webgoal->pose.position.y << ", "
              << webgoal->pose.position.z << ")" << std::endl;
}

