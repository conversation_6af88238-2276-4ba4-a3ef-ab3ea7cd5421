/**
 * @file rrt_star_test_node.cpp
 * @brief RRT*全局规划器测试节点
 * <AUTHOR> Assistant
 * @date 2024
 * @description 用于测试RRT*全局规划器功能的测试节点
 *              支持ROS1话题数据接收、模拟测试和可视化输出
 */

#include "communication.h"
#include "rrt_star_global_planner.h"
#include <chrono>
#include <iomanip>
#include <iostream>
#include <memory>
#include <thread>
#include <signal.h>
#include <random>
#include <fstream>
#include <sstream>

using namespace RRTstar_planner;

static bool g_running = true;

// 全局对象
std::unique_ptr<RRTstarPlanner> rrt_star_planner_ptr;
std::shared_ptr<communication::Communication> communication_ptr;

// 订阅器
std::shared_ptr<communication::OdometrySubscriberBase> odom_subscriber_ptr;
std::shared_ptr<communication::PoseDataSubscriberBase> goal_subscriber_ptr;
std::shared_ptr<communication::CloudDataSubscriberBase> map_subscriber_ptr;

// 发布器
std::shared_ptr<communication::PathDataPublisherBase> path_publisher_ptr;
std::shared_ptr<communication::BoolDataPublisherBase> accessable_publisher_ptr;
std::shared_ptr<communication::CloudDataPublisherBase> tree_visualization_publisher_ptr;

// 测试配置
struct TestConfig {
    bool enable_ros_test = true;           // 启用ROS数据测试
    bool enable_simulation_test = true;    // 启用模拟测试
    bool enable_visualization = true;      // 启用可视化
    bool enable_debug_output = true;       // 启用调试输出
    double test_duration = 300.0;          // 测试持续时间(秒)
    double goal_update_interval = 5.0;     // 目标点更新间隔(秒)
    double robot_speed = 0.5;              // 机器人移动速度(m/s)
    std::string map_file = "";             // 地图文件路径
    std::string output_file = "test_results.txt"; // 测试结果输出文件
};

TestConfig test_config;

/**
 * @brief 信号处理函数，用于优雅退出
 * @param signum 信号编号
 */
void signalHandler(int signum) {
    std::cout << "\n收到信号 " << signum << "，正在关闭RRT*测试节点..."
              << std::endl;
    g_running = false;
    if (rrt_star_planner_ptr) {
        rrt_star_planner_ptr->stop();
        rrt_star_planner_ptr->cleanup();
    }
    std::cout << " RRT*测试节点已安全退出" << std::endl;
}

/**
 * @brief 加载测试配置
 * @param config_file 配置文件路径
 * @return 是否加载成功
 */
bool loadTestConfig(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cout << "配置文件不存在，使用默认配置" << std::endl;
        return false;
    }
    
    std::string line;
    while (std::getline(file, line)) {
        std::istringstream iss(line);
        std::string key, value;
        if (std::getline(iss, key, ':') && std::getline(iss, value)) {
            // 去除空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);
            
            if (key == "enable_ros_test") {
                test_config.enable_ros_test = (value == "true");
            } else if (key == "enable_simulation_test") {
                test_config.enable_simulation_test = (value == "true");
            } else if (key == "enable_visualization") {
                test_config.enable_visualization = (value == "true");
            } else if (key == "enable_debug_output") {
                test_config.enable_debug_output = (value == "true");
            } else if (key == "test_duration") {
                test_config.test_duration = std::stod(value);
            } else if (key == "goal_update_interval") {
                test_config.goal_update_interval = std::stod(value);
            } else if (key == "robot_speed") {
                test_config.robot_speed = std::stod(value);
            } else if (key == "map_file") {
                test_config.map_file = value;
            } else if (key == "output_file") {
                test_config.output_file = value;
            }
        }
    }
    
    file.close();
    std::cout << "测试配置加载成功" << std::endl;
    return true;
}

/**
 * @brief 打印测试配置
 */
void printTestConfig() {
    std::cout << "\n=== 测试配置 ===" << std::endl;
    std::cout << "ROS测试: " << (test_config.enable_ros_test ? "启用" : "禁用") << std::endl;
    std::cout << "模拟测试: " << (test_config.enable_simulation_test ? "启用" : "禁用") << std::endl;
    std::cout << "可视化: " << (test_config.enable_visualization ? "启用" : "禁用") << std::endl;
    std::cout << "调试输出: " << (test_config.enable_debug_output ? "启用" : "禁用") << std::endl;
    std::cout << "测试持续时间: " << test_config.test_duration << " 秒" << std::endl;
    std::cout << "目标点更新间隔: " << test_config.goal_update_interval << " 秒" << std::endl;
    std::cout << "机器人速度: " << test_config.robot_speed << " m/s" << std::endl;
    std::cout << "地图文件: " << (test_config.map_file.empty() ? "无" : test_config.map_file) << std::endl;
    std::cout << "输出文件: " << test_config.output_file << std::endl;
}

/**
 * @brief 创建测试地图
 * @return 测试地图指针
 */
std::unique_ptr<Costmap2D> createTestMap() {
    // 创建一个简单的测试地图 (20x20米，分辨率0.1米)
    const int map_size = 200;  // 200x200像素
    const double resolution = 0.1;  // 0.1米/像素
    const double origin_x = -10.0;  // 地图原点
    const double origin_y = -10.0;
    
    auto test_map = std::make_unique<Costmap2D>(map_size, map_size, resolution, origin_x, origin_y);
    
    // 添加一些障碍物
    // 中心障碍物
    for (int x = 80; x < 120; ++x) {
        for (int y = 80; y < 120; ++y) {
            test_map->setCost(x, y, 100);
        }
    }
    
    // 左侧障碍物
    for (int x = 30; x < 70; ++x) {
        for (int y = 30; y < 70; ++y) {
            test_map->setCost(x, y, 100);
        }
    }
    
    // 右侧障碍物
    for (int x = 130; x < 170; ++x) {
        for (int y = 130; y < 170; ++y) {
            test_map->setCost(x, y, 100);
        }
    }
    
    // 添加一些随机障碍物
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, map_size - 1);
    
    for (int i = 0; i < 50; ++i) {
        int x = dis(gen);
        int y = dis(gen);
        test_map->setCost(x, y, 100);
    }
    
    std::cout << "测试地图创建完成: " << map_size << "x" << map_size 
              << " 像素, 分辨率 " << resolution << " m/像素" << std::endl;
    
    return test_map;
}

/**
 * @brief 生成随机目标点
 * @param current_pose 当前位姿
 * @param map 地图
 * @return 目标位姿
 */
PoseStamped generateRandomGoal(const PoseStamped& current_pose, const Costmap2D* map) {
    std::random_device rd;
    std::mt19937 gen(rd());
    
    PoseStamped goal;
    goal.time = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    // 在地图范围内生成随机目标点
    double min_x = map->origin_x + 2.0;  // 留出边界
    double max_x = map->origin_x + map->width * map->resolution - 2.0;
    double min_y = map->origin_y + 2.0;
    double max_y = map->origin_y + map->height * map->resolution - 2.0;
    
    std::uniform_real_distribution<> x_dist(min_x, max_x);
    std::uniform_real_distribution<> y_dist(min_y, max_y);
    std::uniform_real_distribution<> yaw_dist(-M_PI, M_PI);
    
    do {
        goal.pose.position.x = x_dist(gen);
        goal.pose.position.y = y_dist(gen);
        goal.pose.position.z = 0.0;
        
        // 检查目标点是否在障碍物内
        int mx, my;
        if (map->worldToMap(goal.pose.position.x, goal.pose.position.y, mx, my)) {
            if (map->getCost(mx, my) < 50) {  // 自由空间
                break;
            }
        }
    } while (true);
    
    // 设置朝向
    double yaw = yaw_dist(gen);
    goal.pose.orientation.x = 0.0;
    goal.pose.orientation.y = 0.0;
    goal.pose.orientation.z = sin(yaw / 2.0);
    goal.pose.orientation.w = cos(yaw / 2.0);
    
    return goal;
}

/**
 * @brief ROS数据接收线程
 */
void ROSDataReceiveThread() {
    if (!test_config.enable_ros_test) {
        return;
    }
    
    std::cout << "启动ROS数据接收线程..." << std::endl;
    
    while (g_running) {
        // 接收里程计数据
        if (!odom_subscriber_ptr->IsBufferEmpty()) {
            auto msg = odom_subscriber_ptr->GetBuffer();
            auto odom_data = msg.front();
            
            if (test_config.enable_debug_output) {
                std::cout << "接收到里程计数据: 位置=(" 
                          << odom_data.pose.pose.position.x << ", "
                          << odom_data.pose.pose.position.y << ")" << std::endl;
            }
            
            rrt_star_planner_ptr->updateOdometry(odom_data);
        }

        // 接收目标点数据
        if (!goal_subscriber_ptr->IsBufferEmpty()) {
            auto msg = goal_subscriber_ptr->GetBuffer();
            auto goal_data = msg.front();
            
            // 转换为PoseStamped格式
            PoseStamped goal_pose;
            goal_pose.time = goal_data.time;
            goal_pose.pose.position.x = goal_data.position[0];
            goal_pose.pose.position.y = goal_data.position[1];
            goal_pose.pose.position.z = goal_data.position[2];
            goal_pose.pose.orientation.x = goal_data.orientation[0];
            goal_pose.pose.orientation.y = goal_data.orientation[1];
            goal_pose.pose.orientation.z = goal_data.orientation[2];
            goal_pose.pose.orientation.w = goal_data.orientation[3];
            
            if (test_config.enable_debug_output) {
                std::cout << "接收到目标点: 位置=(" 
                          << goal_pose.pose.position.x << ", "
                          << goal_pose.pose.position.y << ")" << std::endl;
            }
            
            rrt_star_planner_ptr->updateGoal(goal_pose);
        }
        
        // 接收地图数据
        if (!map_subscriber_ptr->IsBufferEmpty()) {
            auto msg = map_subscriber_ptr->GetBuffer();
            auto map_data = msg.front();
            
            if (test_config.enable_debug_output) {
                std::cout << "接收到地图数据: " << map_data.cloud_ptr->points.size() << " 个点" << std::endl;
            }
            
            // TODO: 将点云数据转换为代价地图
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

/**
 * @brief 模拟测试线程
 */
void SimulationTestThread() {
    if (!test_config.enable_simulation_test) {
        return;
    }
    
    std::cout << "启动模拟测试线程..." << std::endl;
    
    // 创建测试地图
    auto test_map = createTestMap();
    rrt_star_planner_ptr->setCostmap(test_map.get());
    
    // 设置初始机器人位姿
    PoseStamped current_pose;
    current_pose.time = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    current_pose.pose.position.x = -8.0;
    current_pose.pose.position.y = -8.0;
    current_pose.pose.position.z = 0.0;
    current_pose.pose.orientation.x = 0.0;
    current_pose.pose.orientation.y = 0.0;
    current_pose.pose.orientation.z = 0.0;
    current_pose.pose.orientation.w = 1.0;
    
    rrt_star_planner_ptr->updatePoseData(current_pose);
    
    auto start_time = std::chrono::steady_clock::now();
    auto last_goal_time = start_time;
    
    while (g_running) {
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed_time = std::chrono::duration<double>(current_time - start_time).count();
        
        // 检查测试时间
        if (elapsed_time > test_config.test_duration) {
            std::cout << "测试时间结束" << std::endl;
            break;
        }
        
        // 定期更新目标点
        auto goal_elapsed = std::chrono::duration<double>(current_time - last_goal_time).count();
        if (goal_elapsed > test_config.goal_update_interval) {
            PoseStamped goal = generateRandomGoal(current_pose, test_map.get());
            
            if (test_config.enable_debug_output) {
                std::cout << "生成新目标点: (" << goal.pose.position.x << ", " 
                          << goal.pose.position.y << ")" << std::endl;
            }
            
            rrt_star_planner_ptr->updateGoal(goal);
            last_goal_time = current_time;
        }
        
        // 模拟机器人移动
        if (rrt_star_planner_ptr->hasValidPose() && rrt_star_planner_ptr->hasValidGoal()) {
            // 简单的直线移动模拟
            PoseStamped current = rrt_star_planner_ptr->getCurrentPose();
            PoseStamped goal = rrt_star_planner_ptr->getCurrentGoal();
            
            double dx = goal.pose.position.x - current.pose.position.x;
            double dy = goal.pose.position.y - current.pose.position.y;
            double distance = sqrt(dx * dx + dy * dy);
            
            if (distance > 0.1) {  // 如果距离目标还有一定距离
                double step = test_config.robot_speed * 0.1;  // 10ms的移动距离
                double ratio = std::min(step / distance, 1.0);
                
                current.pose.position.x += dx * ratio;
                current.pose.position.y += dy * ratio;
                current.time = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count();
                
                rrt_star_planner_ptr->updatePoseData(current);
            }
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

/**
 * @brief 状态监控线程
 */
void StatusMonitorThread() {
    std::cout << "启动状态监控线程..." << std::endl;
    
    auto start_time = std::chrono::steady_clock::now();
    int status_counter = 0;
    
    while (g_running) {
        if (rrt_star_planner_ptr && rrt_star_planner_ptr->isInitialized()) {
            status_counter++;
            
            // 每10秒打印一次状态信息
            if (status_counter >= 1000) {  // 1000 * 10ms = 10秒
                auto current_time = std::chrono::steady_clock::now();
                auto elapsed_time = std::chrono::duration<double>(current_time - start_time).count();
                
                std::cout << "\n=== RRT*规划器状态监控 (运行时间: " 
                          << std::fixed << std::setprecision(1) << elapsed_time << "s) ===" << std::endl;
                
                // 打印基本状态
                std::cout << "规划器状态: " << (rrt_star_planner_ptr->isRunning() ? "运行中" : "已停止") << std::endl;
                std::cout << "当前位姿: " << (rrt_star_planner_ptr->hasValidPose() ? "有效" : "无效") << std::endl;
                std::cout << "目标位姿: " << (rrt_star_planner_ptr->hasValidGoal() ? "有效" : "无效") << std::endl;
                
                // 打印统计信息
                size_t total_attempts, successful_attempts;
                double success_rate = rrt_star_planner_ptr->getPlanningSuccessRate(total_attempts, successful_attempts);
                
                if (total_attempts > 0) {
                    std::cout << "规划统计:" << std::endl;
                    std::cout << "  成功率: " << std::fixed << std::setprecision(1) 
                              << (success_rate * 100.0) << "%" << std::endl;
                    std::cout << "  总尝试: " << total_attempts << ", 成功: " << successful_attempts << std::endl;
                    std::cout << "  平均规划时间: " << std::fixed << std::setprecision(3) 
                              << rrt_star_planner_ptr->getAveragePlanningTime() << " 秒" << std::endl;
                    std::cout << "  平均节点数: " << rrt_star_planner_ptr->getAveragePlanningNodes() << std::endl;
                    std::cout << "  平均路径长度: " << std::fixed << std::setprecision(2) 
                              << rrt_star_planner_ptr->getAveragePathLength() << " 米" << std::endl;
                }
                
                status_counter = 0;
            }
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

/**
 * @brief 保存测试结果
 */
void saveTestResults() {
    std::ofstream file(test_config.output_file);
    if (!file.is_open()) {
        std::cerr << "无法打开输出文件: " << test_config.output_file << std::endl;
        return;
    }
    
    file << "=== RRT*全局规划器测试结果 ===" << std::endl;
    file << "测试时间: " << std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count() << std::endl;
    
    if (rrt_star_planner_ptr) {
        size_t total_attempts, successful_attempts;
        double success_rate = rrt_star_planner_ptr->getPlanningSuccessRate(total_attempts, successful_attempts);
        
        file << "规划统计:" << std::endl;
        file << "  成功率: " << std::fixed << std::setprecision(1) << (success_rate * 100.0) << "%" << std::endl;
        file << "  总尝试: " << total_attempts << std::endl;
        file << "  成功次数: " << successful_attempts << std::endl;
        file << "  平均规划时间: " << std::fixed << std::setprecision(3) 
             << rrt_star_planner_ptr->getAveragePlanningTime() << " 秒" << std::endl;
        file << "  平均节点数: " << rrt_star_planner_ptr->getAveragePlanningNodes() << std::endl;
        file << "  平均路径长度: " << std::fixed << std::setprecision(2) 
             << rrt_star_planner_ptr->getAveragePathLength() << " 米" << std::endl;
    }
    
    file.close();
    std::cout << "测试结果已保存到: " << test_config.output_file << std::endl;
}

int main(int argc, char **argv) {
    std::cout << "=== RRT*全局规划器测试节点启动 ===" << std::endl;
    
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    try {
        // 加载测试配置
        std::string config_file = "config/test_config.yaml";
        if (argc > 1) {
            config_file = argv[1];
        }
        loadTestConfig(config_file);
        printTestConfig();
        
        // 创建通信模块实例
        communication_ptr = std::make_shared<communication::Communication>("rrt_star_test");
        if (!communication_ptr->Initialize("config/communication_config.yaml")) {
            std::cerr << "通信模块初始化失败" << std::endl;
            return -1;
        }
        
        // 创建RRT*规划器
        std::cout << "\n创建RRT*全局规划器..." << std::endl;
        rrt_star_planner_ptr = std::make_unique<RRTstarPlanner>("rrt_star_test", nullptr);
        
        // 设置规划器参数
        rrt_star_planner_ptr->setPlanningMode(0);  // 标准模式
        rrt_star_planner_ptr->setGoalBiasProbability(0.2);
        rrt_star_planner_ptr->setUseBidirectionalSearch(true);
        rrt_star_planner_ptr->setEnableVisualization(test_config.enable_visualization);
        rrt_star_planner_ptr->setEnableDebug(test_config.enable_debug_output);
        rrt_star_planner_ptr->setPlanningFrequency(10.0);
        rrt_star_planner_ptr->setCollisionThreshold(50);
        rrt_star_planner_ptr->setPathSmoothingFactor(0.5);
        rrt_star_planner_ptr->setPathSimplifyTolerance(0.1);
        
        // 创建订阅器和发布器
        if (test_config.enable_ros_test) {
            odom_subscriber_ptr = communication_ptr->CreateOdometrySubscriber("/odom");
            goal_subscriber_ptr = communication_ptr->CreatePoseDataSubscriber("/move_base_simple/goal");
            map_subscriber_ptr = communication_ptr->CreateCloudDataSubscriber("/map");
        }
        
        path_publisher_ptr = communication_ptr->CreatePathDataPublisher("/test_global_path", "map", 10);
        accessable_publisher_ptr = communication_ptr->CreateBoolDataPublisher("/test_path_accessable", 10);
        
        if (test_config.enable_visualization) {
            tree_visualization_publisher_ptr = communication_ptr->CreateCloudDataPublisher("/test_tree_visualization", "map", 10);
        }
        
        // 设置RRT*规划器的回调函数
        rrt_star_planner_ptr->setPathPublishCallback(
            [](const std::vector<PoseStamped>& plan) {
                communication::PathData path_msg;
                path_msg.time_ = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count();
                path_msg.poses_.clear();
                
                for (const auto& pose : plan) {
                    communication::PoseData pose_data;
                    pose_data.time = pose.time;
                    pose_data.position[0] = pose.pose.position.x;
                    pose_data.position[1] = pose.pose.position.y;
                    pose_data.position[2] = pose.pose.position.z;
                    pose_data.orientation[0] = pose.pose.orientation.x;
                    pose_data.orientation[1] = pose.pose.orientation.y;
                    pose_data.orientation[2] = pose.pose.orientation.z;
                    pose_data.orientation[3] = pose.pose.orientation.w;
                    path_msg.poses_.push_back(pose_data);
                }
                
                path_publisher_ptr->Publish(path_msg);
                
                if (test_config.enable_debug_output) {
                    std::cout << "发布路径: " << plan.size() << " 个路径点" << std::endl;
                }
            }
        );
        
        rrt_star_planner_ptr->setAccessablePublishCallback(
            [](bool accessable) {
                accessable_publisher_ptr->Publish(accessable);
                
                if (test_config.enable_debug_output) {
                    std::cout << "路径可达性: " << (accessable ? "可达" : "不可达") << std::endl;
                }
            }
        );
        
        // 初始化规划器
        std::cout << "\n初始化RRT*全局规划器..." << std::endl;
        if (!rrt_star_planner_ptr->isInitialized()) {
            std::cerr << "RRT*全局规划器初始化失败！" << std::endl;
            return -1;
        }
        
        std::cout << "\nRRT*测试节点启动成功！" << std::endl;
        std::cout << "测试功能:" << std::endl;
        if (test_config.enable_ros_test) {
            std::cout << "  - ROS话题数据接收" << std::endl;
        }
        if (test_config.enable_simulation_test) {
            std::cout << "  - 模拟测试" << std::endl;
        }
        if (test_config.enable_visualization) {
            std::cout << "  - 可视化输出" << std::endl;
        }
        std::cout << "  - 状态监控" << std::endl;
        std::cout << "  - 结果统计" << std::endl;
        
        // 启动测试线程
        std::thread ros_thread(ROSDataReceiveThread);
        std::thread sim_thread(SimulationTestThread);
        std::thread monitor_thread(StatusMonitorThread);
        
        ros_thread.detach();
        sim_thread.detach();
        monitor_thread.detach();
        
        // 阻塞主线程，运行通信模块
        communication_ptr->Run();
        
    } catch (const std::exception &e) {
        std::cerr << "程序异常: " << e.what() << std::endl;
        return -1;
    }
    
    // 保存测试结果
    saveTestResults();
    
    return 0;
} 