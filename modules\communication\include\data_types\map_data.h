#pragma once
#include <string>
#include <vector>
#include <cstdint>

namespace communication {

struct OccupancyGrid {
    struct Header {
        double stamp = 0.0; // 时间戳（秒）
        std::string frame_id;
    } header;
    struct Info {
        double resolution = 0.0;
        uint32_t width = 0;
        uint32_t height = 0;
        struct {
            double x = 0.0, y = 0.0, z = 0.0;
        } origin_position;
        struct {
            double x = 0.0, y = 0.0, z = 0.0, w = 1.0;
        } origin_orientation;
    } info;
    std::vector<int8_t> data;
    OccupancyGrid() = default;
};

} // namespace communication 