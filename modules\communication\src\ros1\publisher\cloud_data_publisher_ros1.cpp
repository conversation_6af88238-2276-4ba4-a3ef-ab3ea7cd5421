#include "publisher/cloud_data_publisher_ros1.h"
// #include <pcl_conversions/pcl_conversions.h>

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1{

CloudDataPublisherRos1::CloudDataPublisherRos1(ros::NodeHandle &nh, const std::string &topic, 
                const std::string& frame_id, size_t max_buffer_size)
            : CloudDataPublisherBase(topic, max_buffer_size), nh_(nh), frame_id_(frame_id)
{
    publisher_ = nh_.advertise<sensor_msgs::PointCloud2>(topic, max_buffer_size, true); //保存最后一条发布的消息
}

}   // namespace communication::ros1{

#endif
    