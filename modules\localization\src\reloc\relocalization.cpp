#include "relocalization.h"
#include "cx_singleton.h"
// #include "CMPath.h"
// #include "Yamlconfig.h"
// #include "CMSingleton.h"


Relocalization* GetSingleton4Relocalization()
{
    static CXSingleton<Relocalization> s_Relocalization;
    return s_Relocalization.GetSingletonInstance();
}

Relocalization::Relocalization()
    : m_Config(common_lib::YamlConfig::GetInstance())
   // , m_pThread(nullptr)
   // , m_initialized(false)
    , m_reloc_success(false)
    , m_last_timestamp(0.0)
    , m_bManualPosUpdate(false)
    , m_bNeedRelocal(true)
{
    Initialize();
}

Relocalization::~Relocalization()
{
}

void Relocalization::Initialize()
{
    //m_initialized = false;
    m_sMapPath = m_Config.GetParam<std::string>("loadmappath",
                                                "/mnt/d/work/01_code/jianmi/20250319/na_localization_lslidar/src/na_localization/");
    m_bManualPosUpdate = false;
    m_reloc_success = false;
    m_last_timestamp = 0.0;
    
    //GetMapPath(m_sMapPath);
    m_reloc_params.match_rate_threshold =m_Config.GetParam<double>("match_rate_threshold", 0.75);
    m_reloc_params.local_map_radius = m_Config.GetParam<double>("local_map_radius", 100.0);
    cout<< "Relocalization::Initialize: m_sMapPath = " << m_sMapPath << endl;
    cout<< "Relocalization::Initialize: m_reloc_params.match_rate_threshold = " << m_reloc_params.match_rate_threshold << endl;
    cout<< "Relocalization::Initialize: m_reloc_params.local_map_radius = " << m_reloc_params.local_map_radius << endl;
    m_ptrRelocplugin = std::make_shared<plugins::RelocPlugin>(m_reloc_params, m_sMapPath);
}

// void Relocalization::UpdateRelocPos(const ManualPos& pose)
// {
//     m_manualPos.manualpos = pose.manualpos;
//     m_manualPos.ext_q = pose.ext_q;
//     m_bManualPosUpdate = true;
//     return; 
// }

// void Relocalization::UpdateInputCloud(const CloudData::CLOUD_PTR& input_cloud_ptr)
// {
//     m_input_cloud_ptr = input_cloud_ptr;
// }

bool Relocalization::ManualPosReloc(const ManualPos& manualpos, const PointCloudXYZI::Ptr& input_cloud_ptr)
{
    bool reloSuccess_ = false; 
    //static double high = min_z;
    // double min_score=100.0;
    Eigen::Matrix4f transformation = Eigen::Matrix4f::Identity();

    pcl::PointCloud<PointType>::Ptr mapreposition(new pcl::PointCloud<PointType>());
    PointType pos_repos;

    pos_repos.x = manualpos.manualpos[0];
    pos_repos.y = manualpos.manualpos[1];
    pos_repos.z = manualpos.manualpos[2];

    // Convert PointXYZINormal cloud to PointXYZI cloud
    cout<<"Manual input cloud ptr size = " << input_cloud_ptr->points.size() << endl;
    pcl::PointCloud<pcl::PointXYZI>::Ptr converted_cloud(new pcl::PointCloud<pcl::PointXYZI>);
    converted_cloud->points.resize(input_cloud_ptr->points.size());
    converted_cloud->header = input_cloud_ptr->header;
    converted_cloud->width = input_cloud_ptr->width;
    converted_cloud->height = input_cloud_ptr->height;
    
    for (size_t i = 0; i < input_cloud_ptr->points.size(); ++i) {
        converted_cloud->points[i].x = input_cloud_ptr->points[i].x;
        converted_cloud->points[i].y = input_cloud_ptr->points[i].y;
        converted_cloud->points[i].z = input_cloud_ptr->points[i].z;
        converted_cloud->points[i].intensity = input_cloud_ptr->points[i].intensity;
    }

    m_ptrRelocplugin->readLidar(converted_cloud);
    
    cout << "pos_repos.z = " << pos_repos.z << endl;

    cerr << "manu local relco call !!!" << endl;

    cout << "search radius " << m_search_radius << endl;
    
    utils::Pose init_pose(pos_repos.x, pos_repos.y, pos_repos.z, 0, 0, 0);

    reloSuccess_ = m_ptrRelocplugin->localRelocByBfs(init_pose, m_search_radius);
    return reloSuccess_;
}

bool Relocalization::rtk_reloc(const GnssENU & rtk_pos_data, const PointCloudXYZI::Ptr& input_cloud_ptr)
{
    bool reloSuccess_ = false; 
    PointType pos_repos;
    pos_repos.x = rtk_pos_data.enu_x;
    pos_repos.y = rtk_pos_data.enu_y;
    pos_repos.z = rtk_pos_data.enu_z;

    // Convert PointXYZINormal cloud to PointXYZI cloud
    pcl::PointCloud<pcl::PointXYZI>::Ptr converted_cloud(new pcl::PointCloud<pcl::PointXYZI>);
    converted_cloud->points.resize(input_cloud_ptr->points.size());
    converted_cloud->header = input_cloud_ptr->header;
    converted_cloud->width = input_cloud_ptr->width;
    converted_cloud->height = input_cloud_ptr->height;
    
    for (size_t i = 0; i < input_cloud_ptr->points.size(); ++i) {
        converted_cloud->points[i].x = input_cloud_ptr->points[i].x;
        converted_cloud->points[i].y = input_cloud_ptr->points[i].y;
        converted_cloud->points[i].z = input_cloud_ptr->points[i].z;
        converted_cloud->points[i].intensity = input_cloud_ptr->points[i].intensity;
    }
    
    m_ptrRelocplugin->readLidar(converted_cloud);
    cout << "pos_repos.z = " << pos_repos.z << endl;
    cerr << "rtk local relco call !!!" << endl;
    // << "位姿可能半径： " << search_radius << endl;
    utils::Pose init_pose(pos_repos.x, pos_repos.y, pos_repos.z, 0, 0, 0);
    reloSuccess_ = m_ptrRelocplugin->localRelocByBfs(init_pose, m_search_radius);
    return reloSuccess_;
}

utils::Pose Relocalization::getRelocPos()
{
    return m_ptrRelocplugin->getRelocPose();
}

// bool Relocalization::bNeedReloc()
// {
//     return m_bNeedRelocal;
// }
