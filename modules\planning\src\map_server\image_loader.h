#ifndef IMAGE_LOADER_H
#define IMAGE_LOADER_H

#include <string>
#include <vector>
#include <opencv2/core.hpp>

class ImageLoader {
public:
    ImageLoader(const std::string& map_file, double threshold_occupied, double threshold_free);
    int getWidth() const { return width_; }
    int getHeight() const { return height_; }
    const std::vector<int8_t>& getData() const { return data_; }

private:
    int width_;
    int height_;
    std::vector<int8_t> data_;
};

#endif // IMAGE_LOADER_H 