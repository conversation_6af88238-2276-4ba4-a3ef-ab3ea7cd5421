/**
 * decision模块对外通信
 */

 #pragma once

#include <deque>
#include "communication.h"
#include "imu_data.h"
#include "lidar_point_cloud.h"
#include "path_data.h"
#include "decision_output.h"

namespace decision{

using namespace communication;
class DecisionComm
{
private:
   /* data */
public:
   DecisionComm(const std::string &config_file_path);
   ~DecisionComm() = default;

   bool SubscribImuData(std::deque<IMUData>& dq_imu_data);
   bool SubscribTerrainCloudData(CloudData &cloud_data);
   bool SubscribGlobalPathData(PathData &path_data);
   bool SubscribLocalizationData(PoseVelData &odometry);

   bool PublishDecisionData(const DecisionData& decision_out);

   bool IsTerminated();

   void ShutDown();

private:
   // communication
   //subscriber
   std::shared_ptr<Communication> communication_ptr_;
   std::shared_ptr<ImuDataSubscriberBase> imu_subscriber_ptr_;
   std::shared_ptr<CloudDataSubscriberBase> terrain_subscriber_ptr_;
   std::shared_ptr<PathDataSubscriberBase> globalpath_subscriber_ptr_;
   std::shared_ptr<OdometrySubscriberBase> localization_subscriber_ptr_;

   //publisher
   std::shared_ptr<DecisionOutPublisherBase> decision_out_publisher_ptr_;
};
 

}