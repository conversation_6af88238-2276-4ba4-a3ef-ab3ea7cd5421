#include "communication.h"
#include "local_planner.h"
#include <thread>
#include <memory>
#include <iostream>
#include <signal.h>
#include <chrono>
#include <sstream>
#include <cstring>

using namespace local_planner;
using namespace communication;

// 全局变量
static bool g_running = true;
std::unique_ptr<LocalPlanner> planner_ptr;
std::shared_ptr<OdometrySubscriberBase> odom_subscriber_ptr;
std::shared_ptr<CloudDataSubscriberBase> laser_subscriber_ptr;
std::shared_ptr<CloudDataSubscriberBase> terrain_subscriber_ptr;
std::shared_ptr<PoseDataSubscriberBase> goal_subscriber_ptr;
std::shared_ptr<StringDataSubscriberBase> boundary_subscriber_ptr;
std::shared_ptr<CloudDataSubscriberBase> added_obstacles_subscriber_ptr;
std::shared_ptr<BoolDataSubscriberBase> location_failed_subscriber_ptr;
std::shared_ptr<Int8DataSubscriberBase> calibration_subscriber_ptr;

// 发布器
std::shared_ptr<BoolDataPublisherBase> mode_publisher_ptr;
std::shared_ptr<PathDataPublisherBase> path_publisher_ptr;
std::shared_ptr<Int8DataPublisherBase> stop_publisher_ptr;
std::shared_ptr<Int8DataPublisherBase> inner_stop_publisher_ptr;
std::shared_ptr<Int8DataPublisherBase> replan_publisher_ptr;
std::shared_ptr<BoolDataPublisherBase> node_ready_publisher_ptr;
std::shared_ptr<CloudDataPublisherBase> free_paths_publisher_ptr;

// 信号处理
void signalHandler(int signum) {
    std::cout << "\n收到信号 " << signum << "，正在关闭局部规划器..." << std::endl;
    g_running = false;
    if (planner_ptr) planner_ptr->stopPlanning();
    std::cout << "局部规划器节点已安全退出" << std::endl;
}

// CloudData <-> PointCloud2Data 转换
void CloudDataToPointCloud2Data(const CloudData& src, local_planner::PointCloud2Data& dst) {
    dst.data.resize(src.cloud_ptr->points.size() * sizeof(pcl::PointXYZI));
    std::memcpy(dst.data.data(), src.cloud_ptr->points.data(), dst.data.size());
    dst.height = 1;
    dst.width = src.cloud_ptr->points.size();
}
void PointCloud2DataToCloudData(const local_planner::PointCloud2Data& src, CloudData& dst) {
    size_t n = src.data.size() / sizeof(pcl::PointXYZI);
    dst.cloud_ptr->points.resize(n);
    std::memcpy(dst.cloud_ptr->points.data(), src.data.data(), src.data.size());
}

// boundary字符串转PolygonStamped（假设用逗号分隔x,y,z）
local_planner::PolygonStamped StringToPolygonStamped(const std::string& str) {
    local_planner::PolygonStamped poly;
    std::istringstream iss(str);
    std::string token;
    while (std::getline(iss, token, ';')) {
        std::istringstream ptss(token);
        double x, y, z;
        char comma;
        if (ptss >> x >> comma >> y >> comma >> z) {
            poly.points.emplace_back(local_planner::Point{x, y, z});
        }
    }
    return poly;
}

// 数据接收线程
void DataReceiveThread() {
    while (g_running) {
        if (odom_subscriber_ptr && !odom_subscriber_ptr->IsBufferEmpty()) {
            auto msg = odom_subscriber_ptr->GetBuffer();
            OdometryData odom;
            odom.header.stamp = msg.front().pose_data.time;
            odom.pose.pose.position.x = msg.front().pose_data.position[0];
            odom.pose.pose.position.y = msg.front().pose_data.position[1];
            odom.pose.pose.position.z = msg.front().pose_data.position[2];
            odom.pose.pose.orientation.x = msg.front().pose_data.orientation[0];
            odom.pose.pose.orientation.y = msg.front().pose_data.orientation[1];
            odom.pose.pose.orientation.z = msg.front().pose_data.orientation[2];
            odom.pose.pose.orientation.w = msg.front().pose_data.orientation[3];

            planner_ptr->updateOdometry(odom);
        }
        if (laser_subscriber_ptr && !laser_subscriber_ptr->IsBufferEmpty()) {
            auto msg = laser_subscriber_ptr->GetBuffer();
            PointCloud2Data cloud;
            CloudDataToPointCloud2Data(msg.front(), cloud);
            planner_ptr->updateLaserCloud(cloud);
        }
        if (terrain_subscriber_ptr && !terrain_subscriber_ptr->IsBufferEmpty()) {
            auto msg = terrain_subscriber_ptr->GetBuffer();
            PointCloud2Data cloud;
            CloudDataToPointCloud2Data(msg.front(), cloud);
            planner_ptr->updateTerrainCloud(cloud);
        }
        if (goal_subscriber_ptr && !goal_subscriber_ptr->IsBufferEmpty()) {
            auto msg = goal_subscriber_ptr->GetBuffer();
            PoseStamped goal;
            goal.time = msg.front().time;
            goal.pose.position.x = msg.front().position[0];
            goal.pose.position.y = msg.front().position[1];
            goal.pose.position.z = msg.front().position[2];
            goal.pose.orientation.x = msg.front().orientation[0];
            goal.pose.orientation.y = msg.front().orientation[1];
            goal.pose.orientation.z = msg.front().orientation[2];
            goal.pose.orientation.w = msg.front().orientation[3];
            planner_ptr->updateGoal(goal);
        }
        if (boundary_subscriber_ptr && !boundary_subscriber_ptr->IsBufferEmpty()) {
            auto msg = boundary_subscriber_ptr->GetBuffer();
            PolygonStamped poly = StringToPolygonStamped(msg.front());
            planner_ptr->updateBoundary(poly);
        }
        if (added_obstacles_subscriber_ptr && !added_obstacles_subscriber_ptr->IsBufferEmpty()) {
            auto msg = added_obstacles_subscriber_ptr->GetBuffer();
            PointCloud2Data cloud;
            CloudDataToPointCloud2Data(msg.front(), cloud);
            planner_ptr->updateAddedObstacles(cloud);
        }
        if (location_failed_subscriber_ptr && !location_failed_subscriber_ptr->IsBufferEmpty()) {
            auto msg = location_failed_subscriber_ptr->GetBuffer();
            BoolMsg bmsg;
            bmsg.data = msg.front();
            planner_ptr->updateLocationFailed(bmsg);
        }
        if (calibration_subscriber_ptr && !calibration_subscriber_ptr->IsBufferEmpty()) {
            auto msg = calibration_subscriber_ptr->GetBuffer();
            Int8Msg imsg;
            imsg.data = msg.front().data;
            planner_ptr->updateCalibration(imsg);
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(2));
    }
}

// 新增数据发送线程，主动发布所有需要发布的数据
void DataSendThread() {
    while (g_running) {
        // 1. 发布模式
        if (mode_publisher_ptr && planner_ptr) {
            local_planner::BoolMsg mode = planner_ptr->getCurrentMode();
            mode_publisher_ptr->Publish(mode.data);
        }
        // 2. 发布路径
        if (path_publisher_ptr && planner_ptr) {
            local_planner::PathData path = planner_ptr->getCurrentPath();
            communication::PathData msg;
            msg.time_ = path.header.stamp.sec;
            for (const auto& pt : path.poses_) {
                communication::PoseData pose;
                pose.time = path.header.stamp.sec;
                pose.position = Eigen::Vector3f(pt.pose.position.x, pt.pose.position.y, pt.pose.position.z);
                pose.orientation = Eigen::Vector4f(pt.pose.orientation.x, pt.pose.orientation.y, pt.pose.orientation.z, pt.pose.orientation.w);
                msg.poses_.push_back(pose);
            }
            path_publisher_ptr->Publish(msg);
        }
        // 3. 发布停止信号
        if (stop_publisher_ptr && planner_ptr) {
            local_planner::Int8Msg stop = planner_ptr->getCurrentStop();
            stop_publisher_ptr->Publish({stop.data, 0});
        }
        // 4. 发布内部停止信号
        if (inner_stop_publisher_ptr && planner_ptr) {
            local_planner::Int8Msg inner_stop = planner_ptr->getCurrentInnerStop();
            inner_stop_publisher_ptr->Publish({inner_stop.data, 0});
        }
        // 5. 发布重规划信号
        if (replan_publisher_ptr && planner_ptr) {
            local_planner::Int8Msg replan = planner_ptr->getCurrentReplan();
            replan_publisher_ptr->Publish({replan.data, 0});
        }
        // 6. 发布节点就绪信号
        if (node_ready_publisher_ptr && planner_ptr) {
            local_planner::BoolMsg ready = planner_ptr->getCurrentNodeReady();
            node_ready_publisher_ptr->Publish(ready.data);
        }
        // 7. 发布自由路径点云
        if (free_paths_publisher_ptr && planner_ptr) {
            local_planner::PointCloud2Data cloud = planner_ptr->getCurrentFreePaths();
            communication::CloudData msg;
            PointCloud2DataToCloudData(cloud, msg);
            free_paths_publisher_ptr->Publish(msg);
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

int main(int argc, char** argv) {
    std::cout << "=== 局部规划器节点启动 ===" << std::endl;
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    try {
        std::string config_path = "../config/local_planner.yaml";
        if (argc > 1) config_path = argv[1];
        // 创建通信模块实例
        auto communication_ptr = std::make_shared<Communication>("local_planner");
        if (!communication_ptr->Initialize("config/communication_config.yaml")) {
            std::cerr << " 通信模块初始化失败" << std::endl;
            return -1;
        }
        // 创建订阅器
        odom_subscriber_ptr = communication_ptr->CreateOdometrySubscriber("/Odometry");
        laser_subscriber_ptr = communication_ptr->CreateCloudDataSubscriber("/registered_scan");
        terrain_subscriber_ptr = communication_ptr->CreateCloudDataSubscriber("/terrain_map");
        goal_subscriber_ptr = communication_ptr->CreatePoseDataSubscriber("/way_point");
        boundary_subscriber_ptr = communication_ptr->CreateStringDataSubscriber("/navigation_boundary");
        added_obstacles_subscriber_ptr = communication_ptr->CreateCloudDataSubscriber("/added_obstacles");
        location_failed_subscriber_ptr = communication_ptr->CreateBoolDataSubscriber("/location_failed");
        calibration_subscriber_ptr = communication_ptr->CreateInt8DataSubscriber("/calibration");
        // 创建发布器
        mode_publisher_ptr = communication_ptr->CreateBoolDataPublisher("/adjustmode");
        path_publisher_ptr = communication_ptr->CreatePathDataPublisher("/local_path", "map", 1);
        stop_publisher_ptr = communication_ptr->CreateInt8DataPublisher("/istop");
        inner_stop_publisher_ptr = communication_ptr->CreateInt8DataPublisher("/inner_stop");
        replan_publisher_ptr = communication_ptr->CreateInt8DataPublisher("/replan");
        node_ready_publisher_ptr = communication_ptr->CreateBoolDataPublisher("/node_ready");
        free_paths_publisher_ptr = communication_ptr->CreateCloudDataPublisher("/free_paths", "map", 1);
        // 创建局部规划器
        planner_ptr = std::make_unique<LocalPlanner>(config_path);
        // 启动数据接收线程
        std::thread data_receive_thread(DataReceiveThread);
        data_receive_thread.detach();
        // 启动数据发送线程
        std::thread data_send_thread(DataSendThread);
        data_send_thread.detach();
        // 初始化并启动规划器
        if (!planner_ptr->initialize()) {
            std::cerr << " 局部规划器初始化失败！" << std::endl;
            return -1;
        }
        planner_ptr->startPlanning();
        planner_ptr->printStatus();
        std::cout << "\n✅ 局部规划器节点启动成功！" << std::endl;
        // 阻塞主线程，运行通信模块
        communication_ptr->Run();
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return -1;
    }
    return 0;
}
