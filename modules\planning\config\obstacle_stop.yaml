# ObstacleStop Configuration File
# 障碍物停止器配置文件
# 完全保留原有obstacle_stop1.cpp的所有参数和功能

# 基本参数配置
obstacleHeightThre: 0.15    # 障碍物高度阈值 (原有默认值)
vehicleLength: 1.0          # 载体长度 (原有默认值)
vehicleWidth: 0.5           # 载体宽度 (原有默认值)
obsnumThre: 2               # 障碍物数量阈值 (原有默认值)
adjacentRange: 3.0          # 邻近范围 (原有默认值)
replan_time: 5.0            # 重规划时间 (原有默认值)
sensorOffsetX: 0.0          # 传感器X偏移
sensorOffsetY: 0.0          # 传感器Y偏移

# 预设配置方案
presets:
  # 默认配置
  default:
    obstacleHeightThre: 0.15
    vehicleLength: 1.0
    vehicleWidth: 0.5
    obsnumThre: 2
    adjacentRange: 3.0
    replan_time: 5.0
    sensorOffsetX: 0.0
    sensorOffsetY: 0.0
    description: "默认配置，适用于一般障碍物检测任务"
  
  # 敏感检测配置
  sensitive_detection:
    obstacleHeightThre: 0.1
    vehicleLength: 1.0
    vehicleWidth: 0.5
    obsnumThre: 1
    adjacentRange: 2.5
    replan_time: 3.0
    sensorOffsetX: 0.0
    sensorOffsetY: 0.0
    description: "敏感检测配置，适用于复杂环境"
  
  # 宽松检测配置
  relaxed_detection:
    obstacleHeightThre: 0.25
    vehicleLength: 1.0
    vehicleWidth: 0.5
    obsnumThre: 5
    adjacentRange: 4.0
    replan_time: 8.0
    sensorOffsetX: 0.0
    sensorOffsetY: 0.0
    description: "宽松检测配置，适用于开阔环境"
  
  # 小型载体配置
  small_vehicle:
    obstacleHeightThre: 0.12
    vehicleLength: 0.6
    vehicleWidth: 0.3
    obsnumThre: 1
    adjacentRange: 2.0
    replan_time: 3.0
    sensorOffsetX: 0.0
    sensorOffsetY: 0.0
    description: "小型载体配置，适用于小型机器人"
  
  # 大型载体配置
  large_vehicle:
    obstacleHeightThre: 0.2
    vehicleLength: 2.0
    vehicleWidth: 1.0
    obsnumThre: 3
    adjacentRange: 5.0
    replan_time: 10.0
    sensorOffsetX: 0.0
    sensorOffsetY: 0.0
    description: "大型载体配置，适用于大型机器人"
  
  # 高速导航配置
  high_speed:
    obstacleHeightThre: 0.18
    vehicleLength: 1.2
    vehicleWidth: 0.6
    obsnumThre: 2
    adjacentRange: 4.5
    replan_time: 2.0
    sensorOffsetX: 0.0
    sensorOffsetY: 0.0
    description: "高速导航配置，快速响应障碍物"

# 参数说明和取值范围
parameter_info:
  obstacleHeightThre:
    description: "障碍物高度阈值，用于筛选点云中的障碍物点"
    type: "double"
    range: "0.05 - 1.0"
    unit: "米"
    default: 0.15
    notes:
      - "值越小，检测越敏感，会将更多低矮物体识别为障碍物"
      - "值越大，检测越宽松，只有较高的物体才被识别为障碍物"
      - "建议根据实际环境中的障碍物高度调整"
    
  vehicleLength:
    description: "载体长度，用于计算载体与障碍物的安全距离"
    type: "double"
    range: "0.3 - 5.0"
    unit: "米"
    default: 1.0
    notes:
      - "必须准确设置为实际载体的长度"
      - "影响障碍物检测的安全范围计算"
      - "过小可能导致碰撞，过大可能过于保守"
    
  vehicleWidth:
    description: "载体宽度，用于计算载体与障碍物的安全距离"
    type: "double"
    range: "0.2 - 3.0"
    unit: "米"
    default: 0.5
    notes:
      - "必须准确设置为实际载体的宽度"
      - "影响路径规划时的安全间距"
      - "过小可能导致碰撞，过大可能无法通过狭窄通道"
    
  obsnumThre:
    description: "障碍物数量阈值，超过此数量时触发停止或重规划"
    type: "int"
    range: "1 - 20"
    unit: "个"
    default: 2
    notes:
      - "值越小，对障碍物越敏感，更容易触发停止"
      - "值越大，对障碍物越宽松，需要更多障碍物才触发停止"
      - "建议根据环境复杂度和安全要求调整"
    
  adjacentRange:
    description: "邻近范围，控制局部路径的长度"
    type: "double"
    range: "1.0 - 10.0"
    unit: "米"
    default: 3.0
    notes:
      - "值越大，规划的局部路径越长"
      - "值越小，规划的局部路径越短，响应更灵敏"
      - "应该根据载体速度和环境复杂度调整"
    
  replan_time:
    description: "重规划时间阈值，遇到障碍物后等待多长时间触发重规划"
    type: "double"
    range: "1.0 - 30.0"
    unit: "秒"
    default: 5.0
    notes:
      - "值越小，重规划越频繁，适用于动态环境"
      - "值越大，重规划越少，适用于静态环境"
      - "过小可能导致频繁重规划，过大可能响应不及时"
    
  sensorOffsetX:
    description: "传感器相对于载体中心的X轴偏移"
    type: "double"
    range: "-2.0 - 2.0"
    unit: "米"
    default: 0.0
    notes:
      - "正值表示传感器位于载体前方"
      - "负值表示传感器位于载体后方"
      - "必须准确设置以确保位置计算正确"
    
  sensorOffsetY:
    description: "传感器相对于载体中心的Y轴偏移"
    type: "double"
    range: "-2.0 - 2.0"
    unit: "米"
    default: 0.0
    notes:
      - "正值表示传感器位于载体左侧"
      - "负值表示传感器位于载体右侧"
      - "必须准确设置以确保位置计算正确"

# 应用场景配置
scenarios:
  # 室内导航场景
  indoor_navigation:
    obstacleHeightThre: 0.12
    vehicleLength: 0.8
    vehicleWidth: 0.4
    obsnumThre: 1
    adjacentRange: 2.5
    replan_time: 3.0
    sensorOffsetX: 0.0
    sensorOffsetY: 0.0
    description: "室内环境导航，敏感检测，适应狭窄空间"
    
  # 户外导航场景
  outdoor_navigation:
    obstacleHeightThre: 0.2
    vehicleLength: 1.5
    vehicleWidth: 0.8
    obsnumThre: 3
    adjacentRange: 4.0
    replan_time: 6.0
    sensorOffsetX: 0.0
    sensorOffsetY: 0.0
    description: "户外环境导航，适应复杂地形"
    
  # 仓库作业场景
  warehouse_operation:
    obstacleHeightThre: 0.15
    vehicleLength: 1.0
    vehicleWidth: 0.6
    obsnumThre: 2
    adjacentRange: 3.0
    replan_time: 4.0
    sensorOffsetX: 0.1
    sensorOffsetY: 0.0
    description: "仓库作业，平衡安全性和效率"
    
  # 巡逻场景
  patrol_mission:
    obstacleHeightThre: 0.18
    vehicleLength: 1.2
    vehicleWidth: 0.5
    obsnumThre: 2
    adjacentRange: 3.5
    replan_time: 5.0
    sensorOffsetX: 0.0
    sensorOffsetY: 0.0
    description: "巡逻任务，稳定可靠的障碍物检测"
    
  # 搬运场景
  transport_mission:
    obstacleHeightThre: 0.1
    vehicleLength: 1.8
    vehicleWidth: 0.9
    obsnumThre: 1
    adjacentRange: 2.8
    replan_time: 8.0
    sensorOffsetX: 0.2
    sensorOffsetY: 0.0
    description: "搬运任务，高安全性要求"

# 载体类型配置
vehicle_types:
  # 轮式机器人
  wheeled_robot:
    obstacleHeightThre: 0.15
    vehicleLength: 1.0
    vehicleWidth: 0.5
    obsnumThre: 2
    adjacentRange: 3.0
    replan_time: 5.0
    description: "标准轮式机器人配置"
    
  # 履带机器人
  tracked_robot:
    obstacleHeightThre: 0.12
    vehicleLength: 1.2
    vehicleWidth: 0.6
    obsnumThre: 3
    adjacentRange: 3.5
    replan_time: 6.0
    description: "履带机器人配置，适应复杂地形"
    
  # 四足机器人
  quadruped_robot:
    obstacleHeightThre: 0.08
    vehicleLength: 0.8
    vehicleWidth: 0.4
    obsnumThre: 1
    adjacentRange: 2.0
    replan_time: 3.0
    description: "四足机器人配置，高灵活性"
    
  # 无人车
  autonomous_vehicle:
    obstacleHeightThre: 0.25
    vehicleLength: 2.5
    vehicleWidth: 1.2
    obsnumThre: 5
    adjacentRange: 6.0
    replan_time: 10.0
    description: "无人车配置，适应道路环境"

# 环境复杂度配置
environment_complexity:
  # 简单环境
  simple_environment:
    obstacleHeightThre: 0.2
    obsnumThre: 3
    adjacentRange: 4.0
    replan_time: 8.0
    description: "简单环境，宽松检测参数"
    
  # 中等复杂环境
  medium_complexity:
    obstacleHeightThre: 0.15
    obsnumThre: 2
    adjacentRange: 3.0
    replan_time: 5.0
    description: "中等复杂环境，默认参数"
    
  # 复杂环境
  complex_environment:
    obstacleHeightThre: 0.12
    obsnumThre: 1
    adjacentRange: 2.5
    replan_time: 3.0
    description: "复杂环境，敏感检测参数"
    
  # 极复杂环境
  very_complex:
    obstacleHeightThre: 0.08
    obsnumThre: 1
    adjacentRange: 2.0
    replan_time: 2.0
    description: "极复杂环境，最敏感参数"

# 调试和测试配置
debug:
  enable_logging: true
  log_level: "INFO"  # DEBUG, INFO, WARN, ERROR
  log_file: "obstacle_stop.log"
  print_obstacle_info: true
  print_path_info: true
  
test:
  enable_test_mode: false
  test_obstacles:
    - {x: 1.0, y: 0.5, z: 0.2, intensity: 0.3}
    - {x: 2.0, y: 1.0, z: 0.25, intensity: 0.4}
    - {x: 1.5, y: -0.5, z: 0.18, intensity: 0.35}

# 性能优化配置
performance:
  point_cloud_downsample: false  # 是否对点云进行下采样
  max_cloud_points: 10000        # 最大点云点数
  processing_frequency: 100      # 处理频率 (Hz)
  memory_optimization: true      # 是否启用内存优化

# 安全配置
safety:
  max_obstacle_height: 2.0       # 最大障碍物高度
  min_vehicle_size: 0.1          # 最小载体尺寸
  max_vehicle_size: 10.0         # 最大载体尺寸
  emergency_stop_distance: 0.5   # 紧急停止距离
  safety_margin: 0.1             # 安全边距

# 版本信息
version:
  config_version: "1.0"
  compatible_versions: ["1.0", "1.1"]
  last_modified: "2024-01-01"
  author: "ObstacleStop NoROS Team"
  description: "去ROS化障碍物停止器配置文件，完全保留原有功能"

# 注意事项
notes:
  - "所有参数都完全保留了原有obstacle_stop1.cpp的功能"
  - "obstacleHeightThre控制障碍物检测的敏感度"
  - "vehicleLength和vehicleWidth必须准确设置为实际载体尺寸"
  - "obsnumThre控制触发停止的障碍物数量阈值"
  - "adjacentRange影响局部路径规划的范围"
  - "replan_time控制重规划的触发时机"
  - "传感器偏移参数必须根据实际安装位置设置"
  - "参数修改后需要重新加载配置或重启节点"
  - "建议根据实际应用场景选择合适的预设配置"
