#include "publisher/int8_data_publisher_ros1.h"

#if COMMUNICATION_TYPE == ROS1
namespace communication::ros1 {
Int8DataPublisherRos1::Int8DataPublisherRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size)
    : Int8DataPublisherBase(topic, max_buffer_size), nh_(nh) {
    publisher_ = nh_.advertise<std_msgs::Int8>(topic, max_buffer_size);
}

} // namespace communication::ros1
#endif // COMMUNICATION_TYPE == ROS1 