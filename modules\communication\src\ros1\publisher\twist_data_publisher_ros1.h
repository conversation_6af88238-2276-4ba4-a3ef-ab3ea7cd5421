/**
 * @file twist_data_publisher_ros1.h
 * @brief This file contains the implementation of a ROS1 publisher for twist data.
 * It inherits from the TwistDataPublisherBase class and provides methods to publish twist data in ROS1 format.
 * The publisher uses the geometry_msgs::Twist message type to represent twist data.
 * The class provides methods to convert twist data to the appropriate ROS message format and publish it on a specified topic.
 * It also includes methods to get the number of subscribers to the topic.
 */
#pragma once

#include <ros/ros.h>
#include <geometry_msgs/Twist.h>
#include "publisher_base.h"
#include "data_types/twist_data.h"

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1 {
class TwistDataPublisherRos1 : public TwistDataPublisherBase {

public:
    TwistDataPublisherRos1(ros::NodeHandle &nh, const std::string &topic,
                          const std::string &frame_id = "map", size_t max_buffer_size = 10);      

    virtual ~TwistDataPublisherRos1() = default;
protected:
    virtual void PublishMsg() override {
        publisher_.publish(msg_);
    }

    virtual void ToMsg() override {
        msg_.linear.x = data_.linear_vel.x();
        msg_.linear.y = data_.linear_vel.y();
        msg_.linear.z = data_.linear_vel.z();
        msg_.angular.x = data_.angular_vel.x();
        msg_.angular.y = data_.angular_vel.y();
        msg_.angular.z = data_.angular_vel.z();
    }

   int GetSubscriberCount() const {
        return publisher_.getNumSubscribers();
    }

    private:
        ros::NodeHandle &nh_;
        ros::Publisher publisher_;
        geometry_msgs::Twist msg_; // ROS message type for twist data
        std::string frame_id_; // Frame ID for the twist data

};

}//namespace communication::ros1

#endif
