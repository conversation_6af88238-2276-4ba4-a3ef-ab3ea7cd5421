#ifndef __IMAGE_SEGMENT__H__RKNN__H
#define __IMAGE_SEGMENT__H__RKNN__H

#include "image_segment/image_segment_impl.h"
#include "yolov8_rknn.h"

namespace perception{

class ImageSegmentRknn : public ImageSegmentImpl{
    
public:
    ImageSegmentRknn();
    virtual ~ImageSegmentRknn();
    virtual bool Init(const std::string& model_path);
    virtual bool Inference(cv::Mat src,float box_thresh,float nms_thresh,SegmentDetectResult& out);
    

protected:
    std::unique_ptr<Yolov8Rknn>  yolo_;
};

}

#endif