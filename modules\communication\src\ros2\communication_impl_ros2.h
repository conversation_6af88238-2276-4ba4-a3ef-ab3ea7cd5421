#pragma once

#if COMMUNICATION_TYPE == ROS2
#include <memory>
#include <rclcpp/rclcpp.hpp>
// #include <image_transport/image_transport.hpp>
#include "communication_impl.h"
#include "subscriber_base.h"
#include "publisher_base.h"

namespace communication::ros2
{

    class CommunicationRos2Impl : public communication::CommunicationImpl
    {
    public:
        CommunicationRos2Impl(const std::string &module_name);
        virtual ~CommunicationRos2Impl();

        // Subscribers
        virtual std::shared_ptr<ImageDataSubscriberBase> CreateImageDataSubscriber(
            const std::string &topic, const std::string &pixel_type) override;
        virtual std::shared_ptr<CameraIntSubscriberBase> CreateCameraIntSubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<CameraExtSubscriberBase> CreateCameraExtSubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<ImuDataSubscriberBase> CreateImuDataSubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<GnssDataSubscriberBase> CreateGnssDataSubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<LidarDataSubscriberBase> CreateLidarDataSubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<OdometrySubscriberBase> CreateOdometrySubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<IntDataSubscriberBase> CreateIntDataSubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<DoubleDataSubscriberBase> CreateDoubleDataSubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<StringDataSubscriberBase> CreateStringDataSubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<BoolDataSubscriberBase> CreateBoolDataSubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<CloudDataSubscriberBase> CreateCloudDataSubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<PathDataSubscriberBase> CreatePathDataSubscriber(
            const std::string &topic) override;
        virtual std::shared_ptr<PoseDataSubscriberBase> CreatePoseDataSubscriber(
            const std::string &topic) override;

        // Publishers
        virtual std::shared_ptr<ImageDataPublisherBase> CreateImageDataPublisher(
            const std::string &topic, const std::string &pixel_type,
            size_t max_buffer_size = 10) override;
        virtual std::shared_ptr<OdometryPublisherBase> CreateOdometryPublisher(
            const std::string &topic,
            const std::string &frame_id = "map",
            const std::string &child_frame_id = "body",
            size_t max_buffer_size = 10) override;
        virtual std::shared_ptr<IntDataPublisherBase> CreateIntDataPublisher(
            const std::string &topic, size_t max_buffer_size = 10) override;
        virtual std::shared_ptr<DoubleDataPublisherBase> CreateDoubleDataPublisher(
            const std::string &topic, size_t max_buffer_size = 10) override;
        virtual std::shared_ptr<StringDataPublisherBase> CreateStringDataPublisher(
            const std::string &topic, size_t max_buffer_size = 10) override;
        virtual std::shared_ptr<BoolDataPublisherBase> CreateBoolDataPublisher(
            const std::string &topic, size_t max_buffer_size = 10) override;
        virtual std::shared_ptr<CloudDataPublisherBase> CreateCloudDataPublisher(
            const std::string &topic,
            const std::string &frame_id = "map",
            size_t max_buffer_size = 10) override;
        virtual std::shared_ptr<PathDataPublisherBase> CreatePathDataPublisher(
            const std::string &topic,
            const std::string &frame_id = "map",
            size_t max_buffer_size = 10) override;
        virtual std::shared_ptr<PoseDataPublisherBase> CreatePoseDataPublisher(
            const std::string &topic, size_t max_buffer_size = 10) override;
        virtual std::shared_ptr<TFDataPublisherBase> CreateTFDataPublisher(
            const std::string &frame_id = "map",
            const std::string &child_frame_id = "body") override;
        virtual std::shared_ptr<TFDataSubscriberBase> CreateTFDataSubscriber(
            const std::string &target_frame_id = "map",
            const std::string &source_frame_id = "body") override;

    protected:
        virtual bool Initialize(const CommunicationType type) override;
        virtual void Run() override;
        virtual void RunOnce() override;
        virtual bool IsTerminated() const override;

    private:
        rclcpp::Node::SharedPtr node_;
        rclcpp::executors::SingleThreadedExecutor executor_;
        // std::shared_ptr<image_transport::ImageTransport> it_;
    };

} // namespace communication::ros2

#endif
