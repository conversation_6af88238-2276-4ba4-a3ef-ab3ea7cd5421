module: localization_node
version: v1.0
common:
#RS LiDar
  lid_topic: "/rslidar_points"
  imu_topic: "/imu/data"
#    imu_topic: "yesense_imu/imu/data"
  gnss_topic: "/chattergps"
  leg_topic: "/leg_odom"
  # lid_topic: "/rslidar_points1"
  # imu_topic: "/imu/data1"
  manual_pos_topic: "/initialpose"
  time_sync_en: false         # ONLY turn on when external time synchronization is really not possible
    
preprocess:
    lidar_type: 2                # 1 for Livox serials LiDAR, 2 for Velodyne LiDAR, 3 for ouster LiDAR, 4 RS
    scan_line: 16
    scan_rate: 10                # only need to be set for velodyne, unit: Hz,
    timestamp_unit: 0                 # 0-second, 1-milisecond, 2-microsecond, 3-nanosecond.
    blind: 0.8
    
mapping:
    acc_cov: 0.1
    gyr_cov: 0.1
    b_acc_cov: 0.0001
    b_gyr_cov: 0.0001
    fov_degree:    180
    det_range:     40
    extrinsic_est_en:  false      # true: enable the online estimation of IMU-LiDAR extrinsic,
    extrinsic_T: [0.42, 0, 0]   #lidar在imu坐标系中的坐标
    extrinsic_R: [ 1, 0, 0,
                   0, 1, 0,
                   0, 0, 1]
    #rtk2Lidar_T: [0.42, 0.0, 0.0] #rtk天线在imu坐标系中的坐标
    rtk2Lidar_T: [-0.05, -0.05, 0.15] #rtk天线在imu坐标系中的坐标

publish:
    path_en:  true
    scan_publish_en:  true       # false: close all the point cloud output
    dense_publish_en: true       # false: low down the points number in a global-frame point clouds scan.
    scan_bodyframe_pub_en: true  # true: output the point cloud scans in IMU-body-frame

surroundingkeyframeAddingDistThreshold: 1.0
surroundingkeyframeAddingAngleThreshold: 0.2

globalMapVisualizationSearchRadius: 50.0
globalMapVisualizationPoseDensity: 2.0
globalMapVisualizationLeafSize: 0.4

# GPS Settings
usertk: true
gpsCovThreshold: 0.2
poseCovThreshold: 0.01
useGpsElevation: true
status_threshold: 1

#bfsreloc
#重定位查找半径，一般10够用，室外视情况而定可提高至15，室内地图较小杂点较多可改为5
Search_Radius: 2.5 #室内查找半径
Search_Radius_rtk: 5.0 #RTK室外查找半径

useleg: false
Reposition_type: 2 #1 for rtk ,2 for manual

# imu axis define: FLU(前左上, 默认值), LBU(左后上), 
imu_axis: FLU

numberOfCores: 2
recontructKdTree: true
parent_dir: "/home/<USER>/cx_robot/modules/localization/bin/map/"
map_dir: "PCD/"
map_name: "cloud_map.pcd"
pose_txt_name: "pose.txt"
loadmappath: "/home/<USER>/cx_robot/modules/localization/bin/map/PCD/cloud_map.pcd"
loadposepath: "/home/<USER>/cx_robot/modules/localization/bin/map/PCD/pose.txt"
params_filename: "/home/<USER>/cx_robot/modules/localization/bin/map/PCD/param.json"

#零速检测
zero_detect:
    enable: true
    acc_std_threshold: 0.0060
    gyro_std_threshold: 0.00080

#match rate
match_rate: 60.0
local_map_radius: 100.0 #局部地图半径

match_rate_threshold: 0.75 #匹配率阈值，低于此值则认为匹配失败

#算法版本号：
localization_alg_version: v1.0.0_20250311_01
