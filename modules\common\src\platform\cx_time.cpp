#include "cx_time.h"

#include <time.h>

#ifdef LINUX

#include <sys/time.h>

#endif
namespace common_lib {
CXTime::CXTime() {}

cx_uint64 GetTimestamp() {
#ifdef WIN32
  ticx_t t;
  time(&t);

  return t;
#else
  // struct timeval tv;
  // gettimeofday(&tv,NULL);    //�ú�����sys/time.hͷ�ļ���
  // return tv.tv_sec * 1000 + tv.tv_usec / 1000;

  struct timespec ts;
  clock_gettime(CLOCK_REALTIME, &ts);
  return static_cast<cx_uint64>(ts.tv_sec * 1000 + ts.tv_nsec * 0.000001);
#endif
}

cx_double GetCurrentSeconds()
{
    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);
    return static_cast<cx_double>(ts.tv_sec) + static_cast<cx_double>(ts.tv_nsec) * 1e-9;
}

#ifdef LINUX

cx_long GetTickCount() {
  static cx_long s_InitTime = 0;

  timeval tv;
  gettimeofday(&tv, NULL);
  if (s_InitTime == 0) {
    s_InitTime = tv.tv_sec;
  }

  return (tv.tv_sec - s_InitTime) * 1000 + tv.tv_usec / 1000;
}

#endif

cx_string GetNameStringViaDatatime() {
  cx_string strDatetime;
  cx_char szDatetime[100] = {0};

#ifdef WIN32

  SYSTEMTIME st;
  GetLocalTime(&st);
  sprintf(szDatetime, "[%04d%02d%02d%02d%02d%02d]", st.wYear, st.wMonth, st.wDay, st.wHour,
          st.wMinute, st.wSecond);

#else

  time_t tmNow;
  tm *pTime;
  time(&tmNow);
  //    pTime = gmtime(&tmNow);
  pTime = localtime(&tmNow);
  sprintf(szDatetime, "%04d%02d%02d%02d%02d%02d", pTime->tm_year + 1900, pTime->tm_mon + 1,
          pTime->tm_mday, pTime->tm_hour, pTime->tm_min, pTime->tm_sec);

#endif

  strDatetime = szDatetime;

  return strDatetime;
}

// cx_double g_dGpsSec = 0.0;
// void SetCurGPSTime(cx_double dGpsSec)
// {
//     g_dGpsSec = dGpsSec;
// }

// cx_double GetCurGPSTime()
// {
//     if (g_dGpsSec < 0.1)
//     {
//         g_dGpsSec = (cx_double)GetMSecsSinceEpoch() / 1000.0;
//     }
//     return g_dGpsSec;
// }

// cx_uint64 GetMSecsSinceEpoch()
// {
//     struct timeval tv;
//     gettimeofday(&tv,NULL);    //�ú�����sys/time.hͷ�ļ���
//     return tv.tv_sec * 1000 + tv.tv_usec / 1000;
// }

// cx_double GetMSecs()
// {
//     struct timeval tv;
//     gettimeofday(&tv,NULL);
//     return tv.tv_usec * 0.000001;
// }
}  // namespace common_lib