#include "config.h"
#include <sstream>

namespace common_lib {
void Config::Init(const std::string& file_path) {
  config_file_path_ = file_path;
}
std::vector<std::string> Config::SplitPath(const std::string& path) const {
  std::vector<std::string> tokens;
  std::string token;
  std::istringstream token_stream(path);

  while (std::getline(token_stream, token, '/')) {
    if (!token.empty()) {
      // Support 'parent.child' notation
      if (token.find('.') != std::string::npos) {
        std::istringstream dot_stream(token);
        std::string dot_token;
        while (std::getline(dot_stream, dot_token, '.')) {
          if (!dot_token.empty()) tokens.push_back(dot_token);
        }
      } else {
        tokens.push_back(token);
      }
    }
  }

  return tokens;
}

}  // namespace common_lib