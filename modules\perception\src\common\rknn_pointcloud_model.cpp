// RKNNPointCloudModel.cpp
#include "rknn_pointcloud_model.h"
#include <fstream>
#include <iostream>
#include <cstring>

namespace perception{


void RKNNPointCloudModel::dump_tensor_attr(rknn_tensor_attr* attr) {
    printf("  index=%d, name=%s, n_dims=%d, dims=[%d, %d], size=%d, w_stride=%d, size_with_stride=%d\n",
           attr->index, attr->name, attr->n_dims,
           attr->dims[0], attr->dims[1],
           attr->size, attr->w_stride, attr->size_with_stride);
}

unsigned char* RKNNPointCloudModel::load_model(const std::string& path, int* model_len) {
    std::ifstream file(path, std::ios::binary);
    if (!file) {
        std::cerr << "Failed to open model: " << path << std::endl;
        return nullptr;
    }

    file.seekg(0, std::ios::end);
    *model_len = file.tellg();
    file.seekg(0, std::ios::beg);

    unsigned char* buffer = new unsigned char[*model_len];
    file.read((char*)buffer, *model_len);

    return buffer;
}

RKNNPointCloudModel::RKNNPointCloudModel()
    : init_(false) {
    
    app_ctx_=new rknn_app_context_t();
    memset(app_ctx_,0,sizeof(rknn_app_context_t));
}

RKNNPointCloudModel::~RKNNPointCloudModel() {
    if (app_ctx_->ctx != 0) {
        rknn_destroy(app_ctx_->ctx);
    }
    delete[] app_ctx_->input_attrs;
    delete[] app_ctx_->output_attrs;
    delete app_ctx_;
}

bool RKNNPointCloudModel::init(const std::string& model_path) {
    if(init_){
        return true;
    }

    int ret = -1;
    int model_len = 0;
    unsigned char* model = load_model(model_path, &model_len);
    if (model == NULL) {
        printf("load_model fail!\n");
        return false;
    }

    // 初始化 RKNN 上下文
    ret = rknn_init(&app_ctx_->ctx, model, model_len, 0, NULL);
    delete[] model;
    if (ret < 0) {
        printf("rknn_init fail! ret=%d\n", ret);
        return false;
    }

    // 查询输入输出数量
    ret = rknn_query(app_ctx_->ctx, RKNN_QUERY_IN_OUT_NUM, &app_ctx_->io_num, sizeof(app_ctx_->io_num));
    if (ret != RKNN_SUCC) {
        printf("rknn_query INPUT_OUTPUT_NUM fail! ret=%d\n", ret);
        return false;
    }
    printf("model input num: %d, output num: %d\n", app_ctx_->io_num.n_input, app_ctx_->io_num.n_output);

    // 获取输入 tensor 属性
    app_ctx_->input_attrs = new rknn_tensor_attr[app_ctx_->io_num.n_input];
    for (int i = 0; i < app_ctx_->io_num.n_input; ++i) {
        app_ctx_->input_attrs[i].index = i;
        ret = rknn_query(app_ctx_->ctx, RKNN_QUERY_INPUT_ATTR, &app_ctx_->input_attrs[i], sizeof(rknn_tensor_attr));
        if (ret != RKNN_SUCC) {
            printf("rknn_query INPUT_ATTR[%d] fail! ret=%d\n", i, ret);
            return false;
        }
        dump_tensor_attr(&(app_ctx_->input_attrs[i]));
    }

    // 获取输出 tensor 属性
    app_ctx_->output_attrs = new rknn_tensor_attr[app_ctx_->io_num.n_output];
    for (int i = 0; i < app_ctx_->io_num.n_output; ++i) {
        app_ctx_->output_attrs[i].index = i;
        ret = rknn_query(app_ctx_->ctx, RKNN_QUERY_OUTPUT_ATTR, &app_ctx_->output_attrs[i], sizeof(rknn_tensor_attr));
        if (ret != RKNN_SUCC) {
            printf("rknn_query OUTPUT_ATTR[%d] fail! ret=%d\n", i, ret);
            return false;
        }
        //dump_tensor_attr(&(app_ctx_->output_attrs[i]));
    }

 
  
    app_ctx_->model_height = app_ctx_->input_attrs[0].dims[0];
    app_ctx_->model_width = app_ctx_->input_attrs[0].dims[1];
    
    printf("model input height=%d, width=%d\n",
           app_ctx_->model_height, app_ctx_->model_width);

    // 判断是否量化模型
    if (app_ctx_->output_attrs[0].qnt_type == RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC &&
        app_ctx_->output_attrs[0].type != RKNN_TENSOR_FLOAT16) {
        app_ctx_->is_quant = true;
    } else {
        app_ctx_->is_quant = false;
    }
    init_=true;
    return true;
}

bool RKNNPointCloudModel::infer(const cv::Mat& depth_map, const float* camera_params, std::vector<float>& cloud_data) {
    if(! init_){
        return false;
    }
  
    // Step 2: 设置输入数据
    rknn_input inputs[2];
    memset(inputs,0,2*sizeof(rknn_input));
    // 输入 0: depth_map ()
    inputs[0].index = 0;
    inputs[0].type = RKNN_TENSOR_FLOAT32; //app_ctx_->input_attrs[0].type;  // 
    inputs[0].size = depth_map.rows * depth_map.cols * sizeof(float);
    inputs[0].fmt = app_ctx_->input_attrs[0].fmt;           //RKNN_TENSOR_NHWC
    inputs[0].buf = (void*)depth_map.data;

    // 输入 1: camera_params (FLOAT32)
    inputs[1].index = 1;
    inputs[1].type =RKNN_TENSOR_FLOAT32 ;// app_ctx_->input_attrs[1].type;  // RKNN_TENSOR_FLOAT32
    inputs[1].size = 4 * sizeof(float);
    inputs[1].fmt = app_ctx_->input_attrs[1].fmt;       //RKNN_TENSOR_NHWC
    inputs[1].buf = (void*)camera_params;

   
    int ret = rknn_inputs_set(app_ctx_->ctx, app_ctx_->io_num.n_input, inputs);  //此处Segmentation fault
    if (ret < 0) {
        printf("rknn_inputs_set fail! ret=%d\n", ret);
        return false;
    }

    // Step 3: 推理
    ret = rknn_run(app_ctx_->ctx, nullptr);
    if (ret < 0) {
        printf("rknn_run fail! ret=%d\n", ret);
        return false;
    }
   
    // Step 4: 获取输出
    rknn_output outputs[1];
    memset(outputs,0,sizeof(rknn_output));
    outputs[0].want_float = 1;
    outputs[0].index=0;

    ret = rknn_outputs_get(app_ctx_->ctx, 1, outputs, NULL);
    if (ret < 0) {
        printf("rknn_outputs_get fail! ret=%d\n", ret);
        return false;
    }

    float* data = (float*)outputs[0].buf;
    int count = outputs[0].size / sizeof(float);
    cloud_data.assign(data, data + count);

    rknn_outputs_release(app_ctx_->ctx, 1, outputs);
    return true;
}

}