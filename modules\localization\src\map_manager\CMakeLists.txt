add_library(${PROJECT_NAME}_map_manager SHARED
    kdtreeManage.cpp
    map_management.cpp
)

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/../common
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/eskf
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/ikd-Tree

    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/thread
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/model
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/algorithm
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/file
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/platform
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/config
)

target_link_libraries(${PROJECT_NAME}_map_manager
    ${PROJECT_NAME}_common
    ${PCL_LIBRARIES}
    ${EIGEN3_LIBRARIES}
    common_lib
    communication_core
    #fmt::fmt
)