#pragma once
#if COMMUNICATION_TYPE == ROS1
#include <ros/ros.h>
#include <std_msgs/Int8.h>
#include "subscriber_base.h"

namespace communication::ros1 {
class Int8DataSubscriberRos1 : public Int8DataSubscriberBase {
public:
    Int8DataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size = 10);

    ~Int8DataSubscriberRos1() = default;

    void Int8DataCallbackRos1(const std_msgs::Int8::ConstPtr &int8_msg);

private:
    ros::NodeHandle &nh_;
    ros::Subscriber subscriber_;
};

} // namespace communication::ros1
#endif // COMMUNICATION_TYPE == ROS1 