#ifndef _CX_SINGLETON_H_
#define _CX_SINGLETON_H_

#include "cx_pubhead.h"
#include "thread/cx_mutex.h"
namespace common_lib {
template <class SingletonClass>
class CXSingleton {
 public:
  CXSingleton() {
    singleton_instance_ = NULL;
    is_destroyed_ = false;
  }

  virtual ~CXSingleton() {
    singleton_instance_ = NULL;
    is_destroyed_ = true;
  }

  static SingletonClass *GetSingletonInstance() {
    ASSERT(!is_destroyed_);

    if (!singleton_instance_) {
      cx_mutex_.Lock();
      if (!singleton_instance_) {
        singleton_instance_ = new SingletonClass;
      }
      cx_mutex_.Unlock();
    }

    return singleton_instance_;
  }

 private:
  static SingletonClass *singleton_instance_;
  static CXMutex cx_mutex_;
  static cx_bool is_destroyed_;
};

template <class SingletonClass>
SingletonClass *CXSingleton<SingletonClass>::singleton_instance_ = NULL;
template <class SingletonClass>
CXMutex CXSingleton<SingletonClass>::cx_mutex_;
template <class SingletonClass>
bool C<PERSON><PERSON><PERSON>leton<SingletonClass>::is_destroyed_ = false;
}  // namespace common_lib
#endif  // _CX_SINGLETON_H_
