#ifndef CX_LOGGER_H
#define CX_LOGGER_H

#ifdef USE_LOG4CPLUS
#include <log4cplus/configurator.h>
#include <log4cplus/consoleappender.h>
#include <log4cplus/fileappender.h>
#include <log4cplus/helpers/loglog.h>
#include <log4cplus/helpers/stringhelper.h>
#include <log4cplus/initializer.h>
#include <log4cplus/layout.h>
#include <log4cplus/logger.h>
#include <log4cplus/loggingmacros.h>
#endif
#include <string>
namespace common_lib {
class cx_logger {
 public:
  // 单例模式获取实例
  static cx_logger &getInstance();

  void Init(const std::string &config_file_path = "", const std::string &out_file_path = "",
            const std::string &modules = "");
#ifdef USE_LOG4CPLUS
  // 获取指定名称的日志记录器
  log4cplus::Logger getSubLogger(const std::string &logger_name);
#endif
  // 析构函数
  ~cx_logger();

 private:
  // 构造函数，根据配置文件初始化 log4cplus
  cx_logger();
  // 禁用拷贝构造函数
  cx_logger(const cx_logger &) = delete;
  // 禁用赋值运算符
  cx_logger &operator=(const cx_logger &) = delete;
};
}  // namespace common_lib

#endif