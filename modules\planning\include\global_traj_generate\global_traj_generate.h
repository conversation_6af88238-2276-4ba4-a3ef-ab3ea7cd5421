#ifndef GLOBAL_TRAJ_GENERATE_H
#define GLOBAL_TRAJ_GENERATE_H

#include <iostream>
#include <stdio.h>
#include <math.h>
#include <unistd.h>
#include <time.h>
#include <fstream>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/range_image/range_image.h>
#include <pcl/filters/filter.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/common/common.h>
#include <pcl/registration/icp.h>

#include <Eigen/Dense>
#include <yaml-cpp/yaml.h>
// 包含公共结构体定义
#include "common_types.h"

using namespace std;

// 去ROS化的消息结构体
namespace global_trajec_generate {

typedef pcl::PointXYZ PointType;
#define PI 3.14159265

// 使用公共命名空间中的类型
using planning_common::TimeStamp;
using planning_common::Header;
using planning_common::Point;
using planning_common::Quaternion;
using planning_common::Pose;
using planning_common::PoseStamped;
using planning_common::OdometryData;
using planning_common::PathData;

/**
 * @brief 导航目标消息
 */
struct NavigationTarget {
    int32_t nav_mode;
    int32_t point_id;
    double pose_x, pose_y, pose_z;
    double yaw;
    int32_t point_info;
    int32_t gait;
    int32_t speed;
    int32_t manner;
    int32_t obsmode;
    int32_t navmode;
    
    NavigationTarget() : nav_mode(0), point_id(0), pose_x(0.0), pose_y(0.0), pose_z(0.0),
                        yaw(0.0), point_info(0), gait(0), speed(1), manner(0), obsmode(1), navmode(0) {}
};

/**
 * @brief 导航结果消息
 */
struct NavigationResult {
    int32_t point_id;
    double target_pose_x, target_pose_y, target_pose_z;
    double target_yaw;
    double current_pose_x, current_pose_y, current_pose_z;
    double current_yaw;
    int32_t nav_state;
    
    NavigationResult() : point_id(0), target_pose_x(0.0), target_pose_y(0.0), target_pose_z(0.0),
                        target_yaw(0.0), current_pose_x(0.0), current_pose_y(0.0), current_pose_z(0.0),
                        current_yaw(0.0), nav_state(0) {}
};

/**
 * @brief 轨迹点类 (完全保留原有实现)
 */
class trajPoint {
public:
    float x;
    float y;
    float z;
    float yaw;

    trajPoint(double _x, double _y, double _z, double _yaw) : x(_x), y(_y), z(_z), yaw(_yaw) {}
};

} // namespace global_trajec_generate

/**
 * @brief 去ROS化的全局轨迹生成器类
 */
class GlobalTrajecGenerate {
public:
    GlobalTrajecGenerate(const std::string& name);
    ~GlobalTrajecGenerate();
    
    // 初始化和控制
    bool init();
    bool initFromConfig(const std::string& config_file = "");
    void start();
    void stop();
    
    // 参数设置 (完全保留原有参数)
    void setParameters(int indexIncrement, int indexNum);
    void setIndexIncrement(int indexIncrement);
    void setIndexNum(int indexNum);
    
    // 输入函数 (替换ROS订阅)
    void inputOdometry(const planning_common::OdometryData& odom);
    void inputGoalPose(const global_trajec_generate::PoseStamped& goal);
    void inputWebGoalPose(const global_trajec_generate::PoseStamped& goal);
    void inputGlobalPath(const planning_common::PathData& path);
    
    // 回调函数设置 (替换ROS发布)
    void setLocalGoalCallback(std::function<void(const std::shared_ptr<global_trajec_generate::PoseStamped>&)> callback);
    void setNavigationResultCallback(std::function<void(const std::shared_ptr<global_trajec_generate::NavigationResult>&)> callback);
    
    // 状态查询
    bool isInitialized() const;
    bool isRunning() const;
    bool isStartFlag() const;
    bool isLocalGoalInitialized() const;
    
    // 获取当前参数和状态
    int getIndexIncrement() const;
    int getIndexNum() const;
    int getTrajSize() const;
    int getNearestTrajPointIndex() const;
    int getLocalGoalIndex() const;
    global_trajec_generate::Point getCurrentGoalPoint() const;

    /**
     * @brief 获取当前的局部目标点
     * @return 返回成员变量localGoal_的智能指针
     */
    std::shared_ptr<global_trajec_generate::PoseStamped> getCurrentLocalGoal() const;
   
    
    // 配置管理
    bool loadConfiguration(const std::string& config_file);
    bool saveConfiguration(const std::string& config_file) const;
    void printStatus() const;

    

private:
    // 成员变量
    std::string name_;
    bool initialized_;
    std::atomic<bool> running_;
    std::thread worker_thread_;
    mutable std::mutex data_mutex_;
    
    // 原有的所有变量 (完全保留)
    global_trajec_generate::PoseStamped localGoal_;                    // 全局系下的局部目标点
    global_trajec_generate::Point posenow_;                           // 终点位置信息
    pcl::PointCloud<global_trajec_generate::PointType>::Ptr cloudKeyPoses3DToMap_;
    pcl::KdTreeFLANN<global_trajec_generate::PointType>::Ptr kdtreeCloudKeyPoses3DToMap_;
    std::vector<int> pointSearchInd_;
    std::vector<float> pointSearchSqDis_;
    planning_common::OdometryData odom_;                              // 载体位置信息
    
    std::vector<global_trajec_generate::trajPoint> traj_;
    
    int trajSize_;                                                           // 全局轨迹航迹点数目
    bool localGoalInitialFlag_;                                              // 第一个局部目标点是否初始化
    int indexIncrement_;                                                     // 向前预移动的航迹点索引增量
    int indexNum_;                                                           // 向前预移动的点增量
    int nearestTrajPointIndexToCurrentRobotPos_;                            // 距离当前机器人位置最近的航迹点索引
    int nextTrajPointIndexToCurrentRobotPos_;                               // 距离当前机器人位置最近的航迹点的下一个航迹点索引
    int localGoalIndex_;                                                     // 局部目标点在全局轨迹向量中的索引
    bool start_flag_;                                                        // 是否接收到新的轨迹
    double poseyaw_;                                                         // 目标点航向角
    
    // 回调函数
    std::function<void(const std::shared_ptr<global_trajec_generate::PoseStamped>&)> local_goal_callback_;
    std::function<void(const std::shared_ptr<global_trajec_generate::NavigationResult>&)> nav_result_callback_;
    
    // 原有的处理函数 (完全保留实现)
    void generateLocalGoal(const std::shared_ptr<const planning_common::OdometryData>& msg);
    void generateGlobalGoal(const std::shared_ptr<const planning_common::PathData>& msg);
    void goalPoseCallback(const std::shared_ptr<const global_trajec_generate::PoseStamped>& msg);
    void webGoalPoseCallback(const std::shared_ptr<const global_trajec_generate::PoseStamped>& msg);
    
    // 内部函数
    void controlLoop();
    void setDefaultParameters();
    bool loadConfigurationFromYAML(const std::string& yaml_file);
};

#endif // GLOBAL_TRAJEC_GENERATE_H
