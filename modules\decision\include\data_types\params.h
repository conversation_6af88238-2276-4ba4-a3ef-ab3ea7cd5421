#pragma once
#include <string>
// 参数结构体
struct Params {
    // 决策频率
    double frequency = 10.0;
    
    // 安全参数
    double min_safe_distance = 0.6;
    double collision_time_threshold = 2.0;
    double emergency_stop_distance = 0.3;
    
    // 走廊检测参数
    double corridor_min_width = 0.8;
    double corridor_max_width = 2.5;
    double min_wall_length = 1.5;
    int min_points_per_side = 15;
    double max_angle_deviation = 0.2;
    
    // 速度参数
    double max_speed = 1.5;
    double normal_speed = 1.0;
    double slow_speed = 0.4;
    double stair_speed = 0.2;
    double turn_speed_reduction = 0.6;
    
    // 步态参数
    std::string default_gait = "TROT";
    std::string stair_gait = "WALK";
    std::string obstacle_gait = "CRAWL";
    std::string narrow_gait = "CRAWL";
    std::string yield_gait = "STAND";
    
    // 地形参数
    double slope_threshold = 0.15;
    double step_height_threshold = 0.15;
    double step_width_threshold = 0.25;
    int min_steps = 2;
    double rough_terrain_threshold = 0.08;
};