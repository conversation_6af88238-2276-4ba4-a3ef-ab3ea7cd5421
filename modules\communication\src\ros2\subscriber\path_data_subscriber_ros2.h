#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <nav_msgs/msg/path.hpp>

#include "ros2/core/subscriber_base_ros2.h"
#include "data_types/path_data.h"

namespace communication::ros2
{

    class PathDataSubscriberRos2 : public SubscriberBaseRos2<PathData, nav_msgs::msg::Path>
    {
    public:
        PathDataSubscriberRos2(rclcpp::Node::SharedPtr node,
                               const std::string &topic,
                               //    typename SubscriberBaseRos2<PathData, nav_msgs::msg::Path>::CallbackType callback = nullptr,
                               size_t max_buffer_size = 10)
            : SubscriberBaseRos2<PathData, nav_msgs::msg::Path>(node, topic, max_buffer_size)
        {
        }

    protected:
        virtual void FromMsg(const nav_msgs::msg::Path &msg, PathData &data) override
        {
            data.time_ = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9;
            // data.frame_id = msg.header.frame_id;

            data.poses_.clear();
            for (const auto &pose_stamped : msg.poses)
            {
                PoseData pose;
                pose.time = pose_stamped.header.stamp.sec + pose_stamped.header.stamp.nanosec * 1e-9;
                pose.position[0] = pose_stamped.pose.position.x;
                pose.position[1] = pose_stamped.pose.position.y;
                pose.position[2] = pose_stamped.pose.position.z;
                pose.orientation[0] = pose_stamped.pose.orientation.x;
                pose.orientation[1] = pose_stamped.pose.orientation.y;
                pose.orientation[2] = pose_stamped.pose.orientation.z;
                pose.orientation[3] = pose_stamped.pose.orientation.w;
                data.poses_.push_back(pose);
            }
        }
    };

} // namespace communication::ros2

#endif
