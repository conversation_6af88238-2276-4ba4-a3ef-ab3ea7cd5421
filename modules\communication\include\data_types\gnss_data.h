#pragma once

namespace communication {

struct GNSSData {
    double time = 0.0;  // UTC时间戳，单位：秒

    double latitude = 0.0;   // 纬度，度
    double longitude = 0.0;  // 经度，度
    double altitude = 0.0;   // 海拔，m

    double roll = 0.0;                         // 横滚角，度
    double pitch = 0.0;                        // 俯仰角，度
    double yaw = 0.0;                          // 偏航角，度
    double vel_x = 0.0;                        // 速度X, m/s
    double vel_y = 0.0;                        // 速度Y, m/s
    double vel_z = 0.0;                        // 速度Z, m/s
    int rtk_status = 0;                            // 4:固定解，5：浮点解，2：伪距差分，1：单点解；0：无解
    unsigned char use_sat_num = 0;             // 可用星数
    unsigned char view_sat_num = 0;            // 可见星数
    double llh_std[3] = {0.0, 0.0, 0.0};       // 位置标准差，m
    double attitude_std[3] = {0.0, 0.0, 0.0};  // 姿态标准差，度
    double vel_std[3] = {0.0, 0.0, 0.0};       // 速度标准差，m/s
};

}  // namespace communication
