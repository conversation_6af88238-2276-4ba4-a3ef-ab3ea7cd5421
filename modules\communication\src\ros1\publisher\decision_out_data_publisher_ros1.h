/**
 * @file pose_data_publisher_ros1.h
 * @brief This file contains the implementation of a ROS1 publisher for pose data.
 * * It inherits from the PoseDataPublisherBase class and provides methods to publish pose data in ROS1 format.
 * * The publisher uses the geometry_msgs::PoseStamped message type to represent pose data.
 * * * The class provides methods to convert pose data to the appropriate ROS message format and publish it on a specified topic.
 * * It also includes methods to get the number of subscribers to the topic.
 */
#pragma once
#include <ros/ros.h>
#include <ros1_comm/DecisionOutput.h>

#include "publisher_base.h"
#include "data_types/decision_output.h"

namespace communication::ros1
{
    class DecisionOutPublisherRos1 : public DecisionOutPublisherBase
    {
    public:
        DecisionOutPublisherRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size = 10)
                : DecisionOutPublisherBase(topic, max_buffer_size), nh_(nh), frame_id_("map")
        {
            publisher_ = nh_.advertise<ros1_comm::DecisionOutput>(topic, max_buffer_size, true);//保存最后一条发布的消息
        }

        ~DecisionOutPublisherRos1() = default;

    protected:
        virtual void PublishMsg() override
        {
            publisher_.publish(msg_);
        }

        virtual void ToMsg() override
        {
            msg_.header.stamp = ros::Time(data_.time);
            msg_.header.frame_id = frame_id_;

            msg_.state = data_.state;
            msg_.linear_speed = data_.linear_speed;    // 建议线速度 (m/s)
            msg_.angular_speed = data_.angular_speed;   // 建议角速度 (rad/s)
            msg_.gait_type = data_.gait_type; // 建议步态类型: STAND, WALK, TROT, GALLOP, CRAWL
        }

        virtual int GetSubscriberCount() override
        {
            return publisher_.getNumSubscribers();
        }

    private:
        ros::NodeHandle &nh_;
        ros::Publisher publisher_;
        // geometry_msgs::PoseWithCovarianceStamped msg_; // ROS message type for pose data
        ros1_comm::DecisionOutput msg_;

        std::string frame_id_;                         // Frame ID for the pose data
    };
} // namespace communication::ros1
