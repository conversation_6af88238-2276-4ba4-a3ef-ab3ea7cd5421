#ifndef _RELOCALIZATION_H_
#define _RELOCALIZATION_H_

#include "DataType.h"
#include "reloc_plugin.h"
#include <mutex>
#include <condition_variable>
#include <deque>
#include "basis.h"
#include "yaml_config.h"
using namespace common_lib;
class Relocalization
{
public:
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
    
    Relocalization();
    virtual ~Relocalization();

public:
    void Initialize();

    // void UpdateRelocPos(const ManualPos& pose);
    // void UpdateInputCloud(const CloudData::CLOUD_PTR& input_cloud_ptr);

    utils::Pose getRelocPos();
    // bool bNeedReloc();
    bool ManualPosReloc(const ManualPos& manualpos, const PointCloudXYZI::Ptr& input_cloud_ptr);
    bool rtk_reloc(const GnssENU & rtk_pos_data, const PointCloudXYZI::Ptr& input_cloud_ptr);

private:
    // 互斥锁和条件变量
    std::mutex m_mtx_buffer;
   
    bool m_bNeedRelocal;
    ManualPos m_manualPos;
    bool m_bManualPosUpdate;
    bool m_bInputCloudUpdate;
    utils::PointICloudPtr m_input_cloud_ptr;
    // 重定位状态
    //bool m_initialized{false};
    bool m_reloc_success;
    double m_last_timestamp;

    double m_search_radius{5.0}; // 局部重定位搜索半径

    // 配置
    common_lib::YamlConfig& m_Config;

private:
    cx_string m_sMapPath;
    alg::RelocService::relocParams m_reloc_params;
    std::shared_ptr<plugins::RelocPlugin> m_ptrRelocplugin; // bfs搜索功能指针
};

Relocalization* GetSingleton4Relocalization();

#endif