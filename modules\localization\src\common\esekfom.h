#ifndef ESEKFOM_EKF_H
#define ESEKFOM_EKF_H

#include <vector>
#include <deque>
#include <Eigen/Core>
#include <Eigen/Geometry>
#include <Eigen/Dense>
#include <Eigen/Sparse>
#include <pcl/kdtree/kdtree_flann.h>
#include "use-ikfom.h"
#include <ikd-Tree/ikd_Tree.h>

extern const double epsi;
extern double g_match_rate_threshold;

namespace esekfom
{
    using namespace Eigen;

    extern PointCloudXYZI::Ptr normvec;
    extern PointCloudXYZI::Ptr laserCloudOri;
    extern PointCloudXYZI::Ptr corr_normvect;
    extern bool point_selected_surf[100000];

    struct dyn_share_datastruct
    {
        bool valid;
        bool converge;
        Eigen::Matrix<double, Eigen::Dynamic, 1> h;
        Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> h_x;
    };

    class esekf
    {
    public:
        typedef Matrix<double, 24, 24> cov;
        typedef Matrix<double, 24, 1> vectorized_state;
        esekf();
        ~esekf();

        state_ikfom get_x();
        cov get_P();
        void change_x(state_ikfom &input_state);
        void change_P(cov &input_cov);

        state_ikfom boxplus(state_ikfom x, Eigen::Matrix<double, 24, 1> f_);
        void predict(double &dt, Eigen::Matrix<double, 12, 12> &Q, const input_ikfom &i_in);

        void h_share_model(
            dyn_share_datastruct &ekfom_data,
            PointCloudXYZI::Ptr &feats_down_body,
            pcl::KdTreeFLANN<PointType> &ikdtree,
            std::vector<PointVector> &Nearest_Points,
            bool extrinsic_est, bool &need_relocal, int &effct_feat_num, int &vaild_points);

        vectorized_state boxminus(state_ikfom x1, state_ikfom x2);

        bool update_iterated_dyn_share_modified(
            double R, PointCloudXYZI::Ptr &feats_down_body,
            pcl::KdTreeFLANN<PointType> &ikdtree, std::vector<PointVector> &Nearest_Points, int maximum_iter,
            bool extrinsic_est, int &effct_feat_num, int &vaild_points,
            Eigen::Matrix3d Sigma_leg, Eigen::Vector3d z_leg, bool useleg, bool &need_relocal,
            Eigen::Matrix3d Sigma_rtk, Eigen::Vector3d z_rtk, bool rtk_vaild, Eigen::Vector3d cur_atti, double z_heading, bool rtk_heading_vaild,
            const MeasureGroup &meas);

        std::deque<bool> deq;

    private:
        state_ikfom x_;
        cov P_;
    };
} // namespace esekfom

#endif // ESEKFOM_EKF_HPP1