#ifndef YAML_CONFIG_IMPL_H
#define YAML_CONFIG_IMPL_H

#include "yaml_config.h"
#include <algorithm>

namespace common_lib {

// 特化实现
template <>
bool YamlConfig::ConvertValue<std::string>(const std::string& str, std::string* value) const {
    *value = str;
    return true;
}

template <>
bool YamlConfig::ConvertValue<bool>(const std::string& str, bool* value) const {
    std::string lower_str = str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);
    
    if (lower_str == "true" || lower_str == "1" || lower_str == "yes" || lower_str == "on") {
        *value = true;
        return true;
    }
    if (lower_str == "false" || lower_str == "0" || lower_str == "no" || lower_str == "off") {
        *value = false;
        return true;
    }
    return false;
}

template <>
bool YamlConfig::ConvertValue<int>(const std::string& str, int* value) const {
    try {
        *value = std::stoi(str);
        return true;
    } catch (...) {
        return false;
    }
}

template <>
bool YamlConfig::ConvertValue<double>(const std::string& str, double* value) const {
    try {
        *value = std::stod(str);
        return true;
    } catch (...) {
        return false;
    }
}

template <>
bool YamlConfig::ConvertValue<float>(const std::string& str, float* value) const {
    try {
        *value = std::stof(str);
        return true;
    } catch (...) {
        return false;
    }
}

template <>
bool YamlConfig::ConvertValue<std::vector<double>>(const std::string& str, std::vector<double>* value) const {
    std::istringstream iss(str);
    value->clear();
    double num;
    while (iss >> num) {
        value->push_back(num);
    }
    return !value->empty() || str.empty();
}

}  // namespace common_lib

#endif  // YAML_CONFIG_IMPL_H