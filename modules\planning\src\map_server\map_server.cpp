#include "map_loader.h"
#include <iostream>
#include <string>
#include <chrono>
#include <memory>
#include "communication.h"
#include "data_types/map_data.h"
#include <thread>
using communication::OccupancyGrid;

std::shared_ptr<communication::OccupancyGridPublisherBase> map_data_publisher_ptr;

void DataPublisher(const OccupancyGrid& map) {
    while (true) {
        map_data_publisher_ptr->Publish(map);
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

int main(int argc, char** argv) {
    if (argc != 2) {
        std::cerr << "Usage: map_server <map.yaml>" << std::endl;
        return -1;
    }

    OccupancyGrid map;
    try {
        MapLoader loader(argv[1], map);
    } catch (std::exception& e) {
        std::cerr << "Failed to load map: " << e.what() << std::endl;
        return -1;
    }

    /**/
    // 初始化通信模块
    auto communication_ptr = std::make_shared<communication::Communication>("map_server");
    if (!communication_ptr->Initialize("config/communication_config.yaml")) {
        std::cerr << "通信模块初始化失败" << std::endl;
        return -1;
    }

    // 创建Publisher
    map_data_publisher_ptr = communication_ptr->CreateMapPublisher("/map", "map", 100); // 需在communication中实现CreateMapPublisher

    // 填充header
    map.header.frame_id = "map";
    map.header.stamp = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    /*
        // 打印地图信息
    std::cout << "Map Info:" << std::endl;
    std::cout << "  Resolution: " << map.info.resolution << std::endl;
    std::cout << "  Width: " << map.info.width << std::endl;
    std::cout << "  Height: " << map.info.height << std::endl;
    std::cout << "  Origin Position: (" 
              << map.info.origin_position.x << ", "
              << map.info.origin_position.y << ", "
              << map.info.origin_position.z << ")" << std::endl;
    std::cout << "  Origin Orientation: ("
              << map.info.origin_orientation.x << ", "
              << map.info.origin_orientation.y << ", "
              << map.info.origin_orientation.z << ", "
              << map.info.origin_orientation.w << ")" << std::endl;
    std::cout << "  Data size: " << map.data.size() << std::endl;
    for (int i = 0; i < map.info.width; i++) {
        for (int j = 0; j < map.info.height; j++) {
            std::cout << (int)map.data[i * map.info.width + j] << " ";
        }
        std::cout << std::endl;
    }   
    
    // 发布地图
    map_data_publisher_ptr->Publish(map);
    */
    std::thread data_publisher_thread(DataPublisher, map);
    data_publisher_thread.detach();

    // 进入通信主循环
    communication_ptr->Run();
    



    return 0;
}