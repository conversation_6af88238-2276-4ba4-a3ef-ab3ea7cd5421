#include "cx_event.h"
namespace common_lib {
#ifdef WIN32
CXEvent::CXEvent(cx_bool bIsManualReset, cx_bool bInitialSignaled)
    : m_bIsManualReset(bIsManualReset)
    , m_bEventStatus(bInitialSignaled) {}

CXEvent::~CXEvent() {
  CloseHandle(m_hEvent);
}

cx_bool CXEvent::Create() {
  m_hEvent = CreateEvent(NULL, m_bIsManualReset, m_bEventStatus, NULL);

  if (m_hEvent != NULL) {
    return true;
  } else {
    return false;
  }
}

cx_bool CXEvent::Set() {
  return SetEvent(m_hEvent);
}

cx_bool CXEvent::Reset() {
  return ResetEvent(m_hEvent);
}

cx_bool CXEvent::Wait(int cms) {
  cx_int iResult = WaitForSingleObject(m_hEvent, cms);
  return (WAIT_OBJECT_0 == iResult);
}

cx_bool CXEvent::EnsureInitialized() {
  return (m_hEvent != NULL);
}

cx_bool CXEvent::IsTriggered() {
  return Wait(0);
}

cx_bool CXEvent::Destroy() {
  CloseHandle(m_hEvent);
  m_hEvent = NULL;

  return 0;
}

#else

CXEvent::CXEvent(cx_bool bIsManualReset, cx_bool bInitialSignaled)
    : is_manual_reset_(bIsManualReset)
    , is_event_status_(bInitialSignaled)
    , is_mutex_initialized_(false)
    , is_cond_initialized_(false) {}

CXEvent::~CXEvent() {
  if (is_mutex_initialized_) {
    pthread_mutex_destroy(&mutex_);
    is_mutex_initialized_ = false;
  }

  if (is_cond_initialized_) {
    pthread_cond_destroy(&cond_);
    is_cond_initialized_ = false;
  }
}

cx_bool CXEvent::Create() {
  if (!is_mutex_initialized_) {
    if (0 == pthread_mutex_init(&mutex_, NULL)) {
      is_mutex_initialized_ = true;
    }
  }

  if (!is_cond_initialized_) {
    if (0 == pthread_cond_init(&cond_, NULL)) {
      is_cond_initialized_ = true;
    }
  }

  return (is_mutex_initialized_ && is_cond_initialized_);
}

cx_bool CXEvent::EnsureInitialized() {
  return (is_mutex_initialized_ && is_cond_initialized_);
}

cx_bool CXEvent::Set() {
  if (!EnsureInitialized()) {
    return false;
  }

  pthread_mutex_lock(&mutex_);
  is_event_status_ = true;
  pthread_cond_broadcast(&cond_);
  pthread_mutex_unlock(&mutex_);

  return true;
}

cx_bool CXEvent::Reset() {
  if (!EnsureInitialized()) {
    return false;
  }

  pthread_mutex_lock(&mutex_);
  is_event_status_ = false;
  pthread_mutex_unlock(&mutex_);

  return true;
}

cx_bool CXEvent::Wait(cx_int cms) {
  if (!EnsureInitialized()) {
    return false;
  }

  pthread_mutex_lock(&mutex_);
  cx_int error = 0;
  if (cms != INFINITE) {
    struct timeval tv;
    gettimeofday(&tv, NULL);

    struct timespec ts;
    ts.tv_sec = tv.tv_sec + (cms / 1000);
    ts.tv_nsec = tv.tv_usec * 1000 + (cms % 1000) * 1000000;

    if (ts.tv_nsec >= 1000000000) {
      ts.tv_sec++;
      ts.tv_nsec -= 1000000000;
    }

    while ((!is_event_status_) && (error == 0)) {
      error = pthread_cond_timedwait(&cond_, &mutex_, &ts);
    }
  } else {
    while ((!is_event_status_) && (error == 0)) {
      error = pthread_cond_wait(&cond_, &mutex_);
    }
  }

  if (0 == error && !is_manual_reset_) {
    is_event_status_ = false;
  }

  pthread_mutex_unlock(&mutex_);

  return (0 == error);
}

cx_bool CXEvent::IsTriggered() {
  return is_event_status_;
}

cx_bool CXEvent::Destroy() {
  if (is_mutex_initialized_) {
    pthread_mutex_destroy(&mutex_);
    is_mutex_initialized_ = false;
  }

  if (is_cond_initialized_) {
    pthread_cond_destroy(&cond_);
    is_cond_initialized_ = false;
  }

  return true;
}
}
#endif
