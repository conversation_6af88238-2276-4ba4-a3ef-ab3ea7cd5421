#include "obstacle_devide.h"
#include <pcl/filters/voxel_grid.h>
using namespace perception;


typedef struct{
    float width;        //宽 ，与lidar x轴线一致
    float height;       //高 ，与lidar y轴线一致
}RobotBox;

ObstacleDevide::ObstacleDevide(const ObstacleDevideParameter& parameter){
    parameter_ = parameter;
    patchworkpp_ = std::make_unique<patchwork::PatchWorkpp>(parameter_.patchwork_param);
}
ObstacleDevide::~ObstacleDevide(){
}


void ObstacleDevide::devide_cloud(const PointCloudT::Ptr& cloud, PointCloudT::Ptr& ground_cloud,
                    PointCloudT::Ptr& dynamic_cloud,PointCloudT::Ptr& static_cloud){

  
    PointCloudT::Ptr crop_cloud(new PointCloudT);
    RobotBox box=parameter_.robot_box;
    for(size_t i=0;i<cloud->size();++i){
        PointT point=cloud->points[i];
        if( point.x < box.min_x || point.x > box.max_x 
            || point.y < box.min_y || point.y > box.max_y
            || point.z < box.min_z || point.z > box.max_z){
            crop_cloud->push_back(point);
        }
    }

    /*
    PointCloudT::Ptr cloud_filtered(new PointCloudT);
    pcl::VoxelGrid<PointT> sor;
    sor.setInputCloud (crop_cloud);
    sor.setLeafSize (0.1f, 0.1f, 0.1f);
    sor.filter (*cloud_filtered);
    */

    patchworkpp_->estimateGround(utils::PointCloudToEigenMat(crop_cloud));
    PointCloudT::Ptr nonground_cloud = utils::EigenMatToPointCloud(patchworkpp_->getNonground());
    ground_cloud = utils::EigenMatToPointCloud(patchworkpp_->getGround());

    static_cloud = nonground_cloud;   //临时调试

}