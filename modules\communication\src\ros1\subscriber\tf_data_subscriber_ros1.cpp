#include "subscriber/tf_data_subscriber_ros1.h"

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1{

TFDataSubscriberRos1::TFDataSubscriberRos1(ros::NodeHandle &nh, const std::string& target_frame_id, const std::string& source_frame_id)
            : nh_(nh), TFDataSubscriberBase(source_frame_id + "_" + target_frame_id + "_tf" , 1), 
            tfBuffer_(), tfListener_(tfBuffer_), target_frame_id_(target_frame_id), source_frame_id_(source_frame_id) 
{
            
}


} // namespace communication::ros1{
#endif
    