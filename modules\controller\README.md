# Controller Module

## 概述

Controller模块是移动机器人导航系统的核心控制模块，包含校准控制（calibration_node）和速度平滑（velocity_smoother_node）两个主要功能节点。

## 功能特性

### Calibration Node (calibration_node)
- **功能**: 机器人位姿校准和精确控制
- **输入**: 里程计数据、目标位姿、模式控制
- **输出**: 速度命令、停止信号、状态信息
- **特点**: 支持PID控制、误差限幅、精度控制

### Velocity Smoother Node (velocity_smoother_node)
- **功能**: 速度命令平滑处理
- **输入**: 原始速度命令、里程计反馈
- **输出**: 平滑后的速度命令
- **特点**: 支持加速度限制、减速控制、多种反馈模式

## 快速开始

### 1. 编译项目
```bash
# 在项目根目录执行
./build.sh --p x86_64 --b release --c ros1 --n cpu
```

### 2. 启动所有节点
```bash
# 启动controller模块的所有节点
roslaunch controller controller.launch
```

### 3. 测试模式启动
```bash
# 启动测试模式（包含模拟数据）
roslaunch controller controller_test.launch
```

## 文件结构

```
modules/controller/
├── bin/                    # 可执行文件
│   ├── calibration_node
│   ├── velocity_smoother_node
│   └── controller_node
├── config/                 # 配置文件
│   ├── calibration.yaml
│   ├── velocity_smoother_config.yaml
│   └── communication_config.yaml
├── launch/                 # Launch文件
│   ├── controller.launch
│   ├── controller_test.launch
│   └── README.md
├── rviz/                   # RViz配置
│   └── controller.rviz
├── include/                # 头文件
├── src/                    # 源代码
├── CMakeLists.txt          # 构建配置
├── package.xml             # ROS包配置
└── README.md               # 本文件
```

## 配置说明

### 校准参数 (calibration.yaml)
```yaml
# PID控制参数
p_vel_yaw: 0.6
i_vel_yaw: 0.08
d_vel_yaw: 0.0
p_vel_x: 0.66
i_vel_x: 0.08
d_vel_x: 0.1
p_vel_y: 0.58
i_vel_y: 0.1
d_vel_y: 0.01

# 误差限幅
errorYaw_max: 3.0
errorYaw_min: 1.0
errorX_max: 3.0
errorY_max: 3.0

# 速度限幅
Yaw_max: 0.5
X_max: 0.1
Y_max: 0.1

# 精度参数
set_yaw_precision: 0.3
set_x_precision: 0.15
set_y_precision: 0.15
```

### 速度平滑器参数 (velocity_smoother_config.yaml)
```yaml
# 基本速度限制
speed_lim_v: 1.0          # 最大线速度 (m/s)
speed_lim_w: 1.0          # 最大角速度 (rad/s)

# 加速度限制
accel_lim_v: 1.0          # 线加速度限制 (m/s²)
accel_lim_w: 1.0          # 角加速度限制 (rad/s²)

# 控制参数
frequency: 20.0           # 控制频率 (Hz)
decel_factor: 1.0         # 减速因子
```

## 话题说明

### 输入话题
- `/Odometry` - 里程计数据
- `/move_base_simple/goal` - 导航目标
- `/web_goal` - Web界面目标
- `/calibration_mode` - 校准模式控制
- `/cmd_vel` - 速度命令
- `/twist/data` - 速度数据

### 输出话题
- `/cmd_vel_raw` - 原始速度命令
- `/cmd_vel_smooth` - 平滑后速度命令
- `/emergency_stop` - 紧急停止信号
- `/inner_emergency_stop` - 内部紧急停止
- `/calibration_mode_status` - 校准模式状态

## 使用方法

### 基本启动
```bash
# 启动所有节点
roslaunch controller controller.launch
```

### 自定义参数
```bash
# 指定配置文件路径
roslaunch controller controller.launch config_dir:=/path/to/config

# 不启动RViz
roslaunch controller controller.launch use_rviz:=false
```

### 测试模式
```bash
# 启动测试模式（包含模拟数据）
roslaunch controller controller_test.launch

# 禁用测试模式
roslaunch controller controller_test.launch test_mode:=false
```

## 调试和监控

### 查看话题
```bash
# 列出所有话题
rostopic list

# 查看速度命令
rostopic echo /cmd_vel_smooth

# 查看里程计数据
rostopic echo /Odometry
```

### 查看节点状态
```bash
# 列出所有节点
rosnode list

# 查看节点信息
rosnode info /calibration_node
rosnode info /velocity_smoother_node
```

### 使用RViz可视化
```bash
# 启动RViz
rosrun rviz rviz -d $(find controller)/rviz/controller.rviz
```

## 故障排除

### 常见问题

1. **节点启动失败**
   - 检查可执行文件是否存在
   - 确认配置文件路径正确
   - 查看错误日志

2. **话题连接失败**
   - 使用`rostopic list`检查话题
   - 确认发布者和订阅者都已启动
   - 检查话题名称是否正确

3. **TF变换错误**
   - 使用`tf_echo`检查变换
   - 确认坐标系名称正确
   - 检查TF发布者是否启动

### 调试命令
```bash
# 查看节点日志
rosnode kill /calibration_node
roslaunch controller controller.launch --screen

# 检查话题频率
rostopic hz /cmd_vel_smooth

# 查看话题统计
rostopic bw /Odometry
```

## 开发说明

### 添加新节点
1. 在`src/`目录下添加源代码
2. 在`CMakeLists.txt`中添加编译规则
3. 在`launch/`目录下创建launch文件
4. 更新`package.xml`中的依赖

### 修改配置
1. 编辑相应的YAML配置文件
2. 重启节点以应用新配置
3. 使用`rosparam`命令动态修改参数

## 许可证

MIT License

## 联系方式

如有问题，请联系开发团队。 