#ifndef PERCEPTION_UTILS__HH_H_
#define PERCEPTION_UTILS__HH_H_

#include <condition_variable>
#include <opencv2/opencv.hpp>
#include <pcl/point_types.h>
#include <pcl/point_cloud.h>
#include <Eigen/Core>

#include "data_struct.h"

namespace perception{

typedef pcl::PointXYZI  PointT ;
typedef pcl::PointCloud<PointT> PointCloudT;


typedef struct{
    cv::Mat             mask;
    cv::Mat             yolo_segment_image;
    cv::Mat             repreject_image;
    PointCloudT::Ptr    depth_cloud;
}SegmentDebugInfo;

typedef struct{
    double              max_depth;
    size_t              grid;
    CameraInfo          camera_info;
    Eigen::Matrix4d     transform;
    std::string         cfg;

}DepthPointCloudConvertParameter;

typedef struct {
    PointCloudT::Ptr    result;
    
#ifdef OUTPUT_DEBUG
    SegmentDebugInfo    debug_info;
#endif

 } PerceptionOut;

typedef  void (*PerceptionOutputCallback)(PerceptionOut output) ;


class BoolCondition{
    public:
        BoolCondition();
        ~BoolCondition();
        void NotifyOne();
        void Wait();
        void Reset();
    
    protected:
        std::mutex                              mtx_;
        std::condition_variable                 cv_;
        bool                                    condition_;
};


namespace utils{

    Eigen::MatrixXf PointCloudToEigenMat(PointCloudT::Ptr &cloud);
    PointCloudT::Ptr EigenMatToPointCloud(Eigen::MatrixXf mat);

}

    
} // namespace name



#endif