/*
 * 初始对准算法实现
 */
#pragma once

#include <deque>

#define D2R(deg)  (deg * 0.01745329252)
#define R2D(rad)  (rad * 57.2957795131)

struct ImuData
{
    double gyro[3];
    double acc[3];

    ImuData(double gyroX,double gyroY, double gyroZ,double accX,double accY,double accZ )
    {
        gyro[0] = gyroX;
        gyro[1] = gyroY;
        gyro[2] = gyroZ;

        acc[0] = accX;
        acc[1] = accY;
        acc[2] = accZ;
    }
};

class CZeroVelDetect
{
    public:
        CZeroVelDetect();
        ~CZeroVelDetect();

        // 初始化零速检测队列
        void Init(double gyro_std_threshold, double acc_std_threshold);

        // 判断是否停止
        bool stop_detect(const ImuData& imu_data);

        bool get_stop_status() 
        { 
            return zero_vel_; 
        }

    protected:
        //通过imu六轴数据，判断是否禁止,0: 运动，1：静止，-1：不确定
        char static_detect(const ImuData &imu_data);

        //计算标准差
        double CalVar(std::deque<double>& dq);

    private:
        std::deque<double> dq_gyro_;
        std::deque<double> dq_acc_;
        double gyro_std_threshold_;
        double acc_std_threshold_;

        bool zero_vel_;
};



