#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/int32.hpp>
#include <std_msgs/msg/float64.hpp>
#include <std_msgs/msg/string.hpp>
#include <std_msgs/msg/bool.hpp>
#include <std_msgs/msg/float32.hpp>

#include "ros2/core/subscriber_base_ros2.h"

namespace communication::ros2
{

    template <typename T, typename T_MSG>
    class SimpleDataSubscriberRos2 : public SubscriberBaseRos2<T, T_MSG>
    {
    public:
        SimpleDataSubscriberRos2(rclcpp::Node::SharedPtr node,
                                 const std::string &topic,
                                 //  typename SubscriberBaseRos2<T, T_MSG>::CallbackType callback = nullptr,
                                 size_t max_buffer_size = 10)
            : SubscriberBaseRos2<T, T_MSG>(node, topic, max_buffer_size)
        {
        }

    protected:
        virtual void FromMsg(const T_MSG &msg, T &data) override
        {
            data = msg.data;
        }
    };

    using IntDataSubscriberRos2 = SimpleDataSubscriberRos2<int, std_msgs::msg::Int32>;
    using DoubleDataSubscriberRos2 = SimpleDataSubscriberRos2<double, std_msgs::msg::Float64>;
    using StringDataSubscriberRos2 = SimpleDataSubscriberRos2<std::string, std_msgs::msg::String>;
    using BoolDataSubscriberRos2 = SimpleDataSubscriberRos2<bool, std_msgs::msg::Bool>;
    using FloatDataSubscriberRos2 = SimpleDataSubscriberRos2<float, std_msgs::msg::Float32>;

} // namespace communication::ros2

#endif
