#ifndef __YOLO_V8_RKNN__H__
#define __YOLO_V8_RKNN__H__



#include <string>
#include <vector>
#include <opencv2/opencv.hpp>

#include "postprocess.h"
#include "image_utils.h"



typedef struct {
    cv::Mat image;
    float box_thresh;
    float nms_thresh;
} input_source;

typedef struct {
    cv::Mat mask;
    std::vector<object_detect_result> boxes;
} segment_out;

class Yolov8Rknn {
public:
    Yolov8Rknn();
    ~Yolov8Rknn();
    bool Init(std::string model_path);
    bool Seg(input_source src, segment_out& segment) ;
    void Release();
    cv::Mat debug_img(cv::Mat src,const segment_out& segment);

protected:

    bool rknn_inference(input_source src, object_detect_result_list* od_results);

    static void object_detect_result_list2segment_out(object_detect_result_list& od_results,
                                                        uint32_t width,uint32_t height,
                                                        segment_out& segment);

    static unsigned char* load_model(const char* filename, int* model_size);
    static void dump_tensor_attr(rknn_tensor_attr *attr);
    static bool create_image_buffer(image_buffer_t* buffer,int32_t width,int32_t height,image_format_t format);
    static void release_image_buffer(image_buffer_t buffer);

    

    
protected:
    rknn_app_context_t rknn_app_ctx_;
    
};



#endif