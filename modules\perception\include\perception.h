#ifndef __PERCEPTION__H__HH__
#define __PERCEPTION__H__HH__

#include <string>
#include <memory>
#include <pcl/point_types.h>
#include <pcl/point_cloud.h>
#include <opencv2/opencv.hpp>
#include <Eigen/Geometry>
#include "common/utils.h"
namespace perception{

class PerceptionImpl;

class Perception
{
public:
    Perception(const std::string &config_file_path);
    ~Perception();

    void PushCloud(PointCloudT::Ptr cloud);
    void PushRgbImage(cv::Mat rgb);
    void PushDepthImage(cv::Mat depth);
    void SetRgbIntrinsic(const CameraInfo& info );
    void SetDepthIntrinsic(const CameraInfo& info );
    void SetDepth2RgbTransform(const TransformExtrinsics& transform);
    void SetOutputCallback(PerceptionOutputCallback callback);
protected:

private:
    std::unique_ptr<PerceptionImpl>         perception_impl_;
    

};

} // namespace planning{

#endif
