#include "communication.h"
#include "rrt_star_global_planner.h"
#include <chrono>
#include <iomanip>
#include <iostream>
#include <memory>
#include <thread>
#include <signal.h>

using namespace RRTstar_planner;

static bool g_running = true;

// 全局对象
std::unique_ptr<RRTstarPlanner> rrt_star_planner_ptr;
std::shared_ptr<communication::OdometrySubscriberBase> odom_subscriber_ptr;
std::shared_ptr<communication::PoseDataSubscriberBase> goal_subscriber_ptr;
std::shared_ptr<communication::PoseDataSubscriberBase> pose_subscriber_ptr;
std::shared_ptr<communication::PoseDataSubscriberBase> test_pose_subscriber_ptr;
std::shared_ptr<communication::Int8DataSubscriberBase> replan_subscriber_ptr;

std::shared_ptr<communication::PathDataPublisherBase> path_publisher_ptr;
std::shared_ptr<communication::BoolDataPublisherBase> accessable_publisher_ptr;
std::shared_ptr<communication::Int8DataPublisherBase> goal_success_publisher_ptr;
std::shared_ptr<communication::Int8DataPublisherBase> innerpubStop;



/**
 * @brief 信号处理函数，用于优雅退出
 * @param signum 信号编号
 */
void signalHandler(int signum) {
    std::cout << "\n收到信号 " << signum << "，正在关闭RRT*全局规划器..."
              << std::endl;
    g_running = false;
    if (rrt_star_planner_ptr) {
        rrt_star_planner_ptr->stop();
        rrt_star_planner_ptr->cleanup();
    }
    std::cout << " RRT*全局规划器节点已安全退出" << std::endl;
}

/**
 * @brief 数据接收线程函数
 * 处理来自communication模块的订阅数据
 */
void DataReceiveThread() {
    while (g_running) {
        // 接收里程计数据
        if (!odom_subscriber_ptr->IsBufferEmpty()) {
            auto msg = odom_subscriber_ptr->GetBuffer();
            planning_common::OdometryData odom_data;
            
            odom_data.header.stamp = msg.front().pose_data.time;
            odom_data.header.frame_id = "map";
            odom_data.pose.pose.position.x = msg.front().pose_data.position[0];
            odom_data.pose.pose.position.y = msg.front().pose_data.position[1];
            odom_data.pose.pose.position.z = msg.front().pose_data.position[2];
            odom_data.pose.pose.orientation.x = msg.front().pose_data.orientation[0];
            odom_data.pose.pose.orientation.y = msg.front().pose_data.orientation[1];
            odom_data.pose.pose.orientation.z = msg.front().pose_data.orientation[2];
            odom_data.pose.pose.orientation.w = msg.front().pose_data.orientation[3];

            
            // 使用新的数据接收函数更新里程计数据
            rrt_star_planner_ptr->updateOdometry(odom_data);
        }

        // 接收目标点数据
        if (!goal_subscriber_ptr->IsBufferEmpty()) {
            auto msg = goal_subscriber_ptr->GetBuffer();
            planning_common::PoseStamped goal_msg;
            goal_msg.time = msg.front().time;
            goal_msg.pose.position.x = msg.front().position[0];
            goal_msg.pose.position.y = msg.front().position[1];
            goal_msg.pose.position.z = msg.front().position[2];
            goal_msg.pose.orientation.x = msg.front().orientation[0];
            goal_msg.pose.orientation.y = msg.front().orientation[1];
            goal_msg.pose.orientation.z = msg.front().orientation[2];
            goal_msg.pose.orientation.w = msg.front().orientation[3];
            rrt_star_planner_ptr->updateGoal(goal_msg);
        }
        /*这里需要重构把模块进一步分离
        if (!path_publisher_ptr->IsBufferEmpty()) {
            auto msg = path_publisher_ptr->GetBuffer();
           // rrt_star_planner_ptr->updatePath(msg);
        }

        if(!pose_subscriber_ptr->IsBufferEmpty()){
            auto msg = pose_subscriber_ptr->GetBuffer();
            //rrt_star_planner_ptr->updatePoseData(msg);
        }

        if(!test_pose_subscriber_ptr->IsBufferEmpty()){
            auto msg = test_pose_subscriber_ptr->GetBuffer();
            //rrt_star_planner_ptr->updatePoseData(msg);
        }

        if(!replan_subscriber_ptr->IsBufferEmpty()){
            auto msg = replan_subscriber_ptr->GetBuffer();
            //rrt_star_planner_ptr->updatePath(msg);  
        */
        // 短暂休眠，避免过度占用CPU
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

/**
 * @brief 状态监控线程函数
 * 定期打印规划器状态和统计信息
 */
void StatusMonitorThread() {
    while (g_running) {
        if (rrt_star_planner_ptr && rrt_star_planner_ptr->isInitialized()) {
            // 每30秒打印一次状态信息
            static int status_counter = 0;
            status_counter++;
            
            if (status_counter >= 3000) { // 30秒 (3000 * 10ms)
                std::cout << "\n=== RRT*规划器状态监控 ===" << std::endl;
                rrt_star_planner_ptr->printStatus();
                
                // 打印统计信息
                size_t total_attempts, successful_attempts;
                double success_rate = rrt_star_planner_ptr->getPlanningSuccessRate(total_attempts, successful_attempts);
                
                if (total_attempts > 0) {
                    std::cout << "规划统计: 成功率=" << (success_rate * 100.0) << "%, "
                              << "总尝试=" << total_attempts << ", 成功=" << successful_attempts << std::endl;
                    std::cout << "平均规划时间: " << rrt_star_planner_ptr->getAveragePlanningTime() << " 秒" << std::endl;
                    std::cout << "平均节点数: " << rrt_star_planner_ptr->getAveragePlanningNodes() << std::endl;
                    std::cout << "平均路径长度: " << rrt_star_planner_ptr->getAveragePathLength() << " 米" << std::endl;
                }
                
                status_counter = 0;
            }
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

void DataPublishThread(){
    while(g_running){
        if(rrt_star_planner_ptr && rrt_star_planner_ptr->isInitialized()){
           // rrt_star_planner_ptr->publishPath();
        }
    }
}

int main(int argc, char **argv) {
    std::cout << "=== RRT*全局规划器节点启动 ===" << std::endl;
    
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    try {
        // 配置文件路径
        std::string config_path = "../config/rrt_star_config.yaml";
        
        // 创建通信模块实例
        auto communication_ptr = std::make_shared<communication::Communication>("rrt_star_global_planner");
        if (!communication_ptr->Initialize("config/communication_config.yaml")) {
            std::cerr << " 通信模块初始化失败" << std::endl;
            return -1;
        }
        
        // 创建RRT*规划器
        std::cout << "\n 创建RRT*全局规划器..." << std::endl;
        rrt_star_planner_ptr = std::make_unique<RRTstarPlanner>("rrt_star_global_planner", nullptr);
        
        // 从配置文件初始化规划器
        if (!rrt_star_planner_ptr->initializeFromConfig(config_path)) {
            std::cout << " 从配置文件初始化失败，使用默认参数" << std::endl;
        }
        
        // 设置规划器参数
        rrt_star_planner_ptr->setPlanningMode(0); // 标准模式
        rrt_star_planner_ptr->setGoalBiasProbability(0.2);
        rrt_star_planner_ptr->setUseBidirectionalSearch(true);
        rrt_star_planner_ptr->setEnableVisualization(true);
        rrt_star_planner_ptr->setEnableDebug(false);
        rrt_star_planner_ptr->setPlanningFrequency(10.0);
        rrt_star_planner_ptr->setCollisionThreshold(50);
        rrt_star_planner_ptr->setPathSmoothingFactor(0.5);
        rrt_star_planner_ptr->setPathSimplifyTolerance(0.1);
        
        // 创建订阅器
        odom_subscriber_ptr = communication_ptr->CreateOdometrySubscriber("/Odometry");
        pose_subscriber_ptr = communication_ptr->CreatePoseDataSubscriber("/move_base_simple/goal");
        goal_subscriber_ptr = communication_ptr->CreatePoseDataSubscriber("/web_goal_pose");
        test_pose_subscriber_ptr = communication_ptr->CreatePoseDataSubscriber("/test_poses");
        replan_subscriber_ptr = communication_ptr->CreateInt8DataSubscriber("/replan");
        // 创建发布器
        path_publisher_ptr = communication_ptr->CreatePathDataPublisher("/target_goal", "map", 10);
        accessable_publisher_ptr = communication_ptr->CreateBoolDataPublisher("/ifAccessable", 1);
        goal_success_publisher_ptr = communication_ptr->CreateInt8DataPublisher("/goal_success", 1);
        innerpubStop = communication_ptr->CreateInt8DataPublisher("/istop", 1);



       /*
        rrt_star_planner_ptr->setPathPublishCallback(
            [path_publisher_ptr](const std::vector<PoseStamped>& plan) {
                communication::PathData path_msg;
                path_msg.time_ = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count();
                path_msg.poses_.clear();
                
        

            }
        );
        
        rrt_star_planner_ptr->setAccessablePublishCallback(
            [accessable_publisher_ptr](bool accessable) {
                bool accessable_msg = accessable;
                accessable_publisher_ptr->Publish(accessable_msg);
            }
        );
        */
        // 初始化规划器
        std::cout << "\n 初始化RRT*全局规划器..." << std::endl;
        if (!rrt_star_planner_ptr->isInitialized()) {
            std::cerr << " RRT*全局规划器初始化失败！" << std::endl;
            return -1;
        }
        
        // 打印规划器状态信息
        std::cout << "\n 规划器状态:" << std::endl;
        rrt_star_planner_ptr->printStatus();
        
        // 打印参数配置
        std::cout << "\n 参数配置:" << std::endl;
        rrt_star_planner_ptr->printParameters();
        
        std::cout << "\n RRT*全局规划器节点启动成功！" << std::endl;
        std::cout << "  订阅话题:" << std::endl;
        std::cout << "    /Odometry - 里程计数据" << std::endl;
        std::cout << "    /move_base_simple/goal - 目标点" << std::endl;
        std::cout << "  发布话题:" << std::endl;
        std::cout << "    /global_path - 全局路径" << std::endl;
        std::cout << "    /path_accessable - 路径可达性" << std::endl;
        std::cout << "  控制功能:" << std::endl;
        std::cout << "    - 自动启动/停止规划器" << std::endl;
        std::cout << "    - 实时状态监控" << std::endl;
        std::cout << "    - 规划统计信息" << std::endl;
        std::cout << "    - 可配置参数" << std::endl;
        
        // 启动数据接收线程
        std::thread data_receive_thread(DataReceiveThread);
        data_receive_thread.detach();
        
        // 启动状态监控线程
        std::thread status_monitor_thread(StatusMonitorThread);
        status_monitor_thread.detach();
        
        // 阻塞主线程，运行通信模块
        communication_ptr->Run();
        
    } catch (const std::exception &e) {
        std::cerr << " 程序异常: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
} 