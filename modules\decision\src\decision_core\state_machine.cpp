// =============================================
// 具体状态类实现
// =============================================

#include <iostream>
#include "decision.h"
#include "state_machine.h"
#include "data_types/decision_output.h"

namespace decision{
// StartAccelState 实现
void StartAccelState::enter(Decision& system) {
    std::cout << "Entering StartAccelState" << std::endl;
}

void StartAccelState::execute(Decision& system, DecisionData& decision) {
    decision.state = "ACCELERATE";
    decision.linear_speed = 0.5;  // 初始加速
    decision.gait_type = system.GetParams().default_gait;
    
    // 检查是否应该转换状态
    if (system.GetContext().localization.vel.norm() > 0.8) {
        system.ChangeState(std::make_shared<NormalCruiseState>());
    }
}

void StartAccelState::exit(Decision& system) {
    std::cout << "Exiting StartAccelState" << std::endl;
}

// NormalCruiseState 实现
void NormalCruiseState::enter(Decision& system) {
    std::cout << "Entering NormalCruiseState" << std::endl;
}

void NormalCruiseState::execute(Decision& system, DecisionData& decision) {
    decision.state = "CRUISE";
    decision.linear_speed = system.GetParams().normal_speed;
    decision.gait_type = system.GetParams().default_gait;
    
    // 检查状态转换条件
    if (system.GetContext().frontal_obstacle_detected) {
        system.ChangeState(std::make_shared<ObstacleAvoidState>());
    } else if (system.GetContext().corridor_detected) {
        system.ChangeState(std::make_shared<CenterNavState>());
    } else if (system.GetContext().slope_detected) {
        system.ChangeState(std::make_shared<SlopeState>());
    } else if (system.GetContext().stairs_detected) {
        system.ChangeState(std::make_shared<StairState>());
    } else if (system.GetContext().rough_terrain_detected) {
        system.ChangeState(std::make_shared<RoughTerrainState>());
    }
}

void NormalCruiseState::exit(Decision& system) {
    std::cout << "Exiting NormalCruiseState" << std::endl;
}

// ObstacleAvoidState 实现
void ObstacleAvoidState::enter(Decision& system) {
    std::cout << "Entering ObstacleAvoidState" << std::endl;
}

void ObstacleAvoidState::execute(Decision& system, DecisionData& decision) {
    decision.state = "AVOID";
    decision.linear_speed = system.GetParams().slow_speed;
    decision.gait_type = system.GetParams().obstacle_gait;
    
    // 简单避障策略：向更空旷的一侧转向
    if (system.GetContext().left_min_dist > system.GetContext().right_min_dist) {
        decision.angular_speed = 0.5;  // 向左转
    } else {
        decision.angular_speed = -0.5; // 向右转
    }
    
    // 检查状态转换条件
    if (!system.GetContext().frontal_obstacle_detected) {
        system.ChangeState(std::make_shared<NormalCruiseState>());
    }
}

void ObstacleAvoidState::exit(Decision& system) {
    std::cout << "Exiting ObstacleAvoidState" << std::endl;
}

// CenterNavState 实现
void CenterNavState::enter(Decision& system) {
    std::cout << "Entering CenterNavState" << std::endl;
}

void CenterNavState::execute(Decision& system, DecisionData& decision) {
    decision.state = "CENTER_NAV";
    decision.linear_speed = system.GetParams().normal_speed * 0.8;
    decision.gait_type = system.GetParams().default_gait;
    
    // PID控制保持居中
    const float kp = 0.6;
    decision.angular_speed = kp * system.GetContext().corridor_offset;
    
    // 检查状态转换条件
    if (!system.GetContext().corridor_detected) {
        system.ChangeState(std::make_shared<NormalCruiseState>());
    }
}

void CenterNavState::exit(Decision& system) {
    std::cout << "Exiting CenterNavState" << std::endl;
}

// SlopeState 实现
void SlopeState::enter(Decision& system) {
    std::cout << "Entering SlopeState" << std::endl;
}

void SlopeState::execute(Decision& system, DecisionData& decision) {
    decision.state = "SLOPE";
    
    // 根据坡度调整速度
    float speed_factor = 1.0f - std::min(1.0, 
        system.GetContext().slope_angle / system.GetParams().slope_threshold);
    decision.linear_speed = system.GetParams().normal_speed * speed_factor;
    
    // 上坡和下坡采用不同步态
    if (system.GetContext().slope_direction == UP_SLOPE) {
        decision.gait_type = system.GetParams().stair_gait;
    } else {
        decision.gait_type = system.GetParams().default_gait;
    }
    
    // 检查状态转换条件
    if (!system.GetContext().slope_detected) {
        system.ChangeState(std::make_shared<NormalCruiseState>());
    }
}

void SlopeState::exit(Decision& system) {
    std::cout << "Exiting SlopeState" << std::endl;
}

// StairState 实现
void StairState::enter(Decision& system) {
    std::cout << "Entering StairState" << std::endl;
}

void StairState::execute(Decision& system, DecisionData& decision) {
    decision.state = "STAIR";
    decision.linear_speed = system.GetParams().stair_speed;
    decision.gait_type = system.GetParams().stair_gait;
    
    // 检查状态转换条件
    if (!system.GetContext().stairs_detected) {
        system.ChangeState(std::make_shared<NormalCruiseState>());
    }
}

void StairState::exit(Decision& system) {
    std::cout << "Exiting StairState" << std::endl;
}

// RoughTerrainState 实现
void RoughTerrainState::enter(Decision& system) {
    std::cout << "Entering RoughTerrainState" << std::endl;
}

void RoughTerrainState::execute(Decision& system, DecisionData& decision) {
    decision.state = "ROUGH_TERRAIN";
    
    // 根据粗糙度调整速度
    float roughness_factor = 1.0f - std::min(1.0,
        system.GetContext().terrain_roughness / system.GetParams().rough_terrain_threshold);
    decision.linear_speed = system.GetParams().normal_speed * roughness_factor;
    decision.gait_type = system.GetParams().obstacle_gait;
    
    // 检查状态转换条件
    if (!system.GetContext().rough_terrain_detected) {
        system.ChangeState(std::make_shared<NormalCruiseState>());
    }
}

void RoughTerrainState::exit(Decision& system) {
    std::cout << "Exiting RoughTerrainState" << std::endl;
}


}// namespace decision