#include "subscriber/twist_data_subscriber_ros1.h"

#if COMMUNICATION_TYPE == ROS1
namespace communication::ros1 {

TwistDataSubscriberRos1::TwistDataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size)
    : TwistDataSubscriberBase(topic, max_buffer_size), nh_(nh) {
    subscriber_ = nh_.subscribe(topic, max_buffer_size, &TwistDataSubscriberRos1::TwistDataCallbackRos1, this);
}

void TwistDataSubscriberRos1::TwistDataCallbackRos1(const geometry_msgs::Twist::ConstPtr &msg) {
    TwistData twist_data;
    
    // Convert ROS message to internal data structure
    twist_data.time = ros::Time::now().toSec();
    twist_data.linear_vel.x() = msg->linear.x;    
    twist_data.linear_vel.y() = msg->linear.y;
    twist_data.linear_vel.z() = msg->linear.z;
    twist_data.angular_vel.x() = msg->angular.x;
    twist_data.angular_vel.y() = msg->angular.y;
    twist_data.angular_vel.z() = msg->angular.z;

    // Store the data in the buffer
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    data_buffer_.push_back(twist_data);
    
    // Maintain buffer size
    if (data_buffer_.size() > max_buffer_size_) {
        data_buffer_.pop_front();
    }

    // Call the callback function if it's set
  //  if (data_callback_) {
   //     data_callback_(twist_data);
   // }

}

}// namespace communication::ros1

#endif // COMMUNICATION_TYPE == ROS1
