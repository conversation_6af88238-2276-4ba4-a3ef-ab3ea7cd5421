#ifndef __DEPTH_POINTCLOUD__HH__H
#define __DEPTH_POINTCLOUD__HH__H


#include "common/utils.h"

namespace perception{

class DepthPointCloudConvertImpl;

class DepthPointCloudConvert{

public:
    DepthPointCloudConvert();
    ~DepthPointCloudConvert();

    void Init(const DepthPointCloudConvertParameter& parameter);
    bool Convert(cv::Mat depth,PointCloudT::Ptr& cloud);

protected:
    std::unique_ptr<DepthPointCloudConvertImpl>     depth_pointcloud_convert_impl_;

};

}   // namespace perception

#endif
