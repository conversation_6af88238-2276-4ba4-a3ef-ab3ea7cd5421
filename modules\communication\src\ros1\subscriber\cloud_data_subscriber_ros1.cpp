#include "subscriber/cloud_data_subscriber_ros1.h"
#include <pcl_conversions/pcl_conversions.h>

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1{

CloudDataSubscriberRos1::CloudDataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size)
            : CloudDataSubscriberBase(topic, max_buffer_size), nh_(nh) 
{
            subscriber_ = nh_.subscribe(topic, max_buffer_size, &CloudDataSubscriberRos1::CloudDataCallbackRos1, this);
}

//点云消息回调函数
void CloudDataSubscriberRos1::CloudDataCallbackRos1(const sensor_msgs::PointCloud2::ConstPtr &point_cloud_msg) 
{
 //将点云消息转换为CloudData类型 
  CloudData cloud_data;
  cloud_data.time = point_cloud_msg->header.stamp.toSec();
  // 使用PCL库将sensor_msgs::PointCloud2转换为pcl::PointCloud<pcl::PointXYZI>
  pcl::fromROSMsg(*point_cloud_msg, *cloud_data.cloud_ptr);
  // 检查点云数据是否为空
  if (cloud_data.cloud_ptr->empty()) {
    ROS_WARN("Received empty point cloud data.");
    return;
  }
  // 检查点云数据是否包含有效的点
  if (cloud_data.cloud_ptr->points.empty()) {
    ROS_WARN("Received point cloud data with no points.");
    return;
  }
  // 检查点云数据的时间戳是否有效
  if (cloud_data.time <= 0.0) {
    ROS_WARN("Received point cloud data with invalid timestamp: %f", cloud_data.time);
    return;
  }

  // 将转换后的CloudData存入缓冲区
  std::lock_guard<std::mutex> lock(buffer_mutex_);
  data_buffer_.push_back(cloud_data);
  // 检查缓冲区是否超过最大大小 
  if (data_buffer_.size() > max_buffer_size_) {
    data_buffer_.pop_front(); // 如果缓冲区已满，删除最旧的数据
  }
}

}   // namespace communication::ros1{

#endif
    