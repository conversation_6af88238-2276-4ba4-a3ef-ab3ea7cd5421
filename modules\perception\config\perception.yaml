module: perception
version: v1.0

yolo:
  model_path: $(find depth_cam_perception)/model/yolov8_seg_rk3588_i8.rknn
  label_names: $(find depth_cam_perception)/model/label_names.txt
  box_thresh: 0.4
  nms_thresh: 0.45

depth_cloud:
  max_depth: 2.0
  grid_size: 3

min_x: -0.6
max_x: 0.1
min_y: -0.3
max_y: 0.3
min_z: -0.5
max_z: 0.15


sensor: 

  color_image_topic: /camera/color/image_raw
  color_camera_info_topic: /camera/color/camera_info
  depth_image_topic: /camera/depth/image_rect_raw
  depth_camera_info_topic: /camera/depth/camera_info
  depth_to_color_topic: /camera/extrinsics/depth_to_color

  lidar: /lslidar_point_cloud
  imu: /imu
  gnss: /chattgps
  leg: /leg_odom

transform:

  extrinsic_T: [0.46, -0.06, 0.01]   #lidar在imu坐标系中的坐标
  extrinsic_R: [ 1, 0, 0,
                  0, 1, 0,
                  0, 0, 1]
  rtk2Lidar_T: [-0.05, -0.05, 0.15] #rtk天线在imu坐标系中的坐标


  color_to_lidar_q: [-0.55854,0.43934,-0.43418,0.5536]      #color相机在雷达坐标系下的朝向(四元素 w x y z)
  color_to_lidar_t: [0.06464,0.06732,-0.1364]               #color相机在雷达坐标系下的平移