#ifndef __IMAGE_SEGMENT__H_BASE_
#define __IMAGE_SEGMENT__H_BASE_

#include <string>
#include <opencv2/opencv.hpp>

namespace perception{

typedef struct {
    int left;
    int top;
    int right;
    int bottom;
}Box;


typedef struct{
    Box box;
    float prop;
    int cls_id;
}ObjectDetectResult;


typedef struct {
    cv::Mat mask;
    std::vector<ObjectDetectResult> boxes;
} SegmentDetectResult;

typedef 


class ImageSegmentImpl{
public:
    ImageSegmentImpl(){
    }
    virtual ~ImageSegmentImpl(){
    }
    virtual bool Init(const std::string& model_path)=0;
    virtual bool Inference(cv::Mat src,float box_thresh,float nms_thresh,SegmentDetectResult& out) =0;

    cv::Mat DebugImg(cv::Mat src,const SegmentDetectResult& segment){

        if(segment.boxes.size()==0){
            return src;
        }
        if( src.cols!=segment.mask.cols || src.rows!=segment.mask.rows){
            return src;
        }

        unsigned char class_colors[][3] = {
            {0, 0, 255},   // 'FF3838'
            {255, 0, 0}, // 'FF9D97'
            {0, 255, 0}  // 'FF701F'
        };

        const int N_CLASS_COLORS=3;
    
        const char* labels[3]={
            "stairs","incline","boundary"
        };
    
    
        //cv::Mat mask_color=cv::Mat::zeros(segment.mask.size(),CV_8UC3);
        cv::Mat debug_img=src.clone();
        char *img_data = (char*)debug_img.data;
        uint32_t width=debug_img.cols;
        uint32_t height=debug_img.rows;
        float alpha=0.6;

        for (int j = 0; j < height; j++)
        {
            for (int k = 0; k < width; k++)
            {
                int pixel_offset = 3 * (j * width + k);
                if(segment.mask.at<unsigned char>(j,k)!=0)
                {
                    int32_t color_id= (segment.mask.at<unsigned char>(j,k)-1) % N_CLASS_COLORS ;
                    img_data[pixel_offset + 2] = (unsigned char)clamp(class_colors[color_id][2] * (1 - alpha) + img_data[pixel_offset + 2] * alpha, 0, 255); // r
                    img_data[pixel_offset + 1] = (unsigned char)clamp(class_colors[color_id][1] * (1 - alpha) + img_data[pixel_offset + 1] * alpha, 0, 255); // g
                    img_data[pixel_offset + 0] = (unsigned char)clamp(class_colors[color_id][0] * (1 - alpha) + img_data[pixel_offset + 0] * alpha, 0, 255); // b
    
                    //mask_color.at<cv::Vec3b>(j,k)= cv::Vec3b(class_colors[color_id][0],class_colors[color_id][1],class_colors[color_id][2]);
                }
            }
        }
    
        for(int i=0;i<segment.boxes.size();++i){
            ObjectDetectResult det_result = segment.boxes[i];
    
            int x1 = det_result.box.left;
            int y1 = det_result.box.top;
            int x2 = det_result.box.right;
            int y2 = det_result.box.bottom;
    
            char text[32]={0};
            sprintf(text, "%s %.1f%%", labels[det_result.cls_id], det_result.prop * 100);
    
            //uint8_t *seg_mask = od_results.results_seg[0].seg_mask;
            cv::Scalar color(class_colors[det_result.cls_id%N_CLASS_COLORS][0],class_colors[det_result.cls_id%N_CLASS_COLORS][1],class_colors[det_result.cls_id%N_CLASS_COLORS][2],255);
    
            rectangle(debug_img, cv::Point(x1, y1), cv::Point(x2, y2), color, 2);
            putText(debug_img, text, cv::Point(x1, y1 - 6), cv::FONT_HERSHEY_DUPLEX, 0.7, cv::Scalar(255,255,255), 1, cv::LINE_AA);
        }
    
        return debug_img;
    }
protected:
    int clamp(float value, int min, int max){
        return value > min ? (value < max ? value : max) : min;
    }

};

}
#endif