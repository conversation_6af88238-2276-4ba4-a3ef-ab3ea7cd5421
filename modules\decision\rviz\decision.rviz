Panels:
  - Class: rviz/Displays
    Help Height: 0
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Global Options1
        - /mapping1
        - /mapping1/currPoints1/Autocompute Value Bounds1
        - /mapping1/PointCloud21
        - /Odometry1
        - /Odometry1/Odometry1
        - /Odometry1/Odometry1/Shape1
        - /Odometry1/Odometry1/Covariance1
        - /Odometry1/Odometry1/Covariance1/Position1
        - /Odometry1/Odometry1/Covariance1/Orientation1
        - /Path1
        - /PointCloud21
        - /PointCloud22
        - /PointCloud23
        - /PointCloud24
        - /PointCloud25
        - /PointCloud26
        - /PointCloud27
        - /PointCloud28
        - /PointCloud29
        - /PointCloud210
      Splitter Ratio: 0.6432291865348816
    Tree Height: 1102
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded:
      - /2D Pose Estimate1
      - /2D Nav Goal1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Name: Time
    SyncMode: 0
    SyncSource: ""
Preferences:
  PromptSaveOnExit: true
Toolbars:
  toolButtonStyle: 2
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 1
      Cell Size: 50
      Class: rviz/Grid
      Color: 160; 160; 164
      Enabled: false
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 40
      Reference Frame: <Fixed Frame>
      Value: false
    - Alpha: 1
      Class: rviz/Axes
      Enabled: false
      Length: 0.699999988079071
      Name: Axes
      Radius: 0.05999999865889549
      Reference Frame: <Fixed Frame>
      Show Trail: false
      Value: false
    - Class: rviz/Group
      Displays:
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 238; 238; 236
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 238; 238; 236
          Name: surround
          Position Transformer: XYZ
          Queue Size: 1
          Selectable: false
          Size (Pixels): 2
          Size (m): 0.05000000074505806
          Style: Points
          Topic: /cloud_registered
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Alpha: 0.10000000149011612
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 18.973955154418945
            Min Value: -1.5518549680709839
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: AxisColor
          Decay Time: 1000
          Enabled: false
          Invert Rainbow: true
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: currPoints
          Position Transformer: XYZ
          Queue Size: 100000
          Selectable: true
          Size (Pixels): 1
          Size (m): 0.009999999776482582
          Style: Points
          Topic: /cloud_registered
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 52.72899627685547
            Min Value: 1.8808441162109375
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 238; 238; 236
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: PointCloud2
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.10000000149011612
          Style: Flat Squares
          Topic: /Laser_map
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
      Enabled: false
      Name: mapping
    - Class: rviz/Group
      Displays:
        - Angle Tolerance: 0.009999999776482582
          Class: rviz/Odometry
          Covariance:
            Orientation:
              Alpha: 0.5
              Color: 255; 255; 127
              Color Style: Unique
              Frame: Local
              Offset: 1
              Scale: 1
              Value: true
            Position:
              Alpha: 0.30000001192092896
              Color: 204; 51; 204
              Scale: 1
              Value: true
            Value: true
          Enabled: true
          Keep: 1
          Name: Odometry
          Position Tolerance: 0.0010000000474974513
          Queue Size: 10
          Shape:
            Alpha: 1
            Axes Length: 1
            Axes Radius: 0.20000000298023224
            Color: 255; 85; 0
            Head Length: 0
            Head Radius: 0
            Shaft Length: 0.05000000074505806
            Shaft Radius: 0.05000000074505806
            Value: Axes
          Topic: /Odometry
          Unreliable: false
          Value: true
      Enabled: true
      Name: Odometry
    - Class: rviz/Group
      Displays:
        - Alpha: 0
          Buffer Length: 2
          Class: rviz/Path
          Color: 25; 255; 255
          Enabled: true
          Head Diameter: 0
          Head Length: 0
          Length: 0.30000001192092896
          Line Style: Billboards
          Line Width: 0.20000000298023224
          Name: path
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 25; 255; 255
          Pose Style: None
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.4000000059604645
          Shaft Length: 0.4000000059604645
          Topic: /path
          Unreliable: false
          Value: true
        - Alpha: 0
          Buffer Length: 2
          Class: rviz/Path
          Color: 239; 41; 41
          Enabled: true
          Head Diameter: 0
          Head Length: 0
          Length: 0.30000001192092896
          Line Style: Billboards
          Line Width: 0.20000000298023224
          Name: path_updata
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 25; 255; 255
          Pose Style: None
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.4000000059604645
          Shaft Length: 0.4000000059604645
          Topic: /s_fast_lio/path_update
          Unreliable: false
          Value: true
        - Alpha: 0
          Buffer Length: 2
          Class: rviz/Path
          Color: 138; 226; 52
          Enabled: true
          Head Diameter: 0
          Head Length: 0
          Length: 0.30000001192092896
          Line Style: Billboards
          Line Width: 0.20000000298023224
          Name: path_gnss
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 25; 255; 255
          Pose Style: None
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.4000000059604645
          Shaft Length: 0.4000000059604645
          Topic: /gnss_path
          Unreliable: false
          Value: true
      Enabled: true
      Name: Path
    - Alpha: 1
      Class: rviz/Axes
      Enabled: true
      Length: 0.699999988079071
      Name: Axes
      Radius: 0.10000000149011612
      Reference Frame: <Fixed Frame>
      Show Trail: false
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 13.139549255371094
        Min Value: -32.08251953125
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 138; 226; 52
      Color Transformer: FlatColor
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 138; 226; 52
      Min Color: 138; 226; 52
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 1
      Size (m): 0.10000000149011612
      Style: Points
      Topic: /Laser_map
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Alpha: 0.25
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 238; 238; 236
      Color Transformer: FlatColor
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 5
      Size (m): 0.10000000149011612
      Style: Points
      Topic: /globalmap
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 78; 154; 6
      Color Transformer: FlatColor
      Decay Time: 0
      Enabled: false
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 1
      Size (m): 0.009999999776482582
      Style: Points
      Topic: /icp_in
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: false
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 164; 0; 0
      Color Transformer: FlatColor
      Decay Time: 0
      Enabled: false
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.009999999776482582
      Style: Points
      Topic: /icp_out
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: false
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: FlatColor
      Decay Time: 0
      Enabled: false
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 1
      Size (m): 0.009999999776482582
      Style: Points
      Topic: /icp_target
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: false
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: Intensity
      Decay Time: 0
      Enabled: false
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.009999999776482582
      Style: Points
      Topic: /initCloud
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: false
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: Intensity
      Decay Time: 0
      Enabled: false
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.009999999776482582
      Style: Points
      Topic: /finalCloud
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: false
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: Intensity
      Decay Time: 0
      Enabled: false
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.009999999776482582
      Style: Points
      Topic: /surrouddingCloud
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: false
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 13.735304832458496
        Min Value: -1.234265685081482
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: AxisColor
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.009999999776482582
      Style: Points
      Topic: /cloud_registered
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: Intensity
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.009999999776482582
      Style: Flat Squares
      Topic: /lslidar_point_cloud
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
  Enabled: true
  Global Options:
    Background Color: 0; 0; 0
    Default Light: true
    Fixed Frame: map
    Frame Rate: 10
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/Select
    - Class: rviz/FocusCamera
    - Class: rviz/Measure
    - Class: rviz/SetInitialPose
      Theta std deviation: 0.2617993950843811
      Topic: /initialpose
      X std deviation: 0.5
      Y std deviation: 0.5
    - Class: rviz/SetGoal
      Topic: /move_base_simple/goal
    - Class: rviz/PublishPoint
      Single click: true
      Topic: /clicked_point
  Value: true
  Views:
    Current:
      Angle: 1.484999418258667
      Class: rviz/TopDownOrtho
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Scale: 10.641730308532715
      Target Frame: camera_init
      X: 38.74541091918945
      Y: 13.074407577514648
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 1335
  Hide Left Dock: false
  Hide Right Dock: true
  QMainWindow State: 000000ff00000000fd00000004000000000000018600000489fc020000000dfb0000001200530065006c0065006300740069006f006e00000001e10000009b0000005c00fffffffb0000001e0054006f006f006c002000500072006f007000650072007400690065007302000001ed000001df00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c006100790073010000003b00000489000000c700fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261fb0000000a0049006d0061006700650000000297000001dc0000000000000000fb0000000a0049006d0061006700650000000394000001600000000000000000fb0000000a0049006d00610067006501000002c5000000c70000000000000000fb0000000a0049006d00610067006501000002c5000000c70000000000000000fb0000000a0049006d00610067006501000002c5000000c70000000000000000000000010000015200000344fc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a00560069006500770073000000003b00000344000000a000fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e100000197000000030000086400000052fc0100000002fb0000000800540069006d00650100000000000008640000030700fffffffb0000000800540069006d00650100000000000004500000000000000000000006d80000048900000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: true
  Width: 2148
  X: -32
  Y: -32
