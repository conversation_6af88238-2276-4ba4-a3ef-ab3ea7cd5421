#include <memory>
#include "localization.h"
#include "common.h"


int main(int argc, char **argv) {

  std::string config_file_path = "";
  //cout << "Localization config file path: " << config_file_path << std::endl;
  InitConfig(config_file_path);
  auto localization_ptr = std::make_shared<localization::Localization>(config_file_path);
  localization_ptr->Start();
  // // 阻塞主线程，防止程序自动退出
  // while (true) {
  //   std::this_thread::sleep_for(std::chrono::seconds(1));
  // }

  return 0;
}
