<package>
    <name>costmap_2d</name>
    <version>1.14.2</version>
    <description>
        This package provides an implementation of a 2D costmap that takes in sensor
        data from the world, builds a 2D or 3D occupancy grid of the data (depending
        on whether a voxel based implementation is used), and inflates costs in a
        2D costmap based on the occupancy grid and a user specified inflation radius.
        This package also provides support for map_server based initialization of a
        costmap, rolling window based costmaps, and parameter based subscription to
        and configuration of sensor topics.
    </description>
    <author><PERSON><PERSON><PERSON></author>
    <author>David <PERSON>. <PERSON>!!</author>
    <author><PERSON></author>
    <author><EMAIL></author>
    <maintainer email="<EMAIL>">David <PERSON>. Lu!!</maintainer>
    <maintainer email="<EMAIL>"><PERSON></maintainer>
    <license>BSD</license>
    <url>http://wiki.ros.org/costmap_2d</url>

    <buildtool_depend>catkin</buildtool_depend>

    <build_depend>cmake_modules</build_depend>
    <build_depend>dynamic_reconfigure</build_depend>
    <build_depend>geometry_msgs</build_depend>
    <build_depend>laser_geometry</build_depend>
    <build_depend>map_msgs</build_depend>
    <build_depend>message_filters</build_depend>
    <build_depend>message_generation</build_depend>
    <build_depend>nav_msgs</build_depend>
    <build_depend>pcl_conversions</build_depend>
    <build_depend>pcl_ros</build_depend>
    <build_depend>pluginlib</build_depend>
    <build_depend>roscpp</build_depend>
    <build_depend>sensor_msgs</build_depend>
    <build_depend>std_msgs</build_depend>
    <build_depend>tf</build_depend>
    <build_depend>visualization_msgs</build_depend>
    <build_depend>voxel_grid</build_depend>

    <run_depend>dynamic_reconfigure</run_depend>
    <run_depend>geometry_msgs</run_depend>
    <run_depend>laser_geometry</run_depend>
    <run_depend>map_msgs</run_depend>
    <run_depend>message_filters</run_depend>
    <run_depend>message_runtime</run_depend>
    <run_depend>nav_msgs</run_depend>
    <run_depend>pcl_conversions</run_depend>
    <run_depend>pcl_ros</run_depend>
    <run_depend>pluginlib</run_depend>
    <run_depend>rosconsole</run_depend>
    <run_depend>roscpp</run_depend>
    <run_depend>sensor_msgs</run_depend>
    <run_depend>std_msgs</run_depend>
    <run_depend>tf</run_depend>
    <run_depend>visualization_msgs</run_depend>
    <run_depend>voxel_grid</run_depend>

    <export>
      <costmap_2d plugin="${prefix}/costmap_plugins.xml"/>
    </export>
</package>
