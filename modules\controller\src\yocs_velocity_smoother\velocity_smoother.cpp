/*****************************************************************************
 ** Includes
 *****************************************************************************/

#include "velocity_smoother.h"
#include <fstream>
#include <sstream>

using namespace yocs_velocity_smoother;

/*****************************************************************************
** Implementation
**********************/

VelocitySmoother::VelocitySmoother(const std::string& name)
: name(name)
, quiet(false)
, shutdown_req(false)
, input_active(false)
, pr_next(0)
, dynamic_reconfigure_server(nullptr)
, initialized_(false)
, running_(false)
, thread_running_(false)
{
    // 设置默认参数
    setDefaultConfiguration();
    
    std::cout << "VelocitySmoother 创建完成: " << name << std::endl;
}

void VelocitySmoother::reconfigCB(ParamsConfig &config, uint32_t level)
{
    if (!quiet) {
        std::cout << "重配置请求: speed_lim_v=" << config.speed_lim_v 
                  << ", speed_lim_w=" << config.speed_lim_w
                  << ", accel_lim_v=" << config.accel_lim_v 
                  << ", accel_lim_w=" << config.accel_lim_w
                  << ", decel_factor=" << config.decel_factor << std::endl;
    }

    locker.lock();
    speed_lim_v  = config.speed_lim_v;
    speed_lim_w  = config.speed_lim_w;
    accel_lim_v  = config.accel_lim_v;
    accel_lim_w  = config.accel_lim_w;
    decel_factor = config.decel_factor;
    decel_lim_v  = decel_factor * accel_lim_v;
    decel_lim_w  = decel_factor * accel_lim_w;
    locker.unlock();
}

void VelocitySmoother::velocityCB(const std::shared_ptr<const TwistMsg>& msg)
{
    // Estimate commands frequency; we do continuously as it can be very different depending on the
    // publisher type, and we don't want to impose extra constraints to keep this package flexible
    if (period_record.size() < PERIOD_RECORD_SIZE)
    {
        period_record.push_back((TimeStamp::now() - last_cb_time).toSec());
    }
    else
    {
        period_record[pr_next] = (TimeStamp::now() - last_cb_time).toSec();
    }
  
    pr_next++;
    pr_next %= period_record.size();
    last_cb_time = TimeStamp::now();

    if (period_record.size() <= PERIOD_RECORD_SIZE/2)
    {
        // wait until we have some values; make a reasonable assumption (10 Hz) meanwhile
        cb_avg_time = 0.1;
    }
    else
    {
        // enough; recalculate with the latest input
        cb_avg_time = median(period_record);
    }

    input_active = true;

    // Bound speed with the maximum values
    locker.lock();
    target_vel.linear.x  =
        msg->linear.x  > 0.0 ? std::min(msg->linear.x,  speed_lim_v) : std::max(msg->linear.x,  -speed_lim_v);
    target_vel.angular.z =
        msg->angular.z > 0.0 ? std::min(msg->angular.z, speed_lim_w) : std::max(msg->angular.z, -speed_lim_w);
    locker.unlock();

    // 如果设置了回调函数，则调用它
    if (velocity_callback_) {
      velocity_callback_(std::make_shared<TwistMsg>(target_vel));
   }
}

void VelocitySmoother::odometryCB(const std::shared_ptr<const OdometryMsg>& msg)
{
    if (robot_feedback == ODOMETRY)
        current_vel = msg->twist.twist;

    // ignore otherwise
}

void VelocitySmoother::robotVelCB(const std::shared_ptr<const TwistMsg>& msg)
{
    if (robot_feedback == COMMANDS)
        current_vel = *msg;

    // ignore otherwise
}

void VelocitySmoother::spin()
{
    double period = 1.0/frequency;
    auto sleep_duration = std::chrono::duration<double>(period);

    while (!shutdown_req && thread_running_)
    {
        locker.lock();
        double accel_lim_v_(accel_lim_v);
        double accel_lim_w_(accel_lim_w);
        double decel_factor_(decel_factor);
        double decel_lim_v_(decel_lim_v);
        double decel_lim_w_(decel_lim_w);
        locker.unlock();
        
        if ((input_active == true) && (cb_avg_time > 0.0) &&
            ((TimeStamp::now() - last_cb_time).toSec() > std::min(3.0*cb_avg_time, 0.5)))
        {
            // Velocity input no active anymore; normally last command is a zero-velocity one, but reassure
            // this, just in case something went wrong with our input, or he just forgot good manners...
            // Issue #2, extra check in case cb_avg_time is very big, for example with several atomic commands
            // The cb_avg_time > 0 check is required to deal with low-rate simulated time, that can make that
            // several messages arrive with the same time and so lead to a zero median
            input_active = false;
            if (IS_ZERO_VEOCITY(target_vel) == false)
            {
                if (!quiet) {
                    std::cerr << "Velocity Smoother : input got inactive leaving us a non-zero target velocity ("
                              << target_vel.linear.x << ", " << target_vel.angular.z << "), zeroing...[" << name << "]" << std::endl;
                }
                target_vel = ZERO_VEL_COMMAND;
            }
        }

        //check if the feedback is off from what we expect
        //don't care about min / max velocities here, just for rough checking
        double period_buffer = 2.0;

        double v_deviation_lower_bound = last_cmd_vel.linear.x - decel_lim_v_ * period * period_buffer;
        double v_deviation_upper_bound = last_cmd_vel.linear.x + accel_lim_v_ * period * period_buffer;

        double w_deviation_lower_bound = last_cmd_vel.angular.z - decel_lim_w_ * period * period_buffer;
        double angular_max_deviation = last_cmd_vel.angular.z + accel_lim_w_ * period * period_buffer;

        bool v_different_from_feedback = current_vel.linear.x < v_deviation_lower_bound || current_vel.linear.x > v_deviation_upper_bound;
        bool w_different_from_feedback = current_vel.angular.z < w_deviation_lower_bound || current_vel.angular.z > angular_max_deviation;

        if ((robot_feedback != NONE) && (input_active == true) && (cb_avg_time > 0.0) &&
            (((TimeStamp::now() - last_cb_time).toSec() > 5.0*cb_avg_time)     || // 5 missing msgs
                v_different_from_feedback || w_different_from_feedback))
        {
            // If the publisher has been inactive for a while, or if our current commanding differs a lot
            // from robot velocity feedback, we cannot trust the former; relay on robot's feedback instead
            // This might not work super well using the odometry if it has a high delay
            if (!quiet) {
                // this condition can be unavoidable due to preemption of current velocity control on
                // velocity multiplexer so be quiet if we're instructed to do so
                std::cerr << "Velocity Smoother : using robot velocity feedback " <<
                            std::string(robot_feedback == ODOMETRY ? "odometry" : "end commands") <<
                            " instead of last command: " <<
                            (TimeStamp::now() - last_cb_time).toSec() << ", " <<
                            current_vel.linear.x  - last_cmd_vel.linear.x << ", " <<
                            current_vel.angular.z - last_cmd_vel.angular.z << ", [" << name << "]" << std::endl;
            }
            last_cmd_vel = current_vel;
        }

        std::shared_ptr<TwistMsg> cmd_vel;

        if ((target_vel.linear.x  != last_cmd_vel.linear.x) ||
            (target_vel.angular.z != last_cmd_vel.angular.z))
        {
            // Try to reach target velocity ensuring that we don't exceed the acceleration limits
            cmd_vel = std::make_shared<TwistMsg>(target_vel);

            double v_inc, w_inc, max_v_inc, max_w_inc;

            v_inc = target_vel.linear.x - last_cmd_vel.linear.x;
            if ((robot_feedback == ODOMETRY) && (current_vel.linear.x*target_vel.linear.x < 0.0))
            {
                // countermarch (on robots with significant inertia; requires odometry feedback to be detected)
                max_v_inc = decel_lim_v_*period;
            }
            else
            {
                max_v_inc = ((v_inc*target_vel.linear.x > 0.0)?accel_lim_v_:decel_lim_v_)*period;
            }

            w_inc = target_vel.angular.z - last_cmd_vel.angular.z;
            if ((robot_feedback == ODOMETRY) && (current_vel.angular.z*target_vel.angular.z < 0.0))
            {
                // countermarch (on robots with significant inertia; requires odometry feedback to be detected)
                max_w_inc = decel_lim_w_*period;
            }
            else
            {
                max_w_inc = ((w_inc*target_vel.angular.z > 0.0)?accel_lim_w_:decel_lim_w_)*period;
            }

            // Calculate and normalise vectors A (desired velocity increment) and B (maximum velocity increment),
            // where v acts as coordinate x and w as coordinate y; the sign of the angle from A to B determines
            // which velocity (v or w) must be overconstrained to keep the direction provided as command
            double MA = sqrt(    v_inc *     v_inc +     w_inc *     w_inc);
            double MB = sqrt(max_v_inc * max_v_inc + max_w_inc * max_w_inc);

            double Av = std::abs(v_inc) / MA;
            double Aw = std::abs(w_inc) / MA;
            double Bv = max_v_inc / MB;
            double Bw = max_w_inc / MB;
            double theta = atan2(Bw, Bv) - atan2(Aw, Av);

            if (theta < 0)
            {
                // overconstrain linear velocity
                max_v_inc = (max_w_inc*std::abs(v_inc))/std::abs(w_inc);
            }
            else
            {
                // overconstrain angular velocity
                max_w_inc = (max_v_inc*std::abs(w_inc))/std::abs(v_inc);
            }

            if (std::abs(v_inc) > max_v_inc)
            {
                // we must limit linear velocity
                cmd_vel->linear.x  = last_cmd_vel.linear.x  + sign(v_inc)*max_v_inc;
            }

            if (std::abs(w_inc) > max_w_inc)
            {
                // we must limit angular velocity
                cmd_vel->angular.z = last_cmd_vel.angular.z + sign(w_inc)*max_w_inc;
            }

            // 使用回调函数发布平滑后的速度
            if (smooth_vel_publish_callback_) {
                smooth_vel_publish_callback_(cmd_vel);
            }
            last_cmd_vel = *cmd_vel;
        }
        else if (input_active == true)
        {
            // We already reached target velocity; just keep resending last command while input is active
            cmd_vel = std::make_shared<TwistMsg>(last_cmd_vel);
            // 使用回调函数发布当前速度
            if (smooth_vel_publish_callback_) {
                smooth_vel_publish_callback_(cmd_vel);
            }
        }

        std::this_thread::sleep_for(sleep_duration);
    }
}

/**
 * 初始化函数
 */
bool VelocitySmoother::init()
{
    if (initialized_) {
        std::cout << "VelocitySmoother 已经初始化" << std::endl;
        return true;
    }

    // 设置默认配置
    setDefaultConfiguration();

    // 初始化动态重配置服务器
    if (dynamic_reconfigure_server != nullptr) {
        delete dynamic_reconfigure_server;
    }
    dynamic_reconfigure_server = new DynamicReconfigureServer();
    dynamic_reconfigure_server->setCallback([this](ParamsConfig& config, uint32_t level) {
        reconfigCB(config, level);
    });

    // 应用配置
    applyConfiguration();

    // 验证参数
    validateParameters();

    // 打印状态
    printStatus();

    initialized_ = true;
    std::cout << "VelocitySmoother 初始化完成: " << name << std::endl;

    return true;
}

bool VelocitySmoother::initFromConfig(const std::string& config_file)
{
    if (!config_file.empty()) {
        if (!loadConfiguration(config_file)) {
            std::cout << "配置文件加载失败，使用默认配置: " << config_file << std::endl;
        }
    }
    
    return init();
}

// 回调接口实现
void VelocitySmoother::setVelocityCallback(std::function<void(const std::shared_ptr<TwistMsg>&)> callback)
{
    velocity_callback_ = callback;
}

void VelocitySmoother::setOdometryCallback(std::function<void(const std::shared_ptr<OdometryMsg>&)> callback)
{
    odometry_callback_ = callback;
}

void VelocitySmoother::setRobotVelCallback(std::function<void(const std::shared_ptr<TwistMsg>&)> callback)
{
    robot_vel_callback_ = callback;
}

void VelocitySmoother::setSmoothVelPublishCallback(std::function<void(const std::shared_ptr<TwistMsg>&)> callback)
{
    smooth_vel_publish_callback_ = callback;
}

// 数据输入接口实现
void VelocitySmoother::inputVelocity(const TwistMsg& msg)
{
    auto msg_ptr = std::make_shared<const TwistMsg>(msg);
    velocityCB(msg_ptr);
}

void VelocitySmoother::inputOdometry(const OdometryMsg& msg)
{
    auto msg_ptr = std::make_shared<const OdometryMsg>(msg);
    odometryCB(msg_ptr);
}

void VelocitySmoother::inputRobotVel(const TwistMsg& msg)
{
    auto msg_ptr = std::make_shared<const TwistMsg>(msg);
    robotVelCB(msg_ptr);
}

// 参数设置接口实现
void VelocitySmoother::setSpeedLimits(double speed_lim_v_, double speed_lim_w_)
{
    locker.lock();
    speed_lim_v = speed_lim_v_;
    speed_lim_w = speed_lim_w_;
    locker.unlock();

    if (!quiet) {
        std::cout << "设置速度限制: v=" << speed_lim_v << " m/s, w=" << speed_lim_w << " rad/s" << std::endl;
    }
}

void VelocitySmoother::setAccelLimits(double accel_lim_v_, double accel_lim_w_)
{
    locker.lock();
    accel_lim_v = accel_lim_v_;
    accel_lim_w = accel_lim_w_;
    // 更新减速限制
    decel_lim_v = decel_factor * accel_lim_v;
    decel_lim_w = decel_factor * accel_lim_w;
    locker.unlock();

    if (!quiet) {
        std::cout << "设置加速度限制: v=" << accel_lim_v << " m/s², w=" << accel_lim_w << " rad/s²" << std::endl;
    }
}

void VelocitySmoother::setDecelFactor(double decel_factor_)
{
    locker.lock();
    decel_factor = decel_factor_;
    decel_lim_v = decel_factor * accel_lim_v;
    decel_lim_w = decel_factor * accel_lim_w;
    locker.unlock();

    if (!quiet) {
        std::cout << "设置减速因子: " << decel_factor << std::endl;
    }
}

void VelocitySmoother::setFrequency(double frequency_)
{
    frequency = frequency_;
    if (!quiet) {
        std::cout << "设置频率: " << frequency << " Hz" << std::endl;
    }
}

void VelocitySmoother::setQuiet(bool quiet_)
{
    quiet = quiet_;
    std::cout << "静默模式: " << (quiet ? "启用" : "禁用") << std::endl;
}

void VelocitySmoother::setRobotFeedback(int feedback_type)
{
    if ((feedback_type < NONE) || (feedback_type > COMMANDS))
    {
        std::cerr << "无效的机器人反馈类型 (" << feedback_type
                  << ")。有效选项为 0 (NONE), 1 (ODOMETRY), 2 (COMMANDS)" << std::endl;
        feedback_type = NONE;
    }

    robot_feedback = static_cast<RobotFeedbackType>(feedback_type);

    if (!quiet) {
        std::string feedback_str;
        switch (robot_feedback) {
            case NONE: feedback_str = "NONE"; break;
            case ODOMETRY: feedback_str = "ODOMETRY"; break;
            case COMMANDS: feedback_str = "COMMANDS"; break;
        }
        std::cout << "设置机器人反馈类型: " << feedback_str << std::endl;
    }
}

// 运行控制实现
void VelocitySmoother::start()
{
    if (running_) {
        std::cout << "VelocitySmoother 已经在运行" << std::endl;
        return;
    }

    if (!initialized_) {
        std::cerr << "VelocitySmoother 未初始化，无法启动" << std::endl;
        return;
    }

    shutdown_req = false;
    thread_running_ = true;
    running_ = true;

    worker_thread_ = std::thread(&VelocitySmoother::spin, this);

    std::cout << "VelocitySmoother 已启动: " << name << std::endl;
}

void VelocitySmoother::stop()
{
    if (!running_) {
        return;
    }

    shutdown_req = true;
    thread_running_ = false;

    if (worker_thread_.joinable()) {
        worker_thread_.join();
    }

    running_ = false;
    std::cout << "VelocitySmoother 已停止: " << name << std::endl;
}

// 配置加载函数实现
bool VelocitySmoother::loadConfiguration(const std::string& config_file)
{
    std::cout << "加载配置文件: " << config_file << std::endl;

    // 检查文件扩展名
    if (config_file.find(".yaml") != std::string::npos ||
        config_file.find(".yml") != std::string::npos) {
        return loadConfigurationFromYAML(config_file);
    } else {
        return loadConfigurationFromText(config_file);
    }
}

bool VelocitySmoother::loadConfigurationFromYAML(const std::string& yaml_file)
{
#ifdef USE_YAML_CPP
    try {
        YAML::Node config = YAML::LoadFile(yaml_file);

        // 速度限制
        if (config["speed_lim_v"]) speed_lim_v = config["speed_lim_v"].as<double>();
        if (config["speed_lim_w"]) speed_lim_w = config["speed_lim_w"].as<double>();

        // 加速度限制
        if (config["accel_lim_v"]) accel_lim_v = config["accel_lim_v"].as<double>();
        if (config["accel_lim_w"]) accel_lim_w = config["accel_lim_w"].as<double>();

        // 其他参数
        if (config["decel_factor"]) decel_factor = config["decel_factor"].as<double>();
        if (config["frequency"]) frequency = config["frequency"].as<double>();
        if (config["quiet"]) quiet = config["quiet"].as<bool>();
        if (config["robot_feedback"]) {
            int feedback = config["robot_feedback"].as<int>();
            setRobotFeedback(feedback);
        }

        // 应用配置
        applyConfiguration();

        std::cout << "YAML配置文件加载成功: " << yaml_file << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "YAML配置文件加载失败: " << e.what() << std::endl;
        return false;
    }
#else
    std::cout << "未编译YAML支持，尝试使用文本格式解析" << std::endl;
    return loadConfigurationFromText(yaml_file);
#endif
}

bool VelocitySmoother::loadConfigurationFromText(const std::string& config_file)
{
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "无法打开配置文件: " << config_file << std::endl;
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        // 跳过注释和空行
        if (line.empty() || line[0] == '#') continue;

        // 简单的键值对解析
        size_t pos = line.find('=');
        if (pos != std::string::npos) {
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);

            // 去除空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            // 解析配置项
            if (key == "speed_lim_v") speed_lim_v = std::stod(value);
            else if (key == "speed_lim_w") speed_lim_w = std::stod(value);
            else if (key == "accel_lim_v") accel_lim_v = std::stod(value);
            else if (key == "accel_lim_w") accel_lim_w = std::stod(value);
            else if (key == "decel_factor") decel_factor = std::stod(value);
            else if (key == "frequency") frequency = std::stod(value);
            else if (key == "quiet") quiet = (value == "true");
            else if (key == "robot_feedback") setRobotFeedback(std::stoi(value));
        }
    }

    file.close();

    // 应用配置
    applyConfiguration();

    std::cout << "文本配置文件加载成功: " << config_file << std::endl;
    return true;
}

void VelocitySmoother::setDefaultConfiguration()
{
    // 设置默认参数值
    speed_lim_v = 1.0;
    speed_lim_w = 1.0;
    accel_lim_v = 1.0;
    accel_lim_w = 1.0;
    decel_factor = 1.0;
    frequency = 20.0;
    quiet = false;
    robot_feedback = NONE;

    applyConfiguration();
}

void VelocitySmoother::applyConfiguration()
{
    // 计算减速限制
    decel_lim_v = decel_factor * accel_lim_v;
    decel_lim_w = decel_factor * accel_lim_w;

    // 验证参数
    validateParameters();
}

void VelocitySmoother::validateParameters()
{
    bool valid = true;

    if (speed_lim_v <= 0.0 || speed_lim_w <= 0.0) {
        std::cerr << "错误: 速度限制必须为正数" << std::endl;
        valid = false;
    }

    if (accel_lim_v <= 0.0 || accel_lim_w <= 0.0) {
        std::cerr << "错误: 加速度限制必须为正数" << std::endl;
        valid = false;
    }

    if (decel_factor <= 0.0) {
        std::cerr << "错误: 减速因子必须为正数" << std::endl;
        valid = false;
    }

    if (frequency <= 0.0) {
        std::cerr << "错误: 频率必须为正数" << std::endl;
        valid = false;
    }

    if (!valid) {
        std::cerr << "参数验证失败，请检查配置" << std::endl;
    }
}

void VelocitySmoother::printStatus() const
{
    std::cout << "\n=== VelocitySmoother 状态 ===" << std::endl;
    std::cout << "名称: " << name << std::endl;
    std::cout << "初始化: " << (initialized_ ? "是" : "否") << std::endl;
    std::cout << "运行中: " << (running_ ? "是" : "否") << std::endl;
    std::cout << "静默模式: " << (quiet ? "是" : "否") << std::endl;
    std::cout << "输入活跃: " << (input_active ? "是" : "否") << std::endl;

    std::cout << "\n--- 参数配置 ---" << std::endl;
    std::cout << "速度限制: v=" << speed_lim_v << " m/s, w=" << speed_lim_w << " rad/s" << std::endl;
    std::cout << "加速度限制: v=" << accel_lim_v << " m/s², w=" << accel_lim_w << " rad/s²" << std::endl;
    std::cout << "减速度限制: v=" << decel_lim_v << " m/s², w=" << decel_lim_w << " rad/s²" << std::endl;
    std::cout << "减速因子: " << decel_factor << std::endl;
    std::cout << "频率: " << frequency << " Hz" << std::endl;

    std::string feedback_str;
    switch (robot_feedback) {
        case NONE: feedback_str = "NONE"; break;
        case ODOMETRY: feedback_str = "ODOMETRY"; break;
        case COMMANDS: feedback_str = "COMMANDS"; break;
    }
    std::cout << "机器人反馈: " << feedback_str << std::endl;

    std::cout << "\n--- 当前状态 ---" << std::endl;
    std::cout << "目标速度: v=" << target_vel.linear.x << " m/s, w=" << target_vel.angular.z << " rad/s" << std::endl;
    std::cout << "当前速度: v=" << current_vel.linear.x << " m/s, w=" << current_vel.angular.z << " rad/s" << std::endl;
    std::cout << "上次命令: v=" << last_cmd_vel.linear.x << " m/s, w=" << last_cmd_vel.angular.z << " rad/s" << std::endl;
    std::cout << "平均回调时间: " << cb_avg_time << " s" << std::endl;
    std::cout << "================================\n" << std::endl;
}

double VelocitySmoother::median(std::vector<double> values) {
    // Return the median element of an doubles vector
    nth_element(values.begin(), values.begin() + values.size()/2, values.end());
    return values[values.size()/2];
}
