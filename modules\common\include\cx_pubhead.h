﻿#if !defined(_CX_PUBHEAD_H_)
#define _CX_PUBHEAD_H_

#include <algorithm>
#include <deque>
#include <limits>
#include <list>
#include <map>
#include <set>
#include <string>
#include <vector>

using namespace std;

#include "cx_const.h"
#include "cx_debug.h"
#include "cx_platform.h"
#include "cx_types.h"
namespace common_lib {

#ifndef PI
#define PI (3.14159265358979323846f)
#endif

const cx_double LONGITUDE_MIN = 73.666667f;
const cx_double LONGITUDE_MAX = 135.041667f;
const cx_double LATITUDE_MIN = 3.866667f;
const cx_double LATITUDE_MAX = 53.550000f;
}  // namespace common_lib

#endif  // !defined(_CX_PUBHEAD_H_)
