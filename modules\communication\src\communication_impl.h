#pragma once

#include <string>
#include <memory>
// #include "enum_types.h"
#include "publisher_base.h"
#include "subscriber_base.h"
// #include "communication.h"

// #if COMMUNICATION_TYPE == ROS1
//     #include <ros/ros.h>
// #endif

namespace communication {

// class ImuDataSubscriberBase; // Forward declaration of IMU data subscriber class
// class GnssDataSubscriberBase; // Forward declaration of GNSS data subscriber class
// class LidarDataSubscriberBase; // Forward declaration of LiDAR data subscriber class
// class OdometrySubscriberBase; // Forward declaration of Odometry subscriber class

// // class IntDataPublisherBase; // Forward declaration of Int data publisher class

class CommunicationImpl
{
private:
    /* data */
public:
    CommunicationImpl(const std::string& module_name);
    virtual ~CommunicationImpl();

    virtual bool Initialize(const CommunicationType type);

    // Pure virtual function to run the communication implementation
    virtual void Run() = 0;

    virtual void RunOnce() = 0;

    virtual bool IsTerminated() const = 0;

    // 1. subscribers
    // 1.1 subscribers for sensor data, such as IMU, GNSS, LiDAR
    virtual std::shared_ptr<ImuDataSubscriberBase> CreateImuDataSubscriber(const std::string& topic) = 0;
    virtual std::shared_ptr<GnssDataSubscriberBase> CreateGnssDataSubscriber(const std::string& topic) = 0;
    virtual std::shared_ptr<LidarDataSubscriberBase> CreateLidarDataSubscriber(const std::string& topic) = 0;
    virtual std::shared_ptr<ImageDataSubscriberBase> CreateImageDataSubscriber(const std::string& topic,const std::string& pixel_type) = 0; // camera data subscriber

    // 1.2 subscribers for algorithm data
    virtual std::shared_ptr<OdometrySubscriberBase> CreateOdometrySubscriber(const std::string& topic) = 0;
    virtual std::shared_ptr<IntDataSubscriberBase> CreateIntDataSubscriber(const std::string &topic) = 0;
    virtual std::shared_ptr<DoubleDataSubscriberBase> CreateDoubleDataSubscriber(const std::string &topic) = 0;
    virtual std::shared_ptr<StringDataSubscriberBase> CreateStringDataSubscriber(const std::string &topic) = 0;
    virtual std::shared_ptr<BoolDataSubscriberBase> CreateBoolDataSubscriber(const std::string &topic) = 0;
    virtual std::shared_ptr<CloudDataSubscriberBase> CreateCloudDataSubscriber(const std::string &topic) = 0;
    virtual std::shared_ptr<PathDataSubscriberBase> CreatePathDataSubscriber(const std::string &topic) = 0;
    virtual std::shared_ptr<TwistDataSubscriberBase> CreateTwistDataSubscriber(const std::string &topic) = 0;
    virtual std::shared_ptr<PoseDataSubscriberBase> CreatePoseDataSubscriber(const std::string &topic) = 0;
    virtual std::shared_ptr<Int8DataSubscriberBase> CreateInt8DataSubscriber(const std::string &topic) = 0;

    virtual std::shared_ptr<CameraIntSubscriberBase> CreateCameraIntSubscriber(const std::string &topic) = 0; // Camera Intrinsics data subscriber
    virtual std::shared_ptr<CameraExtSubscriberBase> CreateCameraExtSubscriber(const std::string &topic) = 0; // camera Extrinsics data subscriber
    
    virtual std::shared_ptr<TFDataSubscriberBase> CreateTFDataSubscriber(const std::string& target_frame_id = "map", const std::string& source_frame_id = "body") = 0;
        
    // 2. publishers for algorithm data
    // 2. 1Create publishers for simple data types (e.g., int, float, double)

    virtual std::shared_ptr<ImageDataPublisherBase> CreateImageDataPublisher(const std::string &topic,const std::string& pixel_type,
                                 size_t max_buffer_size=10) =0;
    virtual std::shared_ptr<OdometryPublisherBase> CreateOdometryPublisher(const std::string& topic,
                                const std::string& frame_id, const std::string& child_frame_id, size_t max_buffer_size = 10) = 0;
    virtual std::shared_ptr<IntDataPublisherBase> CreateIntDataPublisher(const std::string &topic, size_t max_buffer_size = 10) = 0;
    virtual std::shared_ptr<DoubleDataPublisherBase> CreateDoubleDataPublisher(const std::string &topic, size_t max_buffer_size = 10) = 0;
    virtual std::shared_ptr<StringDataPublisherBase> CreateStringDataPublisher(const std::string &topic, size_t max_buffer_size = 10) = 0;
    virtual std::shared_ptr<BoolDataPublisherBase> CreateBoolDataPublisher(const std::string &topic, size_t max_buffer_size = 10) = 0;
    virtual std::shared_ptr<CloudDataPublisherBase> CreateCloudDataPublisher(const std::string &topic, 
                                const std::string& frame_id = "map", size_t max_buffer_size = 10) = 0;
    virtual std::shared_ptr<PathDataPublisherBase> CreatePathDataPublisher(const std::string &topic,
                                const std::string& frame_id = "map", size_t max_buffer_size = 10) = 0;
    virtual std::shared_ptr<TwistDataPublisherBase> CreateTwistDataPublisher(const std::string &topic,
                                const std::string& frame_id = "map", size_t max_buffer_size = 10) = 0;
    virtual std::shared_ptr<PoseDataPublisherBase> CreatePoseDataPublisher(const std::string &topic, size_t max_buffer_size = 10) = 0;

    virtual std::shared_ptr<TFDataPublisherBase> CreateTFDataPublisher(const std::string& frame_id = "map", const std::string& child_frame_id = "body") = 0;
    virtual std::shared_ptr<Int8DataPublisherBase> CreateInt8DataPublisher(const std::string &topic, size_t max_buffer_size = 10) = 0;
    
    virtual std::shared_ptr<OccupancyGridPublisherBase> CreateMapPublisher(const std::string &topic, const std::string &frame_id = "map", size_t max_buffer_size = 10) = 0;

protected:

    std::string module_name_; // Name of the module using this communication implementation

    CommunicationType type_; // Type of communication (e.g., ROS1, ROS2, TCP)

private:


private:

};

}// namespace communication