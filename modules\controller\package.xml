<?xml version="1.0"?>
<package format="2">
  <name>controller</name>
  <version>1.0.0</version>
  <description>Controller module for mobile robot navigation and control</description>

  <maintainer email="<EMAIL>">Robot Team</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>

  <!-- 构建依赖 -->
  <build_depend>roscpp</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>tf2</build_depend>
  <build_depend>tf2_ros</build_depend>
  <build_depend>tf2_geometry_msgs</build_depend>
  <build_depend>rviz</build_depend>
  <build_depend>pcl_ros</build_depend>
  <build_depend>pcl_conversions</build_depend>
  <build_depend>eigen</build_depend>
  <build_depend>yaml-cpp</build_depend>

  <!-- 运行依赖 -->
  <exec_depend>roscpp</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>tf2</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>tf2_geometry_msgs</exec_depend>
  <exec_depend>rviz</exec_depend>
  <exec_depend>pcl_ros</exec_depend>
  <exec_depend>pcl_conversions</exec_depend>
  <exec_depend>eigen</exec_depend>
  <exec_depend>yaml-cpp</exec_depend>

  <!-- 导出标签 -->
  <export>
    <rviz plugin="${prefix}/rviz/controller.rviz"/>
  </export>
</package> 