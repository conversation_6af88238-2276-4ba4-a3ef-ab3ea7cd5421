#if COMMUNICATION_TYPE == ROS1

#include "publisher/tf_data_publisher_ros1.h"

namespace communication::ros1 {
TFDataPublisherRos1::TFDataPublisherRos1(ros::NodeHandle &nh, const std::string &frame_id, const std::string &child_frame_id)
    : TFDataPublisherBase(child_frame_id + "_" + frame_id + "_tf", 1), nh_(nh), frame_id_(frame_id), child_frame_id_(child_frame_id) {
    // publisher_ = nh_.advertise<geometry_msgs::TransformStamped>(frame_id, 10);

}

} // namespace communication::ros1

#endif // COMMUNICATION_TYPE == ROS1