/**
 * @file pointcloud_data_subscriber_ros1.h
 * @brief This file contains the ROS1 implementation of the PointCloudDataSubscriber class.
 * * It subscribes to point cloud data from a specified ROS topic and processes the incoming messages.
 */
#pragma once

#if COMMUNICATION_TYPE == ROS1

#include <ros/ros.h>
// #include <deque>
// #include <mutex>
#include <sensor_msgs/PointCloud2.h>

#include "subscriber_base.h"

namespace communication::ros1{

class CloudDataSubscriberRos1 :  public CloudDataSubscriberBase{
    public:
        CloudDataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size = 1);

        ~CloudDataSubscriberRos1() = default;

        void CloudDataCallbackRos1(const sensor_msgs::PointCloud2::ConstPtr &point_cloud_msg);

    private:
        ros::NodeHandle& nh_;
        ros::Subscriber subscriber_;
    };

} // namespace communication::ros1{

#endif