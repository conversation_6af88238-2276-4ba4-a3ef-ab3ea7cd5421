#pragma once

#include "core/publisher_base_ros2.h"
#include "data_types/pose_data.h" // 确保包含PoseVelData定义
#include <nav_msgs/msg/odometry.hpp>

namespace communication::ros2
{

    class OdometryDataPublisherRos2 : public PublisherBaseRos2<PoseVelData, nav_msgs::msg::Odometry>
    {
    public:
        OdometryDataPublisherRos2(rclcpp::Node::SharedPtr node,
                                  const std::string &topic,
                                  const std::string &frame_id = "map",
                                  const std::string &child_frame_id = "body",
                                  size_t max_buffer_size = 10)
            : PublisherBaseRos2<PoseVelData, nav_msgs::msg::Odometry>(node, topic, max_buffer_size),
              frame_id_(frame_id),
              child_frame_id_(child_frame_id)
        {
            // 初始化msg_的header
            this->msg_.header.frame_id = frame_id_;
            this->msg_.child_frame_id = child_frame_id_;
        }

        virtual ~OdometryDataPublisherRos2() = default;

    protected:
        virtual void ToMsg() override
        {
            // 设置时间戳
            auto stamp = rclcpp::Time(static_cast<int64_t>(this->data_.pose_data.time * 1e9)); // 转换为纳秒
            this->msg_.header.stamp = stamp;

            // 设置坐标系ID
            this->msg_.header.frame_id = frame_id_;
            this->msg_.child_frame_id = child_frame_id_;

            // 位置和方向
            this->msg_.pose.pose.position.x = this->data_.pose_data.position[0];
            this->msg_.pose.pose.position.y = this->data_.pose_data.position[1];
            this->msg_.pose.pose.position.z = this->data_.pose_data.position[2];

            this->msg_.pose.pose.orientation.x = this->data_.pose_data.orientation[0];
            this->msg_.pose.pose.orientation.y = this->data_.pose_data.orientation[1];
            this->msg_.pose.pose.orientation.z = this->data_.pose_data.orientation[2];
            this->msg_.pose.pose.orientation.w = this->data_.pose_data.orientation[3];

            // 线速度和角速度
            this->msg_.twist.twist.linear.x = this->data_.vel[0];
            this->msg_.twist.twist.linear.y = this->data_.vel[1];
            this->msg_.twist.twist.linear.z = this->data_.vel[2];

            this->msg_.twist.twist.angular.x = this->data_.angle_vel[0];
            this->msg_.twist.twist.angular.y = this->data_.angle_vel[1];
            this->msg_.twist.twist.angular.z = this->data_.angle_vel[2];
        }

    private:
        std::string frame_id_;
        std::string child_frame_id_;
    };

} // namespace communication::ros2
