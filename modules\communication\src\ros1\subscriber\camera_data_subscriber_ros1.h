#pragma once

#if COMMUNICATION_TYPE == ROS1
#include <ros/ros.h>
#include <image_transport/image_transport.h>
#include <cv_bridge/cv_bridge.h>
#include <sensor_msgs/Image.h>
#include "subscriber_base.h"

namespace communication::ros1 {
class ImageDataSubscriberRos1 : public ImageDataSubscriberBase {
 public:
  ImageDataSubscriberRos1(image_transport::ImageTransport& it, const std::string &topic,const std::string& pixel_type="bgr8",
                                size_t max_buffer_size = 10) : ImageDataSubscriberBase(topic, max_buffer_size), 
                                pixel_type_(pixel_type) {
    subscriber_ =it.subscribe(topic, max_buffer_size, &ImageDataSubscriberRos1::ImageDataCallbackRos1, this);
  }

  ~ImageDataSubscriberRos1() = default;

  void ImageDataCallbackRos1(const sensor_msgs::Image::ConstPtr &image_msg){
    // Convert the ROS message to cv::Mat and store it in the buffer

    ImageData image_data;
    try {
      image_data.image = cv_bridge::toCvCopy(image_msg, pixel_type_)->image;
    } catch (const cv_bridge::Exception &e) {
      ROS_ERROR("cv_bridge exception: %s", e.what());
      return;
    }

    image_data.time = image_msg->header.stamp.toSec();
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    data_buffer_.push_back(image_data);
    if (data_buffer_.size() > max_buffer_size_) {
      data_buffer_.pop_front(); // Remove oldest data if buffer exceeds max size
    }
  }

 private:
  std::string                                         pixel_type_;
  image_transport::Subscriber                         subscriber_;
};
} // namespace communication::ros1
#endif // COMMUNICATION_TYPE == ROS1