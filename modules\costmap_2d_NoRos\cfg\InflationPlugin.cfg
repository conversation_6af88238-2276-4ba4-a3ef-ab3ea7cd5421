#!/usr/bin/env python

from dynamic_reconfigure.parameter_generator_catkin import ParameterGenerator, bool_t, double_t

gen = ParameterGenerator()

gen.add("enabled", bool_t, 0, "Whether to apply this plugin or not", True)
gen.add("cost_scaling_factor", double_t, 0, "A scaling factor to apply to cost values during inflation.", 10, 0, 100)
gen.add("inflation_radius", double_t, 0, "The radius in meters to which the map inflates obstacle cost values.", 0.55, 0, 50)
gen.add("inflate_unknown", bool_t, 0, "Whether to inflate unknown cells.", False)

exit(gen.generate("costmap_2d", "costmap_2d", "InflationPlugin"))
