#include "communication.h"
#include "global_traj_generate.h"
#include <chrono>
#include <csignal>
#include <iostream>
#include <memory>
#include <thread>

using namespace global_trajec_generate;
// using namespace communication;

// === Communication对象和订阅/发布器 ===
static bool g_running = true;
std::unique_ptr<GlobalTrajecGenerate> traj_generator_ptr;
std::shared_ptr<communication::OdometrySubscriberBase> odom_subscriber_ptr;
std::shared_ptr<communication::PathDataSubscriberBase> path_subscriber_ptr;
std::shared_ptr<communication::PoseDataSubscriberBase> goal_subscriber_ptr;
std::shared_ptr<communication::PoseDataSubscriberBase> webgoal_subscriber_ptr;
std::shared_ptr<communication::Int8DataSubscriberBase> stop_subscriber_ptr;
std::shared_ptr<communication::BoolDataSubscriberBase> mode_subscriber_ptr;
std::shared_ptr<communication::PoseDataPublisherBase> local_goal_publisher_ptr;
std::shared_ptr<communication::Int8DataPublisherBase> nav_result_publisher_ptr;

void signalHandler(int signum) {
  std::cout << "\n收到信号 " << signum << "，正在关闭全局轨迹生成器..."
            << std::endl;
  g_running = false;
  if (traj_generator_ptr) {
    traj_generator_ptr->stop();
  }
  std::cout << " 全局轨迹生成节点已安全退出" << std::endl;
}

// === 数据接收线程 ===
void DataReceiveThread() {
  while (g_running) {
    // 里程计输入
    if (!odom_subscriber_ptr->IsBufferEmpty()) {
      auto msg = odom_subscriber_ptr->GetBuffer();
      global_trajec_generate::OdometryData odom;
      odom.header.stamp = msg.front().pose_data.time;
      odom.pose.pose.position.x = msg.front().pose_data.position[0];
      odom.pose.pose.position.y = msg.front().pose_data.position[1];
      odom.pose.pose.position.z = msg.front().pose_data.position[2];
      odom.pose.pose.orientation.x = msg.front().pose_data.orientation[0];
      odom.pose.pose.orientation.y = msg.front().pose_data.orientation[1];
      odom.pose.pose.orientation.z = msg.front().pose_data.orientation[2];
      odom.pose.pose.orientation.w = msg.front().pose_data.orientation[3];
      //std::cout << "odom: " << odom.pose.pose.position.x << " " << odom.pose.pose.position.y << " " << odom.pose.pose.position.z << std::endl;
      traj_generator_ptr->inputOdometry(odom);
    }
    // 路径输入
    if (!path_subscriber_ptr->IsBufferEmpty()) {
      auto msg = path_subscriber_ptr->GetBuffer();
      global_trajec_generate::PathData path;
      path.header.stamp = msg.front().time_;
      path.poses_.clear();
      for (const auto &pose_data : msg.front().poses_) {
        global_trajec_generate::PoseStamped pt;
        pt.time = pose_data.time;
        pt.pose.position.x = pose_data.position[0];
        pt.pose.position.y = pose_data.position[1];
        pt.pose.position.z = pose_data.position[2];
        pt.pose.orientation.x = pose_data.orientation[0];
        pt.pose.orientation.y = pose_data.orientation[1];
        pt.pose.orientation.z = pose_data.orientation[2];
        pt.pose.orientation.w = pose_data.orientation[3];
        std::cout << "path: " << pt.pose.position.x << " " << pt.pose.position.y << " " << pt.pose.position.z << std::endl;
        path.poses_.push_back(pt);
      }

      traj_generator_ptr->inputGlobalPath(path);
    }
    // 目标点输入
    if (goal_subscriber_ptr && !goal_subscriber_ptr->IsBufferEmpty()) {
      auto msg = goal_subscriber_ptr->GetBuffer();

      // 适配PoseData到PoseStamped
      global_trajec_generate::PoseStamped goal;
      goal.pose.position.x = msg.front().position[0];
      goal.pose.position.y = msg.front().position[1];
      goal.pose.position.z = msg.front().position[2];
      goal.pose.orientation.x = msg.front().orientation[0];
      goal.pose.orientation.y = msg.front().orientation[1];
      goal.pose.orientation.z = msg.front().orientation[2];
      goal.pose.orientation.w = msg.front().orientation[3];
      traj_generator_ptr->inputGoalPose(goal);
    }
  }
  // Web目标点输入
  if (webgoal_subscriber_ptr && !webgoal_subscriber_ptr->IsBufferEmpty()) {
    auto msg = webgoal_subscriber_ptr->GetBuffer();
    if (!msg.empty()) {
      global_trajec_generate::PoseStamped webgoal;
      webgoal.pose.position.x = msg.front().position[0];
      webgoal.pose.position.y = msg.front().position[1];
      webgoal.pose.position.z = msg.front().position[2];
      webgoal.pose.orientation.x = msg.front().orientation[0];
      webgoal.pose.orientation.y = msg.front().orientation[1];
      webgoal.pose.orientation.z = msg.front().orientation[2];
      webgoal.pose.orientation.w = msg.front().orientation[3];
      traj_generator_ptr->inputWebGoalPose(webgoal);
    }
  }
  // 停止信号/模式信号可按需扩展
  std::this_thread::sleep_for(std::chrono::milliseconds(10));
}

// === 数据发布线程 ===
void DataSendThread() {
    while (true) {
   
        if (traj_generator_ptr && traj_generator_ptr->isRunning()) {
            auto local_goal_ptr = traj_generator_ptr->getCurrentLocalGoal();

            communication::PoseData msg;
            msg.time = std::chrono::duration_cast<std::chrono::seconds>(
                           std::chrono::system_clock::now().time_since_epoch())
                           .count();
            msg.position[0] = local_goal_ptr->pose.position.x;
            msg.position[1] = local_goal_ptr->pose.position.y;
            msg.position[2] = local_goal_ptr->pose.position.z;
            msg.orientation[0] = local_goal_ptr->pose.orientation.x;
            msg.orientation[1] = local_goal_ptr->pose.orientation.y;
            msg.orientation[2] = local_goal_ptr->pose.orientation.z;
            msg.orientation[3] = local_goal_ptr->pose.orientation.w;
            
            local_goal_publisher_ptr->Publish(msg);
            
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

/**
 * @brief 全局轨迹生成节点 - 基础节点创建和初始化
 */
int main(int argc, char **argv) {
  std::cout << "=== GlobalTrajecGenerate 通信集成节点程序 ===" << std::endl;
  std::signal(SIGINT, signalHandler);
  try {
    std::string config_file = "../config/global_traj_generate_config.yaml";
    // 创建通信模块实例
    auto communication_ptr =
        std::make_shared<communication::Communication>("global_traj_generate");
    if (!communication_ptr->Initialize(
            "../communication/config/communication_config.yaml")) {
      std::cerr << " 通信模块初始化失败" << std::endl;
      return -1;
    }
    // 创建全局轨迹生成器
    traj_generator_ptr = std::make_unique<GlobalTrajecGenerate>(config_file);
    // 创建订阅器
    odom_subscriber_ptr =
        communication_ptr->CreateOdometrySubscriber("/Odometry");
    path_subscriber_ptr =
        communication_ptr->CreatePathDataSubscriber("/rrt_star_planner/RRTstarPlannerROS/plan");
    goal_subscriber_ptr =
        communication_ptr->CreatePoseDataSubscriber("/move_base_simple/goal");
    webgoal_subscriber_ptr =
        communication_ptr->CreatePoseDataSubscriber("/web_goal_pose");
  
    local_goal_publisher_ptr =
        communication_ptr->CreatePoseDataPublisher("/local_goal", 10);

 
    // 初始化
    if (!traj_generator_ptr->init()) {
      std::cerr << "❌ 全局轨迹生成器初始化失败" << std::endl;
      return -1;
    }
    traj_generator_ptr->printStatus();
    traj_generator_ptr->start();
    std::cout << "\n✅ 全局轨迹生成节点启动完成" << std::endl;
    // 启动数据接收/发送线程
    std::thread data_receive_thread(DataReceiveThread);
    data_receive_thread.detach();
    std::thread data_send_thread(DataSendThread);
    data_send_thread.detach();
    // 阻塞主线程，运行通信模块
    communication_ptr->Run();
  } catch (const std::exception &e) {
    std::cerr << "❌ 程序执行出错: " << e.what() << std::endl;
    return -1;
  }
  return 0;
}
