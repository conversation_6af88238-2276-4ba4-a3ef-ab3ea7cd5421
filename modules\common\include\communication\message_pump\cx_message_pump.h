#ifndef CMMESSAGEPUMP_H
#define CMMESSAGEPUMP_H

#include "cx_message.h"
#include "thread/cx_rwlock.h"
namespace common_lib {
class CXMessagePump {
 public:
  CXMessagePump();
  virtual ~CXMessagePump();

 public:
  cx_int SendMessage(const CXMessage &msg);
  cx_int PostMessage(const CXMessage &msg);

  cx_int GetMessage(CXMessage &msg);
  cx_int PeekMessage(CXMessage &msg);

  cx_int GetBackMessage(CXMessage &msg);

  cx_int DestroyMessage(CXMessage &msg);

 private:
  CXSharedData<CXMessageDeque> messages_deq_;
};

CXMessagePump *GetSingleton4MessagePump();

cx_int PostMessage(const CXMessage &msg);
cx_int GetMessage(CXMessage &msg);
}  // namespace common_lib
#endif  // CMMESSAGEPUMP_H
