# ROS2 communication implementation
cmake_minimum_required(VERSION 3.5)
project(ros2_comm)

# Find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(pcl_conversions REQUIRED)

message(STATUS "nav_msgs_INCLUDE_DIRS: ${nav_msgs_INCLUDE_DIRS}")

add_library(${PROJECT_NAME} SHARED
    core/publisher_base_ros2.h
    core/subscriber_base_ros2.h
    communication_impl_ros2.h
    communication_impl_ros2.cpp

    subscriber/simple_data_subscriber_ros2.h
    subscriber/path_data_subscriber_ros2.h
    subscriber/pose_data_subscriber_ros2.h
    subscriber/odometry_data_subscriber_ros2.h
    subscriber/cloud_data_subscriber_ros2.h
    subscriber/lidar_data_subscriber_ros2.h
    subscriber/gnss_data_subscriber_ros2.h
    subscriber/imu_data_subscriber_ros2.h
    subscriber/camera_data_subscriber_ros2.h
    subscriber/camera_int_subscriber_ros2.h
    subscriber/camera_ext_subscriber_ros2.h
    subscriber/tf_data_subscriber_ros2.h

    publisher/simple_data_publisher_ros2.h
    publisher/path_data_publisher_ros2.h
    publisher/pose_data_publisher_ros2.h
    publisher/cloud_data_publisher_ros2.h
    publisher/camera_data_publisher_ros2.h
    publisher/tf_data_publisher_ros2.h
    publisher/odometry_data_publisher_ros2.h
)

# Link dependencies
# target_include_directories(${PROJECT_NAME}
#     PRIVATE
#         ${CMAKE_CURRENT_SOURCE_DIR}/../include
#         ${CMAKE_CURRENT_SOURCE_DIR}/../../include
#         ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include
# )

link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../../../common/lib
)

# 使用ament处理ROS2依赖
ament_target_dependencies(${PROJECT_NAME}
  rclcpp
  std_msgs
  geometry_msgs
  sensor_msgs
  nav_msgs
  tf2
  tf2_geometry_msgs
  cv_bridge
  pcl_conversions
)

# 链接非ROS2依赖项
target_link_libraries(${PROJECT_NAME}
  common_lib
)

# Add this library to the main communication core
# target_link_libraries(${PROJECT_NAME}_core ros2_comm)

# Install targets
install(TARGETS ${PROJECT_NAME}
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

install(DIRECTORY include/
  DESTINATION include
)

# Export dependencies
ament_export_dependencies(
  rclcpp
  std_msgs
  geometry_msgs
  sensor_msgs
  nav_msgs
  tf2
  tf2_geometry_msgs
  cv_bridge
  pcl_conversions
)

ament_package()
