<launch>
    <!-- 加载全局参数 -->
    <rosparam command="load" file="$(find robot_main)/config/robot_main_config.yaml" />
    
    <!-- 启动模块 -->
    <include file="$(find perception)/launch/perception.launch" />
    <include file="$(find mapping)/launch/mapping.launch" />
    
    <!-- 启动可视化 -->
    <!-- <node name="rviz" pkg="rviz" type="rviz" 
          args="-d $(find avp_system)/rviz/avp_display.rviz" /> -->
</launch>
