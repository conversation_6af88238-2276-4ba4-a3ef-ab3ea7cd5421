#pragma once

#include <cmath>
#include <stdexcept>
#include <string>
#include <limits>
#include <algorithm>
#include <chrono>

namespace calibration {

// 自定义clamp函数，用于C++14兼容性
template<typename T>
T clamp(T value, T min, T max) {
    return std::max(min, std::min(value, max));
}

/**
 * @brief PID控制器类，用于实现PID控制算法
 */
class PIDController {
public:
    /**
     * @brief 构造函数
     * @param kp 比例系数
     * @param ki 积分系数
     * @param kd 微分系数
     * @param output_limits 输出限制 [min, max]
     * @param integral_limits 积分限制 [min, max]
     */
    PIDController(double kp, double ki, double kd,
                 const std::pair<double, double>& output_limits = {-std::numeric_limits<double>::infinity(),
                                                                 std::numeric_limits<double>::infinity()},
                 const std::pair<double, double>& integral_limits = {-std::numeric_limits<double>::infinity(),
                                                                   std::numeric_limits<double>::infinity()})
        : kp_(kp), ki_(ki), kd_(kd),
          output_limits_(output_limits),
          integral_limits_(integral_limits),
          integral_(0.0),
          last_error_(0.0),
          last_time_(std::chrono::steady_clock::now()),
          is_first_run_(true) {
        if (kp < 0 || ki < 0 || kd < 0) {
            throw std::invalid_argument("PID gains must be non-negative");
        }
    }

    /**
     * @brief 计算PID控制输出
     * @param error 当前误差
     * @param dt 时间步长（可选，如果不提供则自动计算）
     * @return 控制输出
     */
    double compute(double error, double dt = 0.01) {
        auto current_time = std::chrono::steady_clock::now();
        double time_step;
        
        if (dt <= 0) {
            // 自动计算时间步长
            time_step = std::chrono::duration<double>(current_time - last_time_).count();
            if (time_step <= 0) {
                throw std::invalid_argument("Time step must be positive");
            }
        } else {
            time_step = dt;
        }

        // 积分项
        integral_ += error * time_step;
        
        // 限制积分项
        integral_ = clamp(integral_, integral_limits_.first, integral_limits_.second);

        // 微分项
        double derivative = 0.0;
        if (!is_first_run_) {
            derivative = (error - last_error_) / time_step;
        }

        // 计算PID输出
        double output = kp_ * error + ki_ * integral_ + kd_ * derivative;

        // 限制输出
        output = clamp(output, output_limits_.first, output_limits_.second);

        // 更新状态
        last_error_ = error;
        last_time_ = current_time;
        is_first_run_ = false;

        return output;
    }

    /**
     * @brief 重置控制器状态
     */
    void reset() {
        integral_ = 0.0;
        last_error_ = 0.0;
        last_time_ = std::chrono::steady_clock::now();
        is_first_run_ = true;
    }

    /**
     * @brief 设置PID参数
     * @param kp 比例系数
     * @param ki 积分系数
     * @param kd 微分系数
     */
    void setParameters(double kp, double ki, double kd) {
        if (kp < 0 || ki < 0 || kd < 0) {
            throw std::invalid_argument("PID gains must be non-negative");
        }
        kp_ = kp;
        ki_ = ki;
        kd_ = kd;
    }

    /**
     * @brief 设置输出限制
     * @param min 最小值
     * @param max 最大值
     */
    void setOutputLimits(double min, double max) {
        if (min > max) {
            throw std::invalid_argument("Output limits: min must be less than max");
        }
        output_limits_ = {min, max};
    }

    /**
     * @brief 设置积分限制
     * @param min 最小值
     * @param max 最大值
     */
    void setIntegralLimits(double min, double max) {
        if (min > max) {
            throw std::invalid_argument("Integral limits: min must be less than max");
        }
        integral_limits_ = {min, max};
    }

    /**
     * @brief 获取当前积分值
     * @return 积分值
     */
    double getIntegral() const { return integral_; }

    /**
     * @brief 获取上次误差
     * @return 上次误差
     */
    double getLastError() const { return last_error_; }

private:
    double kp_;  // 比例系数
    double ki_;  // 积分系数
    double kd_;  // 微分系数
    
    std::pair<double, double> output_limits_;    // 输出限制
    std::pair<double, double> integral_limits_;  // 积分限制
    
    double integral_;      // 积分项
    double last_error_;    // 上次误差
    std::chrono::steady_clock::time_point last_time_;     // 上次时间
    bool is_first_run_;    // 首次运行标志
};

} // namespace calibration