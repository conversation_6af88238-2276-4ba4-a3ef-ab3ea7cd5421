<?xml version="1.0"?>
<launch>
  <!-- 加载 全局参数 -->
  <rosparam command="load" file="$(find localization_node)/config/localization_config.yaml" />

  <!-- 启动可视化 rivi 文件 -->
  <!-- <node name="rviz_planning" pkg="rviz" type="rviz" args="-d $(find planning)/src/ros_viewer/planning.rviz"/> -->

  <!-- 启动节点 communication_node -->
  <node pkg="localization_node" type="localization_node"  name="localization_node" output="screen" /> 
  <!-- respawn="false",  -->

  <arg name="rviz" default="true"/>
  <group if="$(arg rviz)">
      <node name="rviz" pkg="rviz" type="rviz" args="-d $(find localization_node)/rviz/robot.rviz" />
  </group>


</launch>