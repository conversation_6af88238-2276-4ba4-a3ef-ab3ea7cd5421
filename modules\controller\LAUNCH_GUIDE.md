# Controller Module 启动指南

## 概述

本指南说明如何启动controller模块的calibration_node和velocity_smoother_node节点。

## 问题解决

### 原始问题
- 编译错误：CMake安装规则使用了未定义的ROS变量
- 启动错误：ROS无法找到controller包

### 解决方案
1. **修复了CMakeLists.txt**：使用标准CMake安装路径
2. **创建了多种启动方式**：适应不同的使用场景

## 启动方式

### 方式1: Bash脚本启动（推荐）

```bash
cd modules/controller
./scripts/start_controller.sh
```

**优点：**
- 自动设置环境变量
- 错误检查和验证
- 进程管理
- 不依赖ROS包系统

### 方式2: 手动启动

```bash
# 设置环境变量
export LD_LIBRARY_PATH="/home/<USER>/modules/controller/lib:/home/<USER>/modules/common/lib:/home/<USER>/modules/communication/lib:$LD_LIBRARY_PATH"

# 启动节点
cd modules/controller/bin
./calibration_node ../config/calibration.yaml &
./velocity_smoother_node ../config/velocity_smoother_config.yaml &
```

### 方式3: 使用绝对路径的launch文件

```bash
roslaunch /home/<USER>/modules/controller/launch/controller_simple.launch
```

### 方式4: 设置ROS包路径

```bash
export ROS_PACKAGE_PATH="/home/<USER>/modules/controller:$ROS_PACKAGE_PATH"
roslaunch controller controller.launch
```

## 验证启动成功

### 检查进程
```bash
ps aux | grep -E "(calibration_node|velocity_smoother_node)"
```

### 检查ROS节点
```bash
rosnode list
# 应该看到: /calibration, /velocity_smoother
```

### 检查ROS话题
```bash
rostopic list
# 应该看到相关话题
```

## 文件结构

```
modules/controller/
├── bin/                    # 可执行文件
│   ├── calibration_node
│   └── velocity_smoother_node
├── config/                 # 配置文件
│   ├── calibration.yaml
│   ├── velocity_smoother_config.yaml
│   └── communication_config.yaml
├── launch/                 # Launch文件
│   ├── controller.launch          # 标准launch文件
│   ├── controller_simple.launch   # 使用绝对路径
│   ├── controller_test.launch     # 测试模式
│   └── USAGE.md                   # 使用说明
├── rviz/                   # RViz配置
│   └── controller.rviz
├── scripts/                # 启动脚本
│   └── start_controller.sh # 推荐启动脚本
├── CMakeLists.txt          # 构建配置（已修复）
├── package.xml             # ROS包配置
└── LAUNCH_GUIDE.md         # 本文件
```

## 话题映射

### 输入话题
- `/Odometry` - 里程计数据
- `/move_base_simple/goal` - 导航目标
- `/web_goal` - Web界面目标
- `/calibration_mode` - 校准模式
- `/cmd_vel` - 速度命令
- `/twist/data` - 速度数据

### 输出话题
- `/cmd_vel_raw` - 原始速度命令
- `/cmd_vel_smooth` - 平滑后速度命令
- `/emergency_stop` - 紧急停止
- `/inner_emergency_stop` - 内部紧急停止
- `/calibration_mode_status` - 校准模式状态

## 故障排除

### 常见问题

1. **找不到可执行文件**
   ```bash
   ls -la modules/controller/bin/
   ```

2. **找不到配置文件**
   ```bash
   ls -la modules/controller/config/
   ```

3. **库文件路径问题**
   ```bash
   ls -la modules/controller/lib/
   ls -la modules/common/lib/
   ls -la modules/communication/lib/
   ```

4. **权限问题**
   ```bash
   chmod +x modules/controller/scripts/start_controller.sh
   ```

### 调试命令

```bash
# 查看节点日志
rosnode info /calibration
rosnode info /velocity_smoother

# 查看话题数据
rostopic echo /cmd_vel_smooth
rostopic echo /Odometry

# 检查话题频率
rostopic hz /cmd_vel_smooth
```

## 完整使用流程

```bash
# 1. 编译项目
cd /home/<USER>
./build.sh --p x86_64 --b release --c ros1 --n cpu

# 2. 启动controller节点（推荐方式）
cd modules/controller
./scripts/start_controller.sh

# 3. 验证启动成功
# 在另一个终端执行：
rostopic list
rosnode list

# 4. 停止节点
# 在启动脚本的终端按 Ctrl+C
# 或者：
pkill -f "calibration_node|velocity_smoother_node"
```

## 总结

- ✅ **编译问题已解决**：修复了CMakeLists.txt中的安装规则
- ✅ **启动问题已解决**：提供了多种启动方式
- ✅ **功能验证通过**：节点能正常启动并发布话题
- ✅ **文档完善**：提供了详细的使用说明

**推荐使用Bash脚本启动方式**，因为它最稳定且不依赖ROS包系统。 