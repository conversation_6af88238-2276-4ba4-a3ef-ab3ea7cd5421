#include "subscriber/pose_data_subscriber_ros1.h"

#if COMMUNICATION_TYPE == ROS1
namespace communication::ros1 {
PoseDataSubscriberRos1::PoseDataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size)
    : PoseDataSubscriberBase(topic, max_buffer_size), nh_(nh) {
    subscriber_ = nh_.subscribe(topic, max_buffer_size, &PoseDataSubscriberRos1::PoseDataCallbackRos1, this);
}
void PoseDataSubscriberRos1::PoseDataCallbackRos1(const geometry_msgs::PoseWithCovarianceStamped::ConstPtr &pose_msg) {
    PoseData pose_data;
    pose_data.time = pose_msg->header.stamp.toSec();
    
    // Convert position and orientation from geometry_msgs to PoseData format
    pose_data.position[0] = pose_msg->pose.pose.position.x;
    pose_data.position[1] = pose_msg->pose.pose.position.y;
    pose_data.position[2] = pose_msg->pose.pose.position.z;

    pose_data.orientation[0] = pose_msg->pose.pose.orientation.x;
    pose_data.orientation[1] = pose_msg->pose.pose.orientation.y;
    pose_data.orientation[2] = pose_msg->pose.pose.orientation.z;
    pose_data.orientation[3] = pose_msg->pose.pose.orientation.w;

    // Check if the time is valid
    if (pose_data.time <= 0.0) {
        ROS_WARN("Received invalid pose data.");
        return;
    }

    // Store the converted data in the buffer
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    data_buffer_.push_back(pose_data);
    
    // Check if the buffer exceeds the maximum size
    if (data_buffer_.size() > max_buffer_size_) {
        data_buffer_.pop_front(); // Remove the oldest data if the buffer is full
    }
}

} // namespace communication::ros1

#endif // COMMUNICATION_TYPE == ROS1