#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/nav_sat_fix.hpp>

#include "ros2/core/subscriber_base_ros2.h"
#include "data_types/gnss_data.h"

namespace communication::ros2
{

    class GNSSDataSubscriberRos2 : public SubscriberBaseRos2<GNSSData, sensor_msgs::msg::NavSatFix>
    {
    public:
        GNSSDataSubscriberRos2(rclcpp::Node::SharedPtr node,
                               const std::string &topic,
                               //    typename SubscriberBaseRos2<GNSSData, sensor_msgs::msg::NavSatFix>::CallbackType callback = nullptr,
                               size_t max_buffer_size = 10)
            : SubscriberBaseRos2<GNSSData, sensor_msgs::msg::NavSatFix>(node, topic, max_buffer_size)
        {
        }

    protected:
        virtual void FromMsg(const sensor_msgs::msg::NavSatFix &msg, GNSSData &data) override
        {
            data.time = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9;
            // data.frame_id = msg.header.frame_id;
            data.latitude = msg.latitude;
            data.longitude = msg.longitude;
            data.altitude = msg.altitude;
            data.rtk_status = static_cast<uint8_t>(msg.status.status);
            // data.service = static_cast<uint16_t>(msg.status.service);
        }
    };

} // namespace communication::ros2

#endif
