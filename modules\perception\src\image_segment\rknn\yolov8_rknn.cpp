#include "yolov8_rknn.h"

#include "dma_alloc.h"
#include "easy_timer.h"
#include <stdint.h>

Yolov8Rknn::Yolov8Rknn(){}
Yolov8Rknn::~Yolov8Rknn(){}
bool Yolov8Rknn::Init(std::string model_path){
    int32_t ret=-1;
    int32_t model_len = 0;
    rknn_context ctx = 0;

    // Load RKNN Model
    unsigned char* model = load_model(model_path.c_str(), &model_len);
    if (model == NULL)
    {
        printf("load_model fail!\n");
        return false;
    }

    // init RKNN
    ret = rknn_init(&ctx, model, model_len, 0, NULL);
    free(model);
    if (ret < 0)
    {
        printf("rknn_init fail! ret=%d\n", ret);
        return false;
    }

    // Get Model Input Output Number
    rknn_input_output_num io_num;
    ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
    if (ret != RKNN_SUCC)
    {
        printf("rknn_query fail! ret=%d\n", ret);
        return false;
    }
    printf("model input num: %d, output num: %d\n", io_num.n_input, io_num.n_output);

    // Get Model Input Info
    printf("input tensors:\n");
    rknn_tensor_attr input_attrs[io_num.n_input];
    memset(input_attrs, 0, sizeof(input_attrs));
    for (int i = 0; i < io_num.n_input; i++)
    {
        input_attrs[i].index = i;
        ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
        if (ret != RKNN_SUCC)
        {
            printf("rknn_query fail! ret=%d\n", ret);
            return false;
        }
        dump_tensor_attr(&(input_attrs[i]));
    }

    // Get Model Output Info
    printf("output tensors:\n");
    rknn_tensor_attr output_attrs[io_num.n_output];
    memset(output_attrs, 0, sizeof(output_attrs));
    for (int i = 0; i < io_num.n_output; i++)
    {
        output_attrs[i].index = i;
        ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
        if (ret != RKNN_SUCC)
        {
            printf("rknn_query fail! ret=%d\n", ret);
            return false;
        }
        dump_tensor_attr(&(output_attrs[i]));
    }

    // Set to context
    rknn_app_ctx_.rknn_ctx = ctx;

    // TODO
    if (output_attrs[0].qnt_type == RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC && output_attrs[0].type != RKNN_TENSOR_FLOAT16){
        rknn_app_ctx_.is_quant = true;
    }else{
        rknn_app_ctx_.is_quant = false;
    }

    rknn_app_ctx_.io_num = io_num;
    rknn_app_ctx_.input_attrs = (rknn_tensor_attr *)malloc(io_num.n_input * sizeof(rknn_tensor_attr));
    memcpy(rknn_app_ctx_.input_attrs, input_attrs, io_num.n_input * sizeof(rknn_tensor_attr));
    rknn_app_ctx_.output_attrs = (rknn_tensor_attr *)malloc(io_num.n_output * sizeof(rknn_tensor_attr));
    memcpy(rknn_app_ctx_.output_attrs, output_attrs, io_num.n_output * sizeof(rknn_tensor_attr));

    if (input_attrs[0].fmt == RKNN_TENSOR_NCHW)
    {
        printf("model is NCHW input fmt\n");
        rknn_app_ctx_.model_channel = input_attrs[0].dims[1];
        rknn_app_ctx_.model_height = input_attrs[0].dims[2];
        rknn_app_ctx_.model_width = input_attrs[0].dims[3];
    }
    else
    {
        printf("model is NHWC input fmt\n");
        rknn_app_ctx_.model_height = input_attrs[0].dims[1];
        rknn_app_ctx_.model_width = input_attrs[0].dims[2];
        rknn_app_ctx_.model_channel = input_attrs[0].dims[3];
    }
    printf("model input height=%d, width=%d, channel=%d\n",
        rknn_app_ctx_.model_height, rknn_app_ctx_.model_width, rknn_app_ctx_.model_channel);

    return true;
}


bool Yolov8Rknn::create_image_buffer(image_buffer_t* dst_img,int32_t width,int32_t height,image_format_t format){
    memset(dst_img, 0, sizeof(image_buffer_t));
    dst_img->width = width;
    dst_img->height = height;
    dst_img->format = format;
    dst_img->size = get_image_size(dst_img);

#if defined(DMA_ALLOC_DMA32)
    /*
     * Allocate dma_buf within 4G from dma32_heap,
     * return dma_fd and virtual address.
     */
    int ret = dma_buf_alloc(DMA_HEAP_DMA32_UNCACHE_PATCH, dst_img->size, &dst_img->fd, (void **)&dst_img->virt_addr);
    if (ret < 0) {
        printf("alloc dma32_heap buffer failed!\n");
        return false;
    }
#else
    dst_img->virt_addr = (unsigned char *)malloc(dst_img->size);
    if (dst_img->virt_addr == NULL)
    {
        printf("malloc buffer size:%d fail!\n", dst_img->size);
        return false;
    }

#endif
    return true;
}

void Yolov8Rknn::release_image_buffer(image_buffer_t dst_img){
    if (dst_img.virt_addr != NULL)
    {
        #if defined(DMA_ALLOC_DMA32)
        dma_buf_free(dst_img.size, &dst_img.fd, dst_img.virt_addr);
        #else
        free(dst_img.virt_addr);
        #endif
    }
}


bool Yolov8Rknn::rknn_inference(input_source src, object_detect_result_list* od_results) {

    image_buffer_t src_image;
    src_image.width  = src.image.cols;
    src_image.height = src.image.rows;
    src_image.format = IMAGE_FORMAT_RGB888;
    src_image.virt_addr = (unsigned char*)src.image.data;
    src_image.fd=0;

    image_buffer_t dst_img;
    letterbox_t letter_box;
    rknn_input inputs[rknn_app_ctx_.io_num.n_input];
    rknn_output outputs[rknn_app_ctx_.io_num.n_output];
    const float nms_threshold = src.nms_thresh;
    const float box_conf_threshold = src.box_thresh;
    int bg_color = 114; // pad color for letterbox

    if ( src.image.cols ==0 || src.image.rows==0 ){
        return false;
    }

    memset(&letter_box, 0, sizeof(letterbox_t));
    memset(inputs, 0, sizeof(inputs));
    memset(outputs, 0, sizeof(outputs));

    // Pre Process
    rknn_app_ctx_.input_image_width = src.image.cols;
    rknn_app_ctx_.input_image_height = src.image.rows;
    
    create_image_buffer(&dst_img,rknn_app_ctx_.model_width,rknn_app_ctx_.model_height,IMAGE_FORMAT_RGB888);

    // letterbox
    int32_t ret=-1;
    Timer::Evaluate([&,this](){
        ret = convert_image_with_letterbox(&src_image, &dst_img, &letter_box, bg_color);
    },"segment::convert_image_with_letterbox");
    if (ret < 0)
    {
        printf("convert_image_with_letterbox fail! ret=%d\n", ret);
        release_image_buffer(dst_img);
        return false;
    }

    // Set Input Data
    inputs[0].index = 0;
    inputs[0].type = RKNN_TENSOR_UINT8;
    inputs[0].fmt = RKNN_TENSOR_NHWC;
    inputs[0].size = rknn_app_ctx_.model_width * rknn_app_ctx_.model_height * rknn_app_ctx_.model_channel;
    inputs[0].buf = dst_img.virt_addr;

    Timer::Evaluate([&,this](){
        ret = rknn_inputs_set(rknn_app_ctx_.rknn_ctx, rknn_app_ctx_.io_num.n_input, inputs);
    },"segment::rknn_inputs_set");
    if (ret < 0)
    {
        printf("rknn_input_set fail! ret=%d\n", ret);
        release_image_buffer(dst_img);
        return false;
    }

    // Run
    //printf("rknn_run\n");
    Timer::Evaluate([&,this](){
        ret = rknn_run(rknn_app_ctx_.rknn_ctx, nullptr);
    },"segment::rknn_run");


    if (ret < 0)
    {
        printf("rknn_run fail! ret=%d\n", ret);
        release_image_buffer(dst_img);
        return false;
    }

    // Get Output
    memset(outputs, 0, sizeof(outputs));
    Timer::Evaluate([&,this](){
        
        for (int i = 0; i < rknn_app_ctx_.io_num.n_output; i++)
        {
            outputs[i].index = i;
            outputs[i].want_float = (!rknn_app_ctx_.is_quant);
        }
        ret = rknn_outputs_get(rknn_app_ctx_.rknn_ctx, rknn_app_ctx_.io_num.n_output, outputs, NULL);
    },"segment::get_out");

    if (ret < 0)
    {
        printf("rknn_outputs_get fail! ret=%d\n", ret);
        release_image_buffer(dst_img);
        return false;
    }
    // Post Process
    Timer::Evaluate([&](){
        post_process(&rknn_app_ctx_, outputs, &letter_box, box_conf_threshold, nms_threshold, od_results);
    },"segment::post_process");
    // Remeber to release rknn output
    rknn_outputs_release(rknn_app_ctx_.rknn_ctx, rknn_app_ctx_.io_num.n_output, outputs);

    release_image_buffer(dst_img);
    return true;
}

bool Yolov8Rknn::Seg(input_source src,segment_out& out) {
    object_detect_result_list od_results;
    if(rknn_inference(src,&od_results)){
        
        object_detect_result_list2segment_out(od_results,src.image.cols,src.image.rows,out);
        free(od_results.results_seg[0].seg_mask);  //
        return true;
    }else{
        out.mask=cv::Mat::zeros(cv::Size(src.image.cols,src.image.rows),CV_8UC1);
        return false;
    }
    
}
void Yolov8Rknn::Release(){
    if (rknn_app_ctx_.rknn_ctx != 0)
    {
        rknn_destroy(rknn_app_ctx_.rknn_ctx);
        rknn_app_ctx_.rknn_ctx = 0;
    }
    if (rknn_app_ctx_.input_attrs != NULL)
    {
        free(rknn_app_ctx_.input_attrs);
        rknn_app_ctx_.input_attrs = NULL;
    }
    if (rknn_app_ctx_.output_attrs != NULL)
    {
        free(rknn_app_ctx_.output_attrs);
        rknn_app_ctx_.output_attrs = NULL;
    }
}


void Yolov8Rknn::object_detect_result_list2segment_out(object_detect_result_list& od_results,
                                                        uint32_t width,uint32_t height,
                                                        segment_out& segment){
    cv::Mat mask_img=cv::Mat::zeros(cv::Size(width,height),CV_8UC1);
    if(od_results.count>=1){
        uint8_t *seg_mask = od_results.results_seg[0].seg_mask;
        memcpy(mask_img.data,seg_mask,width*height);
    }
    segment.mask=mask_img;

    for (int i = 0; i < od_results.count; i++)
    {
        segment.boxes.push_back(od_results.results[i]);
    }

}

cv::Mat Yolov8Rknn::debug_img(cv::Mat src,const segment_out& segment){

    if(segment.boxes.size()==0){
        return src.clone();
    }

    unsigned char class_colors[][3] = {
        {0, 0, 255},   // 'FF3838'
        {255, 0, 0}, // 'FF9D97'
        {0, 255, 0}  // 'FF701F'
    };

    const char* labels[3]={
        "stairs","incline","boundary"
    };


    //cv::Mat mask_color=cv::Mat::zeros(segment.mask.size(),CV_8UC3);
    cv::Mat debug_img=src.clone();
    char *img_data = (char*)debug_img.data;
    uint32_t width=debug_img.cols;
    uint32_t height=debug_img.rows;
    float alpha=0.6;

    for (int j = 0; j < height; j++)
    {
        for (int k = 0; k < width; k++)
        {
            int pixel_offset = 3 * (j * width + k);
            if(segment.mask.at<unsigned char>(j,k)!=0)
            {
                int32_t color_id= (segment.mask.at<unsigned char>(j,k)-1) % N_CLASS_COLORS ;
                img_data[pixel_offset + 2] = (unsigned char)clamp(class_colors[color_id][2] * (1 - alpha) + img_data[pixel_offset + 2] * alpha, 0, 255); // r
                img_data[pixel_offset + 1] = (unsigned char)clamp(class_colors[color_id][1] * (1 - alpha) + img_data[pixel_offset + 1] * alpha, 0, 255); // g
                img_data[pixel_offset + 0] = (unsigned char)clamp(class_colors[color_id][0] * (1 - alpha) + img_data[pixel_offset + 0] * alpha, 0, 255); // b

                //mask_color.at<cv::Vec3b>(j,k)= cv::Vec3b(class_colors[color_id][0],class_colors[color_id][1],class_colors[color_id][2]);
            }
        }
    }

    for(int i=0;i<segment.boxes.size();++i){
        object_detect_result det_result = segment.boxes[i];

        printf("%s @ (%d %d %d %d) %.3f\n", labels[det_result.cls_id],
               det_result.box.left, det_result.box.top,
               det_result.box.right, det_result.box.bottom,
               det_result.prop);
        int x1 = det_result.box.left;
        int y1 = det_result.box.top;
        int x2 = det_result.box.right;
        int y2 = det_result.box.bottom;

        char text[32]={0};
        sprintf(text, "%s %.1f%%", labels[det_result.cls_id], det_result.prop * 100);

        //uint8_t *seg_mask = od_results.results_seg[0].seg_mask;
        cv::Scalar color(class_colors[det_result.cls_id%N_CLASS_COLORS][0],class_colors[det_result.cls_id%N_CLASS_COLORS][1],class_colors[det_result.cls_id%N_CLASS_COLORS][2],255);

        rectangle(debug_img, cv::Point(x1, y1), cv::Point(x2, y2), color, 2);
        putText(debug_img, text, cv::Point(x1, y1 - 6), cv::FONT_HERSHEY_DUPLEX, 0.7, cv::Scalar(255,255,255), 1, cv::LINE_AA);
    }

    

    return debug_img;

}


unsigned char* Yolov8Rknn::load_model(const char* filename, int* model_size)
{
    FILE* fp = fopen(filename, "rb");
    if (fp == NULL) {
        printf("fopen %s fail!\n", filename);
        return NULL;
    }
    fseek(fp, 0, SEEK_END);
    int model_len = ftell(fp);
    unsigned char* model = (unsigned char*)malloc(model_len);
    fseek(fp, 0, SEEK_SET);
    if (model_len != fread(model, 1, model_len, fp)) {
        printf("fread %s fail!\n", filename);
        free(model);
        fclose(fp);
        return NULL;
    }
    *model_size = model_len;
    fclose(fp);
    return model;
}


void Yolov8Rknn::dump_tensor_attr(rknn_tensor_attr *attr)
{
    printf("  index=%d, name=%s, n_dims=%d, dims=[%d, %d, %d, %d], n_elems=%d, size=%d, fmt=%s, type=%s, qnt_type=%s, "
           "zp=%d, scale=%f\n",
           attr->index, attr->name, attr->n_dims, attr->dims[0], attr->dims[1], attr->dims[2], attr->dims[3],
           attr->n_elems, attr->size, get_format_string(attr->fmt), get_type_string(attr->type),
           get_qnt_type_string(attr->qnt_type), attr->zp, attr->scale);
}