# PointPublish Configuration File v1.0
# 点发布器配置文件
# 完全保留原有pointPublish.cpp的所有参数

# 基本参数配置
point_id: 0        # 点位ID，用于标识不同的导航点
point_info: 0      # 点位信息，附加的点位描述信息
gait: 0            # 步态模式，控制机器人的行走方式
speed: 1           # 速度等级，控制机器人的移动速度
manner: 0          # 行为方式，控制机器人的行为模式
obsmode: 1         # 避障模式，控制避障行为
navmode: 0         # 导航模式，控制导航策略

# 预设配置方案
presets:
  # 默认配置
  default:
    point_id: 0
    point_info: 0
    gait: 0
    speed: 1
    manner: 0
    obsmode: 1
    navmode: 0
    description: "默认配置，适用于一般导航任务"
  
  # 快速导航配置
  fast_navigation:
    point_id: 1
    point_info: 1
    gait: 2
    speed: 3
    manner: 0
    obsmode: 1
    navmode: 1
    description: "快速导航配置，适用于开阔环境"
  
  # 精确导航配置
  precise_navigation:
    point_id: 2
    point_info: 2
    gait: 1
    speed: 1
    manner: 1
    obsmode: 2
    navmode: 2
    description: "精确导航配置，适用于狭窄空间"
  
  # 慢速安全配置
  safe_navigation:
    point_id: 3
    point_info: 3
    gait: 0
    speed: 0
    manner: 2
    obsmode: 2
    navmode: 0
    description: "安全导航配置，适用于复杂环境"
  
  # 巡逻配置
  patrol_mode:
    point_id: 100
    point_info: 10
    gait: 1
    speed: 2
    manner: 0
    obsmode: 1
    navmode: 1
    description: "巡逻模式配置，适用于定期巡逻任务"
  
  # 搬运配置
  transport_mode:
    point_id: 200
    point_info: 20
    gait: 0
    speed: 1
    manner: 1
    obsmode: 2
    navmode: 2
    description: "搬运模式配置，适用于货物搬运任务"

# 参数说明和取值范围
parameter_info:
  point_id:
    description: "点位ID，用于唯一标识导航点"
    type: "int32"
    range: "0 - 9999"
    default: 0
    
  point_info:
    description: "点位信息，提供额外的点位描述"
    type: "int32"
    range: "0 - 99"
    default: 0
    
  gait:
    description: "步态模式，控制机器人行走方式"
    type: "int32"
    range: "0 - 10"
    values:
      0: "标准步态"
      1: "慢速步态"
      2: "快速步态"
      3: "爬行步态"
      4: "跳跃步态"
    default: 0
    
  speed:
    description: "速度等级，控制移动速度"
    type: "int32"
    range: "0 - 10"
    values:
      0: "极慢速"
      1: "慢速"
      2: "中速"
      3: "快速"
      4: "极快速"
    default: 1
    
  manner:
    description: "行为方式，控制机器人行为模式"
    type: "int32"
    range: "0 - 10"
    values:
      0: "正常模式"
      1: "谨慎模式"
      2: "激进模式"
    default: 0
    
  obsmode:
    description: "避障模式，控制避障策略"
    type: "int32"
    range: "0 - 10"
    values:
      0: "关闭避障"
      1: "基础避障"
      2: "高级避障"
      3: "智能避障"
    default: 1
    
  navmode:
    description: "导航模式，控制导航策略"
    type: "int32"
    range: "0 - 10"
    values:
      0: "点到点导航"
      1: "路径跟踪导航"
      2: "自主探索导航"
      3: "协作导航"
    default: 0

# 应用场景配置
scenarios:
  # 室内导航场景
  indoor_navigation:
    point_id: 1000
    point_info: 100
    gait: 1
    speed: 2
    manner: 1
    obsmode: 2
    navmode: 1
    description: "室内环境导航，中等速度，高避障"
    
  # 户外导航场景
  outdoor_navigation:
    point_id: 2000
    point_info: 200
    gait: 2
    speed: 3
    manner: 0
    obsmode: 1
    navmode: 1
    description: "户外环境导航，快速移动，基础避障"
    
  # 仓库作业场景
  warehouse_operation:
    point_id: 3000
    point_info: 300
    gait: 0
    speed: 1
    manner: 2
    obsmode: 2
    navmode: 2
    description: "仓库作业，精确定位，高安全性"
    
  # 服务机器人场景
  service_robot:
    point_id: 4000
    point_info: 400
    gait: 1
    speed: 1
    manner: 1
    obsmode: 2
    navmode: 0
    description: "服务机器人，平稳移动，人机友好"

# 调试和测试配置
debug:
  enable_logging: true
  log_level: "INFO"  # DEBUG, INFO, WARN, ERROR
  log_file: "point_publish.log"
  
test:
  enable_test_mode: false
  test_points:
    - {x: 0.0, y: 0.0, z: 0.0, yaw: 0.0}
    - {x: 1.0, y: 0.0, z: 0.0, yaw: 1.57}
    - {x: 1.0, y: 1.0, z: 0.0, yaw: 3.14}
    - {x: 0.0, y: 1.0, z: 0.0, yaw: -1.57}

# 版本信息
version:
  config_version: "1.0"
  compatible_versions: ["1.0", "1.1"]
  last_modified: "2024-01-01"
  author: "PointPublish NoROS Team"
  description: "去ROS化点发布器配置文件，完全保留原有功能"

# 注意事项
notes:
  - "所有参数都完全保留了原有pointPublish.cpp的功能"
  - "参数修改后需要重新加载配置或重启节点"
  - "建议在测试环境中验证参数配置的效果"
  - "不同场景下建议使用对应的预设配置"
  - "如需自定义配置，请参考parameter_info中的取值范围"
