#ifndef LOCAL_PLANNER_H
#define LOCAL_PLANNER_H

#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <mutex>
#include <chrono>
#include <iostream>
#include <fstream>
#include <thread>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/common/time.h>
#include <pcl/registration/icp.h>
#include <pcl/io/pcd_io.h>
#include <Eigen/Dense>
#include <yaml-cpp/yaml.h>

#include "common/common_types.h"

namespace local_planner {

const double PI = 3.1415926;

using planning_common::TimeStamp;
using planning_common::Header;
using planning_common::Point;
using planning_common::Vector3;
using planning_common::Quaternion;
using planning_common::Pose;
using planning_common::PoseStamped;
using planning_common::OdometryData;
using planning_common::PathData;
using planning_common::TwistData;
using planning_common::BoolMsg;
using planning_common::Int8Msg;




/**
 * @brief 路径点结构
 */
struct PathPoint {
    Point position;
    Quaternion orientation;
    
    PathPoint() {}
    PathPoint(double x, double y, double z) : position(x, y, z) {}
};


/**
 * @brief 点云数据结构
 */
struct PointCloud2Data {
    Header header;
    std::vector<uint8_t> data;
    uint32_t height;
    uint32_t width;
    
    PointCloud2Data() : height(0), width(0) {}
};




/**
 * @brief 多边形数据结构
 */
struct PolygonStamped {
    Header header;
    std::vector<Point> points;
    
    PolygonStamped() {}
};

/**
 * @brief 导航结果数据结构
 */
struct NavigationResult {
    Header header;
    int8_t result_code;
    std::string message;
    
    NavigationResult() : result_code(0) {}
};

/**
 * @brief 导航目标数据结构
 */
struct NavigationTarget {
    Header header;
    PoseStamped target_pose;
    int8_t nav_mode;
    int32_t point_id;
    int8_t point_info;
    int8_t speed;
    int8_t manner;
    int8_t obsmode;
    
    NavigationTarget() : nav_mode(0), point_id(0), point_info(0), speed(0), manner(0), obsmode(0) {}
};

/**
 * @brief 局部规划器配置参数结构体
 */
struct LocalPlannerConfig {
    // 路径文件夹
    std::string pathFolder;
    
    // 车辆参数
    double vehicleLength;
    double vehicleWidth;
    double sensorOffsetX;
    double sensorOffsetY;
    bool twoWayDrive;
    
    // 点云处理参数
    double laserVoxelSize;
    double terrainVoxelSize;
    bool useTerrainAnalysis;
    bool checkRotObstacle;
    double adjacentRange;
    double obstacleHeightThre;
    double groundHeightThre;
    double costHeightThre;
    double costScore;
    bool useCost;
    int pointPerPathThre;
    double minRelZ;
    double maxRelZ;
    
    // 路径规划参数
    double dirWeight;
    double dirThre;
    bool dirToVehicle;
    double pathScale;
    double minPathScale;
    double pathScaleStep;
    bool pathScaleBySpeed;
    double minPathRange;
    double pathRangeStep;
    bool pathRangeBySpeed;
    bool pathCropByGoal;
    double goalClearRange;
    double arrived_dis_threshold;
    
    // 速度参数
    double maxSpeed;
    
    // 网格参数
    float gridVoxelSize;
    float searchRadius;
    float gridVoxelOffsetX;
    float gridVoxelOffsetY;
    
    // 默认构造函数
    LocalPlannerConfig() {
        // 设置默认值
        pathFolder = "/home/<USER>/NR_Navigation/src/local_planner";
        vehicleLength = 1.2;
        vehicleWidth = 0.8;
        sensorOffsetX = 0.0;
        sensorOffsetY = 0.0;
        twoWayDrive = false;
        
        laserVoxelSize = 0.05;
        terrainVoxelSize = 0.2;
        useTerrainAnalysis = true;
        checkRotObstacle = false;
        adjacentRange = 4.25;
        obstacleHeightThre = 0.15;
        groundHeightThre = 0.1;
        costHeightThre = 0.1;
        costScore = 0.02;
        useCost = false;
        pointPerPathThre = 2;
        minRelZ = -0.8;
        maxRelZ = 0.25;
        
        dirWeight = 0.02;
        dirThre = 90.0;
        dirToVehicle = false;
        pathScale = 1.25;
        minPathScale = 0.75;
        pathScaleStep = 0.25;
        pathScaleBySpeed = true;
        minPathRange = 1.0;
        pathRangeStep = 0.5;
        pathRangeBySpeed = true;
        pathCropByGoal = true;
        goalClearRange = 0.5;
        arrived_dis_threshold = 0.2;
        
        maxSpeed = 0.8;
        
        gridVoxelSize = 0.02;
        searchRadius = 0.45;
        gridVoxelOffsetX = 3.2;
        gridVoxelOffsetY = 4.5;
    }
};

// 回调函数类型定义
using OdometryCallback = std::function<void(const std::shared_ptr<const OdometryData>&)>;
using PathCallback = std::function<void(const std::shared_ptr<const PathData>&)>;
using BoolCallback = std::function<void(const std::shared_ptr<const BoolMsg>&)>;
using Int8Callback = std::function<void(const std::shared_ptr<const Int8Msg>&)>;
using NavigationResultCallback = std::function<void(const std::shared_ptr<const NavigationResult>&)>;

/**
 * @brief 去ROS化的局部规划器类
 */
class LocalPlanner {
public:
    // 构造函数和析构函数
    LocalPlanner(const std::string& config_path = "");
    ~LocalPlanner();

    // 初始化函数
    bool initialize();
    bool loadConfig(const std::string& config_path);

    // 数据输入接口
    void updateOdometry(const planning_common::OdometryData& odom);
    void updateLaserCloud(const PointCloud2Data& cloud);
    void updateTerrainCloud(const PointCloud2Data& cloud);
    void updateGoal(const planning_common::PoseStamped& goal);
    void updateTarget(const planning_common::PoseStamped& target);
    void updateWebTarget(const planning_common::PoseStamped& target);
    void updateBoundary(const PolygonStamped& boundary);
    void updateAddedObstacles(const PointCloud2Data& obstacles);
    void updateLocationFailed(const planning_common::BoolMsg& msg);
    void updateCalibration(const planning_common::Int8Msg& calibration);
    void updatePath(const planning_common::PathData& path);
    void updateStop(const planning_common::Int8Msg& stop);

    // 输出回调设置
    void setModePublishCallback(std::function<void(const std::shared_ptr<const planning_common::BoolMsg>&)> callback);
    void setPathPublishCallback(std::function<void(const std::shared_ptr<const planning_common::PathData>&)> callback);
    void setStopPublishCallback(std::function<void(const std::shared_ptr<const planning_common::Int8Msg>&)> callback);
    void setInnerStopPublishCallback(std::function<void(const std::shared_ptr<const planning_common::Int8Msg>&)> callback);
    void setReplanPublishCallback(std::function<void(const std::shared_ptr<const planning_common::Int8Msg>&)> callback);
    void setNodeReadyPublishCallback(std::function<void(const std::shared_ptr<const planning_common::BoolMsg>&)> callback);
    void setFreePathsPublishCallback(std::function<void(const std::shared_ptr<PointCloud2Data>&)> callback);

    // 控制接口
    void startPlanning();
    void stopPlanning();
    void pausePlanning();
    void resumePlanning();

    // 参数设置
    void setMaxSpeed(double speed);
    void setVehicleDimensions(double length, double width);
    void setPathScale(double scale);
    void setAdjacentRange(double range);
    void setUseTerrainAnalysis(bool use);

    // 状态查询
    bool isInitialized() const;
    bool isRunning() const;
    bool isPaused() const;
    LocalPlannerConfig getConfig() const;
    void printStatus();

    // 主循环方法
    void controlLoop();
    void processOnce();

    // 主动获取当前发布数据的getter
    planning_common::BoolMsg getCurrentMode();
    planning_common::PathData getCurrentPath();
    planning_common::Int8Msg getCurrentStop();
    planning_common::Int8Msg getCurrentInnerStop();
    planning_common::Int8Msg getCurrentReplan();
    planning_common::BoolMsg getCurrentNodeReady();
    PointCloud2Data getCurrentFreePaths();

private:
    // 配置参数
    LocalPlannerConfig config_;
    std::string config_path_;

    // 运行状态
    bool initialized_;
    bool running_;
    bool paused_;
    std::mutex data_mutex_;

    // 回调函数
    BoolCallback mode_publish_callback_;
    PathCallback path_publish_callback_;
    Int8Callback stop_publish_callback_;
    Int8Callback inner_stop_publish_callback_;
    Int8Callback replan_publish_callback_;
    BoolCallback node_ready_publish_callback_;
    std::function<void(const std::shared_ptr<PointCloud2Data>&)> free_paths_publish_callback_;

    // 原始变量保留 (与原localPlanner.cpp中的全局变量对应)
    planning_common::BoolMsg arrive_inf;
    planning_common::BoolMsg adjustmode;
    int nav_start;
    int info;
    int id;
    double goalX, goalY, goalZ;
    double targetX, targetY, targetZ, targetYaw;
    float joyDir;
    bool newLaserCloud;
    bool newTerrainCloud;
    double odomTime;
    float vehicleRoll, vehiclePitch, vehicleYaw;
    float vehicleX, vehicleY, vehicleZ;
    bool init;
    double start_time, end_time;
    bool location_failed;

    // 路径相关常量和变量
    static const int pathNum = 343;
    static const int groupNum = 7;
    static const int gridVoxelNumX = 161;
    static const int gridVoxelNumY = 451;
    static const int gridVoxelNum = gridVoxelNumX * gridVoxelNumY;
    static const int laserCloudStackNum = 1;

    int laserCloudCount;
    int pathList[pathNum];
    Eigen::Vector3f endDirPosPathList[pathNum];
    int clearPathList[36 * pathNum];
    float pathPenaltyList[36 * pathNum];
    float clearPathPerGroupScore[36 * groupNum];
    std::vector<int> correspondences[gridVoxelNum];

    // PCL点云
    pcl::PointCloud<pcl::PointXYZI>::Ptr cloudKeyPoses3D;
    pcl::PointCloud<pcl::PointXYZI>::Ptr pointview;
    pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloud;
    pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloudCrop;
    pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloudDwz;
    pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloud;
    pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloudCrop;
    pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloudDwz;
    pcl::PointCloud<pcl::PointXYZI>::Ptr laserCloudStack[laserCloudStackNum];
    pcl::PointCloud<pcl::PointXYZI>::Ptr plannerCloud;
    pcl::PointCloud<pcl::PointXYZI>::Ptr plannerCloudCrop;
    pcl::PointCloud<pcl::PointXYZI>::Ptr boundaryCloud;
    pcl::PointCloud<pcl::PointXYZI>::Ptr addedObstacles;
    pcl::PointCloud<pcl::PointXYZ>::Ptr startPaths[groupNum];
    pcl::PointCloud<pcl::PointXYZI>::Ptr paths[pathNum];
    pcl::PointCloud<pcl::PointXYZI>::Ptr freePaths;

    // 滤波器
    pcl::VoxelGrid<pcl::PointXYZI> laserDwzFilter, terrainDwzFilter;

    // 私有方法 (保留原始函数功能)
    void odometryHandler(const std::shared_ptr<const OdometryData>& odom);
    void laserCloudHandler(const std::shared_ptr<const PointCloud2Data>& laserCloud2);
    void terrainCloudHandler(const std::shared_ptr<const PointCloud2Data>& terrainCloud2);
    void goalHandler(const std::shared_ptr<const PoseStamped>& goal);
    void targetHandler(const std::shared_ptr<const PoseStamped>& target);
    void webtargetHandler(const std::shared_ptr<const PoseStamped>& target);
    void boundaryHandler(const std::shared_ptr<const PolygonStamped>& boundary);
    void addedObstaclesHandler(const std::shared_ptr<const PointCloud2Data>& addedObstacles2);
    void locationFailedHandler(const std::shared_ptr<const BoolMsg>& msg);
    void calibrationHandler(const std::shared_ptr<const Int8Msg>& calibration);

    // 文件读取方法
    int readPlyHeader(FILE *filePtr);
    void readStartPaths();
    void readPaths();
    void readPathList();
    void readCorrespondences();

    // 工具方法
    double getCurrentTime();
    void initializePointClouds();
    void initializeFilters();
    bool loadYamlConfig(const std::string& config_path);

    // PCL点云转换方法
    void convertPointCloud2ToPCL(const PointCloud2Data& cloud2, pcl::PointCloud<pcl::PointXYZI>& pcl_cloud);
    void convertPCLToPointCloud2(const pcl::PointCloud<pcl::PointXYZI>& pcl_cloud, PointCloud2Data& cloud2);

    // 路径规划核心算法
    void performPathPlanning(float pathRange, float relativeGoalDis);

    planning_common::PathData last_path_data_;
    planning_common::Int8Msg last_stop_msg_;
    planning_common::Int8Msg last_inner_stop_msg_;
    planning_common::Int8Msg last_replan_msg_;
    planning_common::BoolMsg last_node_ready_msg_;
    PointCloud2Data last_free_paths_;
};

} // namespace local_planner

#endif // LOCAL_PLANNER_H
