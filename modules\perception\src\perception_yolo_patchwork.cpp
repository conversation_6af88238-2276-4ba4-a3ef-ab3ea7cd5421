
#include "perception_yolo_patchwork.h"
#include <pcl/io/pcd_io.h>
#include <pcl/common/transforms.h>

#include "config.h"
#include "image_segment/image_segment.h"

using namespace perception;


PerceptionYoloPatchwork::PerceptionYoloPatchwork(const std::string& config_file_path)
        :PerceptionImpl(config_file_path),
        lidar_cloud_(new PointCloudT),exit_(false){
    
    {
        //调试地面分割
        ObstacleDevideParameter     obstacle_devide_parameter;
        obstacle_devide_parameter.patchwork_param.enable_RNR=false;

        obstacle_devide_parameter.robot_box.min_x = -0.6;
        obstacle_devide_parameter.robot_box.max_x = 0.1;
        obstacle_devide_parameter.robot_box.min_y = -0.3;
        obstacle_devide_parameter.robot_box.max_y = 0.3;
        obstacle_devide_parameter.robot_box.min_z = -0.5;
        obstacle_devide_parameter.robot_box.max_z = 0.15;

        /*
        obstacle_devide_parameter.robot_box.min_x = Config::GetInstance().getParam<double>("min_x",0.0);
        obstacle_devide_parameter.robot_box.max_x = Config::GetInstance().getParam<double>("max_x",0.0);
        obstacle_devide_parameter.robot_box.min_y = Config::GetInstance().getParam<double>("min_y",0.0);
        obstacle_devide_parameter.robot_box.max_y = Config::GetInstance().getParam<double>("max_y",0.0);
        obstacle_devide_parameter.robot_box.min_z = Config::GetInstance().getParam<double>("min_z",0.0);
        obstacle_devide_parameter.robot_box.max_z = Config::GetInstance().getParam<double>("max_z",0.0);*/

        printf("parameter ----------------------    >>      min_x :%f \n",obstacle_devide_parameter.robot_box.min_x);
        printf("parameter ----------------------    >>      max_x :%f \n",obstacle_devide_parameter.robot_box.max_x);
        printf("parameter ----------------------    >>      min_y :%f \n",obstacle_devide_parameter.robot_box.min_y);
        printf("parameter ----------------------    >>      max_y :%f \n",obstacle_devide_parameter.robot_box.max_y);
        printf("parameter ----------------------    >>      min_z :%f \n",obstacle_devide_parameter.robot_box.min_z);
        printf("parameter ----------------------    >>      max_z :%f \n",obstacle_devide_parameter.robot_box.max_z);

        obstacle_devider_ = std::make_unique<ObstacleDevide>(obstacle_devide_parameter);
    }
    image_segment_ = std::make_unique<ImageSegment>();
    {
        //调试 yolo
        image_segment_->Init("/home/<USER>/cx-fusion-core-dc200/modules/perception/model/yolov8_seg_rk3588_i8.rknn");
    }

    max_depth_=10.0;

    {
        // 调试 点云投影
        reprejection_ = std::make_unique<Reprejection>();
        lidar2rgb_transform_.resize(7); //// tx,ty,tz , qx,qy,qz,qw
        lidar2rgb_transform_<< 0.06464,0.06732,-0.1364,
                                -0.55854,0.43934,-0.43418,0.5536 ;   

        /*double fx = 605.3957;
        double fy = 605.44909;
        double ppx = 325.02072;
        double ppy = 244.43301;

        Eigen::Vector4d K(fx,fy,ppx,ppy);
        reprejection_->Init(K,Eigen::VectorXd(),lidar2rgb_transform_);
        rgb_intrinsic_recieved_=true;*/
    }

    {
        depth_rknn_model_= "/home/<USER>/cx-fusion-core-dc200/modules/perception/model/depth_to_pointcloud_fixedsize.rknn";
        depth_pointcloud_convert_= std::make_unique<DepthPointCloudConvert>();
        
        depth_grid_=3;

        /*depth2rgb_transform_=Eigen::Matrix4d::Identity();
        depth2rgb_transform_.block<3,3>(0,0) <<0.9999366, -0.01084874, -0.00300235,
                                                0.01086695, 0.999922, 0.006116375, 
                                                0.002935761, -0.0061486, 0.9999768;

        depth2rgb_transform_.block<3,1>(0,3)<< 0.0148429991677, -6.113084964454174e-05, 0.00026961759431287646;

        std::cout<<"depth2rgb_transform_: \n"<<depth2rgb_transform_<<std::endl;
        rgb2depth_transform_recieved_=true;*/

    }

    thread_lidar_cloud_ = std::thread(&PerceptionYoloPatchwork::LidarCloudLoop,this);
    thread_yolo_ = std::thread(&PerceptionYoloPatchwork::YoloLoop,this);
    thread_depth_cloud_ = std::thread(&PerceptionYoloPatchwork::DepthCloudLoop,this);

}
PerceptionYoloPatchwork::~PerceptionYoloPatchwork(){
    exit_=true;
    lidar_arrived_signal_.NotifyOne();
    rgb_arrived_signal_.NotifyOne();
    depth_arrived_signal_.NotifyOne();
    thread_lidar_cloud_.join();
    thread_yolo_.join();
    thread_depth_cloud_.join();
}


void    PerceptionYoloPatchwork::PushCloud(PointCloudT::Ptr cloud){
    lidar_cloud_=cloud;
    lidar_arrived_signal_.NotifyOne();

}

void    PerceptionYoloPatchwork::PushRgbImage(cv::Mat rgb){
    timed_rgb_=rgb;
    rgb_arrived_signal_.NotifyOne();
}
void    PerceptionYoloPatchwork::PushDepthImage(cv::Mat depth){
    depth_image_=depth;
    depth_arrived_signal_.NotifyOne();
}

void    PerceptionYoloPatchwork::SetRgbIntrinsic(const CameraInfo& info){
    rgb_camera_info_=info;

    if( !rgb_intrinsic_recieved_ ){
        Eigen::Vector4d K(rgb_camera_info_.fx,rgb_camera_info_.fy,rgb_camera_info_.ppx,rgb_camera_info_.ppy);
        reprejection_->Init(K,Eigen::VectorXd(),lidar2rgb_transform_);
    }
    rgb_intrinsic_recieved_=true;
    

}
void    PerceptionYoloPatchwork::SetDepthIntrinsic(const CameraInfo& info){
    depth_camera_info_=info;
    depth_intrinsic_recieved_=true;

    if(rgb2depth_transform_recieved_ && depth_intrinsic_recieved_){
        
        Eigen::Quaterniond quaternion(lidar2rgb_transform_[6],lidar2rgb_transform_[3],lidar2rgb_transform_[4],lidar2rgb_transform_[5]);    
        Eigen::Matrix4d rgb2lidar = Eigen::Matrix4d::Identity();
        rgb2lidar.block<3,3>(0,0) = quaternion.toRotationMatrix();
        rgb2lidar.block<3,1>(0,3) = lidar2rgb_transform_.topRows(3);

        DepthPointCloudConvertParameter parameter;
        parameter.max_depth         = max_depth_;
        parameter.grid              = depth_grid_;
        parameter.camera_info = depth_camera_info_;
        parameter.transform         = (rgb2lidar*depth2rgb_transform_).cast<double>();
        parameter.cfg               = depth_rknn_model_;

        depth_pointcloud_convert_->Init(parameter);
    }
}

void    PerceptionYoloPatchwork::SetDepth2RgbTransform(const TransformExtrinsics& transform){

    depth2rgb_transform_=Eigen::Matrix4d::Identity();
    depth2rgb_transform_.block<3,3>(0,0) << transform.r00,transform.r01,transform.r02,
                                            transform.r10,transform.r11,transform.r12,
                                            transform.r20,transform.r21,transform.r22;

    depth2rgb_transform_.block<3,1>(0,3)<< transform.tx,transform.ty,transform.tz;
    rgb2depth_transform_recieved_=true;
    if(rgb2depth_transform_recieved_ && depth_intrinsic_recieved_){
        
        Eigen::Quaterniond quaternion(lidar2rgb_transform_[6],lidar2rgb_transform_[3],lidar2rgb_transform_[4],lidar2rgb_transform_[5]);    
        Eigen::Matrix4d rgb2lidar = Eigen::Matrix4d::Identity();
        rgb2lidar.block<3,3>(0,0) = quaternion.toRotationMatrix();
        rgb2lidar.block<3,1>(0,3) = lidar2rgb_transform_.topRows(3);

        DepthPointCloudConvertParameter parameter;
        parameter.max_depth         = max_depth_;
        parameter.grid              = depth_grid_;
        parameter.camera_info = depth_camera_info_;
        parameter.transform         = (rgb2lidar*depth2rgb_transform_).cast<double>();
        parameter.cfg               = depth_rknn_model_;

        depth_pointcloud_convert_->Init(parameter);
    }

}

void PerceptionYoloPatchwork::LidarCloudLoop(){
    while(!exit_){

        lidar_arrived_signal_.Wait();   //等待数据
        
        PointCloudT::Ptr cloudin(new PointCloudT);
        pcl::copyPointCloud(*lidar_cloud_,*cloudin);
        
        PerceptionOut output = LidarCloudSegment(cloudin);
        if(output_callback_){
            output_callback_(output);
        }
    }
}

void PerceptionYoloPatchwork::YoloLoop(){

    while( !exit_){
        rgb_arrived_signal_.Wait(); 
        cv::Mat color_image=timed_rgb_.get().clone();

        float box_thresh = 0.4;
        float nms_thresh = 0.45;
        SegmentDetectResult out;
        image_segment_->Inference(color_image,box_thresh,nms_thresh,out);
        reprejection_->ResetMask(out.mask,0.5);
        rgb_arrived_signal_.Reset();
    #ifdef OUTPUT_DEBUG
        output_.debug_info.mask=out.mask;
        output_.debug_info.yolo_segment_image = image_segment_->DebugImg(color_image,out);
    #endif
    }
}

void PerceptionYoloPatchwork::DepthCloudLoop(){

    while( !exit_){

        depth_arrived_signal_.Wait(); 
        cv::Mat depth_image=depth_image_.clone();

        PointCloudT::Ptr cloud(new PointCloudT);
        
        if(depth_pointcloud_convert_->Convert(depth_image,cloud)){
            timed_stereo_cloud_=cloud;
        #ifdef OUTPUT_DEBUG
            output_.debug_info.depth_cloud=cloud;
        #endif

        }
        
        depth_arrived_signal_.Reset();
    }

}

PerceptionOut    PerceptionYoloPatchwork::LidarCloudSegment(PointCloudT::Ptr cloudin){
    
    //添加相机点
    if( !timed_stereo_cloud_.timeout()){
        *cloudin += (*timed_stereo_cloud_.get());
    }
   
    // 1,分割点云
    PointCloudT::Ptr ground_cloud(new PointCloudT);
    PointCloudT::Ptr static_cloud(new PointCloudT);
    PointCloudT::Ptr dynamic_cloud(new PointCloudT);
    obstacle_devider_->devide_cloud(cloudin,ground_cloud,dynamic_cloud,static_cloud);

    PointCloudT::Ptr output_cloud(new PointCloudT);
    for(auto point: ground_cloud->points){
        point.intensity=0;
        output_cloud->push_back(point);
    }

    for(auto point: static_cloud->points){
        point.intensity=100;
        output_cloud->push_back(point);
    }
   
    //2, 反投影到图像

    if(!reprejection_->Timeout()  && rgb_intrinsic_recieved_ ){
        for(auto& pt : output_cloud->points){
            Eigen::Vector2d pixel;
            if(reprejection_->Preject(Eigen::Vector3d(pt.x,pt.y,pt.z),pixel)){
                int type=reprejection_->PixelType(pixel);
                if(type !=0 && type != 240){  //判断点类型
                    pt.intensity=0; 
                }
            } 
        }
    #ifdef OUTPUT_DEBUG
        output_.debug_info.repreject_image=reprejection_->DebugImg(output_cloud);
    #endif 
    }
    
    lidar_arrived_signal_.Reset();
    output_.result=output_cloud;
    
    return output_;
}