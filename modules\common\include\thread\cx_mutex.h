﻿#ifndef _CX_MUTEX_H_
#define _CX_MUTEX_H_

#include "cx_pubhead.h"

#ifdef WIN32
#include <windows.h>
#else
#include <pthread.h>
#endif
namespace common_lib {
class CXMutex {
 public:
  CXMutex();
  virtual ~CXMutex();

 public:
  cx_int Lock();
  cx_int Unlock();

  cx_int SetRecursiveAttr();

 protected:
#ifdef WIN32
  CRITICAL_SECTION m_cs;
#else
  pthread_mutex_t mutex_;
#endif

  friend class CXAutoMutex;
};
}  // namespace common_lib

#endif  // _CX_MUTEX_H_
