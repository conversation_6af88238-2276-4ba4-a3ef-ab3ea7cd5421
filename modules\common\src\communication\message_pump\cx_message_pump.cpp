#include "communication/message_pump/cx_message_pump.h"

#include "cx_singleton.h"
#include "cx_template.h"
namespace common_lib {
CXMessagePump *GetSingleton4MessagePump() {
  static CXSingleton<CXMessagePump> s_MessagePump;

  return s_MessagePump.GetSingletonInstance();
}

CXMessagePump::CXMessagePump() {}

CXMessagePump::~CXMessagePump() {
  messages_deq_.LockW();

  FreeDequeContainer(messages_deq_.data_);

  messages_deq_.UnlockW();
}

cx_int CXMessagePump::SendMessage(const CXMessage &msg) {
  ASSERT(0);

  return 0;
}

cx_int CXMessagePump::PostMessage(const CXMessage &msg) {
  messages_deq_.LockW();

  CXMessage *pNewMessage = new CXMessage(msg.m_messageId);
  *pNewMessage = msg;
  messages_deq_.data_.push_back(pNewMessage);

  messages_deq_.UnlockW();

  return 0;
}

cx_int CXMessagePump::GetMessage(CXMessage &msg) {
  cx_int iResult = 0;

  messages_deq_.LockW();

  if (!messages_deq_.data_.empty()) {
    CXMessage *pMessage = messages_deq_.data_.front();
    ASSERT(pMessage);
    msg = *pMessage;
    messages_deq_.data_.pop_front();
    DELETE_S(pMessage);
  } else {
    iResult = -1;
  }

  messages_deq_.UnlockW();

  return iResult;
}

cx_int CXMessagePump::GetBackMessage(CXMessage &msg) {
  cx_int iResult = 0;

  messages_deq_.LockW();

  if (!messages_deq_.data_.empty()) {
    CXMessage *pMessage = messages_deq_.data_.back();
    ASSERT(pMessage);
    msg = *pMessage;
  } else {
    iResult = -1;
  }

  messages_deq_.UnlockW();

  return iResult;
}

cx_int CXMessagePump::PeekMessage(CXMessage &msg) {
  ASSERT(0);

  return 0;
}

cx_int CXMessagePump::DestroyMessage(CXMessage &msg) {
  ASSERT(0);
  DELETE_S(msg.m_lParam);
  return 0;
}

cx_int PostMessage(const CXMessage &msg) {
  GetSingleton4MessagePump()->PostMessage(msg);
}

cx_int GetMessage(CXMessage &msg) {
  GetSingleton4MessagePump()->GetMessage(msg);
}
}  // namespace common_lib