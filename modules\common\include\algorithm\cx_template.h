#ifndef CMTEMPLATE_H
#define CMTEMPLATE_H

#include "cx_pubhead.h"
namespace common_lib {
template <class T>
void FreeVectorContainer(typename std::vector<T *> &container) {
  for (typename std::vector<T *>::iterator itr = container.begin(); itr != container.end(); ++itr) {
    T *pValue = *itr;
    DELETE_S(pValue);
  }

  container.clear();
}

template <class T>
void FreeDequeContainer(typename std::deque<T *> &container) {
  for (typename std::deque<T *>::iterator itr = container.begin(); itr != container.end(); ++itr) {
    T *pValue = *itr;
    DELETE_S(pValue);
  }

  container.clear();
}

template <class KEY, class VALUE>
void FreeMapContainer(typename std::map<KEY, VALUE *> &container) {
  for (typename std::map<KEY, VALUE *>::iterator itr = container.begin(); itr != container.end();
       ++itr) {
    VALUE *pValue = itr->second;
    DELETE_S(pValue);
  }

  container.clear();
}
}  // namespace common_lib
#endif  // CMTEMPLATE_H
