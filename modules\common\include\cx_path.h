﻿#ifndef _CX_PATH_H_
#define _CX_PATH_H_

#include "cx_pubhead.h"
namespace common_lib {
cx_int SetModuleFileName(cx_char *pszFile);

#ifdef LINUX
cx_dword GetModuleFileName(cx_handle hModule, cx_char *lpFilename, cx_dword nSize);
#endif  // LINUX

cx_int SetExePath(const cx_char *const szPath);
cx_int GetExePath(cx_string &strPath);
cx_int SetMapPath(const cx_char *const szPath);
cx_int GetMapPath(cx_string &strPath);

void DeleteDirectory(const char *szPath);
void RemoveFiles(const char *path);

cx_bool IsFileExist(const cx_string &strFileName);

void ConvertSlash(char *szFileName);

cx_int CleanDir(const char *szDir);

std::vector<std::string> GetListFiles(const std::string &path, const std::string &exten = "*");
std::vector<std::string> GetListFolders(const std::string &path, const std::string &exten = "*");
std::vector<std::string> GetListFilesR(const std::string &path, const std::string &exten = "*");

std::vector<std::string> SearchListFiles(const std::string &path, const std::string &name = "*",
                                         bool bIsFile = true);

cx_int is_dir(const char *filename);
cx_int delete_dir(const char *dirname);

void CreatPath(cx_string &strPath);

FILE *CreateFile(const cx_string &strPath, const char *filename);

void SetRecordPath(const cx_string &strPath);

cx_string GetRecordPath();
}  // namespace common_lib

#endif  // _CX_PATH_H_
