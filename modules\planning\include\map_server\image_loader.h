#pragma once
#include <opencv2/core/core.hpp>
#include <string>

class ImageLoader {
public:
  ImageLoader(const std::string& map_file, double threshold_occupied = 0.65, double threshold_free = 0.196);
  const std::vector<int8_t>& getData() const { return data_; }
  unsigned int getWidth() const { return width_; }
  unsigned int getHeight() const { return height_; }

private:
  std::vector<int8_t> data_;
  unsigned int width_, height_;
};