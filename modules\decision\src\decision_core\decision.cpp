// Standard Library
#include <chrono>
#include <thread>
#include <algorithm>
#include <memory>
#include <cmath>
#include <vector>

// Third-party Libraries
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/filters/passthrough.h>
#include <Eigen/Dense>

// Project Headers
#include "decision.h"
#include "config.h"
#include "yaml_config.h"
#include "decision_output.h"
#include "state_machine.h"
#include "decision_comm.h"


namespace decision
{

Decision::Decision(){
  // Load parameters
  decision_comm_ptr = nullptr;
}

Decision::~Decision(){
  if(decision_comm_ptr){
    decision_comm_ptr->ShutDown();

    decision_comm_ptr = nullptr;
  }
}

bool Decision::Initialize(const std::string &config_file_path){
  if(ReadParams(config_file_path)){
    decision_comm_ptr = std::make_shared<DecisionComm>(config_file_path);

    return true;
  }
  return false;
}


void Decision::Run(){

  while(decision_comm_ptr && !decision_comm_ptr->IsTerminated()){

    if(SubscribLocalizationData()){

      //获取感知数据
      GetObstaclesData();
      SubscribImuData();
      //获取全局规划路径
      SubscribGlobalPathData();

      //获取动态物体的轨迹
      GetTrajectoriesData();

      // 分析环境
      AnalyzeStaticObstacles();
      AnalyzeDynamicObstacles();
      AnalyzePathFeasibility();
      AnalyzeTerrainFeatures();

      // 执行当前状态
      DecisionData decision_out;
      current_state_->execute(*this, decision_out);

      // 发布决策
      decision_comm_ptr->PublishDecisionData(decision_out);
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(10)); //sleep 10 ms
  }
}
void Decision::ChangeState(std::shared_ptr<State> new_state){
  current_state_->exit(*this);
  current_state_ = new_state;
  current_state_->enter(*this);
}

// 静态障碍物分析
void Decision::AnalyzeStaticObstacles(){
  context_.frontal_obstacle_detected = false;
  context_.corridor_detected = false;
  
  if (context_.obstacles.cloud_ptr->points.empty()) return;
  
  // 检测前方障碍物
  for (const auto& point : context_.obstacles.cloud_ptr->points) {
      if (point.z < -0.5) continue; // 忽略地面点
      
      if (point.x > 0 && point.x < params_.min_safe_distance && 
          std::abs(point.y) < 0.5) {
          context_.frontal_obstacle_detected = true;
          break;
      }
  }
  
  // 检测走廊环境
  DetectCorridorEnvironment();
}

//动态障碍物分析
void Decision::AnalyzeDynamicObstacles(){
  context_.collision_risk = false;
  context_.yield_required = false;
  
  if (context_.dynamic_trajectories.poses_.empty()) return;
  
  // 获取机器人当前位置和速度
  auto robot_pose = context_.localization.pose_data;
  auto robot_vel = context_.localization.vel;
  
  // 计算机器人未来位置
  double robot_future_x = robot_pose.position[0] + 
                        robot_vel[0] * params_.collision_time_threshold;
  double robot_future_y = robot_pose.position[1] + 
                        robot_vel[1] * params_.collision_time_threshold;
  
  // 检查每个动态轨迹
  for (const auto& pose : context_.dynamic_trajectories.poses_) {
      // 计算障碍物未来位置. 注：下面的orientation存储的是速度
      double obs_future_x = pose.position[0] + pose.orientation[0] * params_.collision_time_threshold;
      double obs_future_y = pose.position[1] + pose.orientation[1] * params_.collision_time_threshold;
      
      // 计算未来距离
      double dx = robot_future_x - obs_future_x;
      double dy = robot_future_y - obs_future_y;
      double future_dist = std::sqrt(dx*dx + dy*dy);
      
      // 检查碰撞风险
      if (future_dist < params_.min_safe_distance) {
          context_.collision_risk = true;
          
          // 检查是否需要让行（障碍物在右侧通行规则）
          if (obs_future_y < 0) {
              context_.yield_required = true;
          }
          break;
      }
  }
}

//路径可行性分析
void Decision::AnalyzePathFeasibility(){
  context_.path_blocked = false;
    
  if (context_.global_path.poses_.empty() || context_.obstacles.cloud_ptr->points.empty()) return;
  
  // 检查路径前方是否有障碍物
  for (size_t i = 0; i < std::min(10, (int)context_.global_path.poses_.size()); i++) {
      const auto& pose = context_.global_path.poses_[i];
      
      // 检查附近是否有障碍物
      for (const auto& point : context_.obstacles.cloud_ptr->points) {
          double dx = pose.position[0] - point.x;
          double dy = pose.position[1] - point.y;
          double dist = sqrt(dx * dx + dy * dy);
          
          if (dist < params_.min_safe_distance && point.z > -0.4) {
              context_.path_blocked = true;
              break;
          }
      }
  }
  
}

//地形特征分析
void Decision::AnalyzeTerrainFeatures(){
  context_.slope_detected = false;
  context_.stairs_detected = false;
  context_.rough_terrain_detected = false;
  
  // if (context_.obstacles.cloud_ptr->points.empty() || !context_.imu_data.orientation.x) return;
  if (context_.obstacles.cloud_ptr->points.empty())
    return;

  // 坡度检测
  DetectSlope();
  
  // 楼梯检测
  DetectStairs();
  
  // 崎岖地形检测
  DetectRoughTerrain();
}

bool Decision::ReadParams(const std::string &config_file_path){

  common_lib::YamlConfig &config = common_lib::YamlConfig::GetInstance();
  if(!config.LoadFile(config_file_path)){
    return false;
  }

  params_.frequency = config.GetParam<double>("frequency", 10);
  params_.min_safe_distance = config.GetParam<double>("min_safe_distance", 0.6);
  params_.collision_time_threshold = config.GetParam<double>("collision_time_threshold", 2.0);
  params_.emergency_stop_distance = config.GetParam<double>("emergency_stop_distance", 0.3);
  params_.corridor_min_width = config.GetParam<double>("corridor_min_width", 0.8);
  params_.corridor_max_width = config.GetParam<double>("corridor_max_width", 2.5);
  params_.min_wall_length = config.GetParam<double>("min_wall_length", 1.5);
  params_.max_angle_deviation = config.GetParam<double>("max_angle_deviation", 0.2);
  params_.max_speed = config.GetParam<double>("max_speed", 1.5);
  params_.normal_speed = config.GetParam<double>("normal_speed", 1.0);
  params_.slow_speed = config.GetParam<double>("slow_speed", 0.4);
  params_.stair_speed = config.GetParam<double>("stair_speed", 0.2);
  params_.turn_speed_reduction = config.GetParam<double>("turn_speed_reduction", 0.6);
  params_.slope_threshold = config.GetParam<double>("slope_threshold", 0.15);
  params_.step_height_threshold = config.GetParam<double>("step_height_threshold", 0.15);
  params_.step_width_threshold = config.GetParam<double>("step_width_threshold", 0.25);
  params_.rough_terrain_threshold = config.GetParam<double>("rough_terrain_threshold", 0.08);
  params_.min_points_per_side = config.GetParam<int>("min_points_per_side", 15);
  params_.min_steps = config.GetParam<int>("min_steps", 2);
  params_.default_gait = config.GetParam<std::string>("default_gait", "TROT");
  params_.stair_gait = config.GetParam<std::string>("stair_gait", "WALK");
  params_.obstacle_gait = config.GetParam<std::string>("obstacle_gait", "CRAWL");
  params_.narrow_gait = config.GetParam<std::string>("narrow_gait", "CRAWL");
  params_.yield_gait = config.GetParam<std::string>("yield_gait", "STAND");

  return true;
}

bool Decision::GetObstaclesData()
{
  bool flag = decision_comm_ptr->SubscribTerrainCloudData(context_.obstacles);
  if(flag){
    PreprocessPointCloud(context_.obstacles.cloud_ptr);
  }

  return flag;
}

bool Decision::SubscribGlobalPathData(){

  return decision_comm_ptr->SubscribGlobalPathData(context_.global_path);
}

bool Decision::GetTrajectoriesData(){

  //TODO
  return false;
}
bool Decision::SubscribLocalizationData(){

  return decision_comm_ptr->SubscribLocalizationData(context_.localization);
}

bool Decision::SubscribImuData(){

  return decision_comm_ptr->SubscribImuData(context_.imu_data_dq);
}

void Decision::PreprocessPointCloud(pcl::PointCloud<pcl::PointXYZI>::Ptr& cloud){
  // 1. 降采样
  pcl::VoxelGrid<pcl::PointXYZI> voxel_filter;
  voxel_filter.setInputCloud(cloud);
  voxel_filter.setLeafSize(0.05f, 0.05f, 0.05f);
  voxel_filter.filter(*cloud);

  // 2. 范围过滤
  pcl::PassThrough<pcl::PointXYZI> pass;
  pass.setInputCloud(cloud);
  pass.setFilterFieldName("x");
  pass.setFilterLimits(0.1, 5.0);
  pass.filter(*cloud);

  pass.setFilterFieldName("y");
  pass.setFilterLimits(-3.0, 3.0);
  pass.filter(*cloud);

  pass.setFilterFieldName("z");
  pass.setFilterLimits(-0.5, 1.0);
  pass.filter(*cloud);
}

//实现墙壁方向拟合
Eigen::Vector2f Decision::FitWallDirection(const std::vector<pcl::PointXYZI>& points){
  if (points.empty()) return Eigen::Vector2f::Zero();
    
  // 计算质心
  Eigen::Vector2f centroid = Eigen::Vector2f::Zero();
  for (const auto& p : points) {
      centroid += Eigen::Vector2f(p.x, p.y);
  }
  centroid /= points.size();
  
  // 计算协方差矩阵
  Eigen::Matrix2f covariance = Eigen::Matrix2f::Zero();
  for (const auto& p : points) {
      Eigen::Vector2f pt(p.x, p.y);
      Eigen::Vector2f diff = pt - centroid;
      covariance += diff * diff.transpose();
  }
  
  // 特征值分解
  Eigen::SelfAdjointEigenSolver<Eigen::Matrix2f> solver(covariance);
  return solver.eigenvectors().col(1).normalized(); // 主方向

}

//检测走廊环境
void Decision::DetectCorridorEnvironment(){

  if (context_.obstacles.cloud_ptr->points.empty()) return;
    
    // 分割左右区域
    std::vector<pcl::PointXYZI> left_points;
    std::vector<pcl::PointXYZI> right_points;
    
    for (const auto& point : context_.obstacles.cloud_ptr->points) {
        if (point.z < 0.1 || point.z > 1.0) continue; // 忽略地面点和高空点
        
        if (point.y > 0) { // 左侧点
            left_points.push_back(point);
        } else if (point.y < 0) { // 右侧点
            right_points.push_back(point);
        }
    }
    
    // 检查每侧是否有足够点
    if (left_points.size() < params_.min_points_per_side || 
        right_points.size() < params_.min_points_per_side) {
        context_.corridor_detected = false;
        return;
    }
    
    // 拟合左侧墙壁直线
    Eigen::Vector2f left_dir = FitWallDirection(left_points);
    // 拟合右侧墙壁直线
    Eigen::Vector2f right_dir = FitWallDirection(right_points);
    
    // 检查墙壁是否平行
    float dot_product = left_dir.dot(right_dir);
    float angle_diff = std::acos(std::abs(dot_product));
    
    if (angle_diff > params_.max_angle_deviation) {
        context_.corridor_detected = false;
        return;
    }
    
    // 计算平均距离
    auto calculateAvgDist = [](const std::vector<pcl::PointXYZI>& points) {
        float sum = 0.0f;
        for (const auto& p : points) sum += std::abs(p.y);
        return sum / points.size();
    };
    
    float left_avg = calculateAvgDist(left_points);
    float right_avg = calculateAvgDist(right_points);
    
    // 计算走廊宽度
    context_.corridor_width = left_avg + right_avg;
    
    // 检查宽度是否在合理范围
    if (context_.corridor_width < params_.corridor_min_width || 
        context_.corridor_width > params_.corridor_max_width) {
        context_.corridor_detected = false;
        return;
    }
    
    // 计算偏离中心的程度
    context_.corridor_offset = (left_avg - right_avg) / 2.0f;
    
    context_.corridor_detected = true;
}

//检测斜坡
void Decision::DetectSlope(){
// 使用IMU数据作为主要依据
//TODO 



  // tf2::Quaternion q(
  //   context_.imu_data_dq.back().orientation.x,
  //   context_.imu_data_dq.back().orientation.y,
  //   context_.imu_data_dq.back().orientation.z,
  //   context_.imu_data_dq.back().orientation.w);
  // tf2::Matrix3x3 m(q);
  // double roll, pitch, yaw;
  // m.getRPY(roll, pitch, yaw);

  // // 检查是否有显著坡度
  // if (std::abs(pitch) > params_.slope_threshold || 
  //   std::abs(roll) > params_.slope_threshold) {
  //   context_.slope_detected = true;
  //   context_.slope_angle = std::max(std::abs(pitch), std::abs(roll));
  //   context_.slope_direction = (pitch > 0) ? 
  //       UP_SLOPE : DOWN_SLOPE;
  // }
}

//检测台阶
void Decision::DetectStairs(){
  if (context_.obstacles.cloud_ptr->points.empty()) return;
    
  // 按X方向排序点云
  auto sorted_points = context_.obstacles.cloud_ptr->points;
  std::sort(sorted_points.begin(), sorted_points.end(), 
           [](const pcl::PointXYZI& a, const pcl::PointXYZI& b) {
               return a.x < b.x;
           });
  
  // 检测水平段
  std::vector<float> level_heights;
  std::vector<float> level_ranges;
  
  float current_height = sorted_points[0].z;
  float current_min_x = sorted_points[0].x;
  float current_max_x = sorted_points[0].x;
  
  for (size_t i = 1; i < sorted_points.size(); i++) {
      const auto& p = sorted_points[i];
      
      // 忽略Y方向太偏的点
      if (std::abs(p.y) > 0.5) continue;
      
      // 检查高度变化是否超过阈值
      if (std::abs(p.z - current_height) > params_.step_height_threshold) {
          // 保存当前水平段
          level_heights.push_back(current_height);
          level_ranges.push_back(current_max_x - current_min_x);
          
          // 开始新水平段
          current_height = p.z;
          current_min_x = p.x;
          current_max_x = p.x;
      } else {
          // 更新当前水平段范围
          current_max_x = std::max(current_max_x, p.x);
          current_min_x = std::min(current_min_x, p.x);
      }
  }
  
  // 保存最后一个水平段
  level_heights.push_back(current_height);
  level_ranges.push_back(current_max_x - current_min_x);
  
  // 分析台阶模式
  int step_count = 0;
  bool ascending = false;
  
  for (size_t i = 1; i < level_heights.size(); i++) {
      float height_diff = level_heights[i] - level_heights[i-1];
      float step_width = level_ranges[i];
      
      // 检查是否符合台阶特征
      if (std::abs(height_diff) > params_.step_height_threshold && 
          step_width > params_.step_width_threshold) {
          
          // 检查连续性
          if (step_count == 0) {
              ascending = (height_diff > 0);
          } else if (ascending != (height_diff > 0)) {
              // 方向改变，重置计数
              step_count = 0;
              continue;
          }
          
          step_count++;
      } else {
          // 不符合台阶特征，重置计数
          step_count = 0;
      }
      
      // 找到足够多的连续台阶
      if (step_count >= params_.min_steps) {
          context_.stairs_detected = true;
          context_.stair_direction = ascending ? ASCENDING : DESCENDING;
          return;
      }
  }
}

// 崎岖地形检测
void Decision::DetectRoughTerrain(){
  if (context_.slope_detected || context_.stairs_detected) return;
    
  // 采样前方区域点云
  std::vector<float> heights;
  for (const auto& point : context_.obstacles.cloud_ptr->points) {
      if (point.x > 0.5 && point.x < 1.5 && std::abs(point.y) < 0.8) {
          heights.push_back(point.z);
      }
  }
  
  if (heights.size() < 20) return;
  
  // 计算高度标准差
  float sum = std::accumulate(heights.begin(), heights.end(), 0.0f);
  float mean = sum / heights.size();
  
  float variance = 0.0f;
  for (float h : heights) {
      variance += (h - mean) * (h - mean);
  }
  float stddev = std::sqrt(variance / heights.size());
  
  // 检测崎岖地形
  if (stddev > params_.rough_terrain_threshold) {
      context_.rough_terrain_detected = true;
      context_.terrain_roughness = stddev;
  }
}

} // namespace decision {
