#ifndef PERCEPTION__IMPL__H___H
#define PERCEPTION__IMPL__H___H

#include <thread>
#include <memory>
#include <pcl/point_types.h>
#include <pcl/point_cloud.h>
#include <opencv2/opencv.hpp>
#include <Eigen/Geometry>

#include "common/utils.h"
#include "common/threadsafe_timeout.hpp"
#include "obstacle_devide/obstacle_devide.h"

namespace perception{


class PerceptionImpl{

public:
    PerceptionImpl(const std::string &config_file_path);
    virtual     ~PerceptionImpl();
    void        SetOutputCallback(PerceptionOutputCallback callback);
    

    virtual     void PushCloud(PointCloudT::Ptr cloud)=0;
    virtual     void PushRgbImage(cv::Mat rgb)=0;
    virtual     void PushDepthImage(cv::Mat depth)=0;
    virtual     void SetRgbIntrinsic(const CameraInfo& info )=0;
    virtual     void SetDepthIntrinsic(const CameraInfo& info )=0;
    virtual     void SetDepth2RgbTransform(const TransformExtrinsics& transform)=0;

protected:
    
    PerceptionOutputCallback                output_callback_ =nullptr;


};

}
#endif