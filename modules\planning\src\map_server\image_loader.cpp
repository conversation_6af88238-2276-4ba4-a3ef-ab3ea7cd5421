#include "image_loader.h"
#include <opencv2/imgcodecs/imgcodecs.hpp>
#include <stdexcept>

ImageLoader::ImageLoader(const std::string& map_file, double threshold_occupied, double threshold_free) {
  cv::Mat image = cv::imread(map_file, cv::IMREAD_UNCHANGED);
  if (image.empty()) throw std::runtime_error("Unable to load map image");
  width_ = image.cols;
  height_ = image.rows;
  data_.resize(width_ * height_);

  for (int y = 0; y < height_; ++y) {
    for (int x = 0; x < width_; ++x) {
      int i = x + (height_ - y - 1) * width_;
      uchar pixel = image.at<uchar>(y, x);
      if (pixel >= 0 && pixel <= 255) {
        double occ = (255 - pixel) / 255.0;
        if (occ > threshold_occupied) data_[i] = 100;
        else if (occ < threshold_free) data_[i] = 0;
        else data_[i] = -1;
      } else {
        data_[i] = -1;
      }
    }
  }
}