add_library(${PROJECT_NAME}_communication SHARED
    subcribe.cpp
)

find_package(OpenCV REQUIRED)

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/
    ${CMAKE_CURRENT_SOURCE_DIR}/../common
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/eskf
    ${CMAKE_CURRENT_SOURCE_DIR}/../zero_detect
    ${CMAKE_CURRENT_SOURCE_DIR}/../data_pretreat
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include

    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/thread
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/model
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/algorithm
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/file
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/platform
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/config
    #${CMAKE_CURRENT_SOURCE_DIR}/../../../common/src/communication
    #${CMAKE_CURRENT_SOURCE_DIR}/../../../common/src/communication/MessagePump

    ${CMAKE_CURRENT_SOURCE_DIR}/../../../communication/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../communication/include/data_types
    ${OpenCV_INCLUDE_DIRS}
)

target_link_libraries(${PROJECT_NAME}_communication
    ${PROJECT_NAME}_zero_detect  
    common_lib
    communication_core
    ${EIGEN3_LIBRARIES}
    ${PCL_LIBRARIES}
    ${OpenCV_LIBS}
    #fmt::fmt
)
