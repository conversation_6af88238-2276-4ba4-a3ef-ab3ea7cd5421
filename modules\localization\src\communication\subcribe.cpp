#include "subcribe.h"
#include "cx_thread_pool.h"
#include "cx_time.h"

LocSubcribe::LocSubcribe(/* args */)
:m_LidarPreprocess(new LidarPreprocess()),
  m_Config(common_lib::YamlConfig::GetInstance()),
  m_first_lidar(false),
  m_last_timestamp_lidar(0.0),
  m_lidar_mean_scantime(0.0),
  m_initialized_last_time(false),
  m_last_time(0.0),
  m_time_interval(0.1),
  m_buse_rtk(false), // 默认使用RTK
  m_bflag_manualpos(false),
  m_bGnssDataReady(false),
  m_bimu_vaild(false),
  m_dimu_fault_time(0.0),
  m_dlast_timestamp_imu(0.0),
  m_dlast_timestamp_gnss(0.0),
  m_lidar_end_time(0.0),
  m_last_time_packed(0.0),
  m_package_end_time_last(0.0),
  m_bzero_detect_enable(false),
  m_simu_axis("FLU") // 默认坐标系为FLU
{
    //Init();
}

LocSubcribe::~LocSubcribe()
{
    
}

void LocSubcribe::Init()
{
    m_ptr_communication_ = std::make_shared<communication::Communication>("LocalizationModule");
    if (!m_ptr_communication_->Initialize("config/communication_config.yaml")) {
        throw std::runtime_error("Failed to initialize communication module.");
    }
    // Create subscribers for IMU, GNSS, and LiDAR data
    m_ptr_imu_subscriber_ = m_ptr_communication_->CreateImuDataSubscriber("/imu");
    m_ptr_gnss_subscriber_ = m_ptr_communication_->CreateGnssDataSubscriber("gnss_topic");
    m_ptr_lidar_subscriber_ = m_ptr_communication_->CreateLidarDataSubscriber("/lslidar_point_cloud");
    m_ptr_manaul_pose_data_subscriber = m_ptr_communication_->CreatePoseDataSubscriber("/initialpose");
    m_ptr_web_pose_data_subscriber = m_ptr_communication_->CreatePoseDataSubscriber("/web_loc_goal_pose");

    m_pcloud_data_publisher_ptr = m_ptr_communication_->CreateCloudDataPublisher("/globalmap", "map", 1);
    m_pcloud_world_publisher_ptr = m_ptr_communication_->CreateCloudDataPublisher("/cloud_registered", "map", 100000);
    m_pcloud_body_publisher_ptr = m_ptr_communication_->CreateCloudDataPublisher("/cloud_registered_body", "body", 100000);
    m_local_map_publisher_ptr = m_ptr_communication_->CreateCloudDataPublisher("/Laser_map", "map", 100000);
    m_podometry_publisher_ptr = m_ptr_communication_->CreateOdometryPublisher("/Odometry","map","body", 100000);
    m_tf_data_publisher_ptr = m_ptr_communication_->CreateTFDataPublisher("map", "body");
    m_path_data_publisher_ptr = m_ptr_communication_->CreatePathDataPublisher("/path", "map", 100000);
    m_gnss_path_publisher_ptr = m_ptr_communication_->CreatePathDataPublisher("/gnss_path", "map", 100000);

    m_LidarPreprocess->blind = 0.8;
    m_LidarPreprocess->N_SCANS = 16; // 设置激光雷达的扫描线数
    m_LidarPreprocess->SCAN_RATE = 10; // 设置激光雷达的扫描频率
    m_LidarPreprocess->time_unit = SEC; // 设置时间单位为秒
    m_LidarPreprocess->lidar_type = VELO16; // 设置激光雷达类型为VELO16
    m_LidarPreprocess->feature_enabled = false; // 启用特征提取
    m_LidarPreprocess->point_filter_num = 1; // 设置点云滤波器的点数

    m_ptr_gnss2enu = std::make_shared<LLAENU>();
    m_buse_rtk = m_Config.GetParam<bool>("usertk", false);
    if(m_buse_rtk)
    {
        InitGnss();
    }
    m_deq_IMUData.clear();
    m_deq_IMUData2RTK.clear();
    m_deq_GnssData.clear();

    Start();  
}

void LocSubcribe::InitGnss()
{
    string load_pose_path = m_Config.GetParam<std::string>("loadposepath", "");
    std::ifstream inputFile(load_pose_path);
    double lat0 = 0.0, lon0 = 0.0, alt0 = 0.0,
           max_z = 0.0, min_z = 0.0;
    inputFile >> lat0 >> lon0 >> alt0 >> max_z >> min_z;
    if (lon0 < 0.1 || lat0 < 0.1)
    {
        cout<<"Failed to read map RTK origin, manual repositioning required" << endl;
        m_buse_rtk = false; // 如果读取失败，则不使用RTK
    }
    else
    {
        m_ptr_gnss2enu->SetOriginLLA(lat0, lon0, alt0);
    }
}

void LocSubcribe::Start()
{
    CXWork::SetName("LocSubcribe");
    CXWork::SetThreadProc(LocSubcribe::MainLoop);
    CXWork::SetThreadContext(this);
    GetSingleton4ThreadPool()->ExecuteTask(this, m_pThread);
}

void LocSubcribe::StartCommunication()
{
    // 启动通信模块
    m_ptr_communication_->Run();
}

void LocSubcribe::ProcessImuData(communication::IMUData &imu_data)
{
    // 如果imu无效，则置有效
    if (m_bimu_vaild == false)
    {
        // imu_fault_time
        m_bimu_vaild = true;
        cout<<"IMU falut recovery! recovery time:"<< imu_data.time - m_dimu_fault_time << endl;
    }

    // 检查数据是否异常
    if (std::fabs(imu_data.linear_acceleration[0]) > 1000 || 
        std::fabs(imu_data.linear_acceleration[1]) > 1000 || 
        std::fabs(imu_data.linear_acceleration[2]) > 1000 ||
        std::fabs(imu_data.angular_velocity[0]) > 1000 ||
        std::fabs(imu_data.angular_velocity[1]) > 1000 ||
        std::fabs(imu_data.angular_velocity[2]) > 1000) {
        std::cout << "IMU data abnormal!" << std::endl;
        return;  
    }

    if (m_simu_axis == "LBU")
    {
        //*********左后上转前左上*********//
        double x_acc_tmp = -imu_data.linear_acceleration[1];
        double y_acc_tmp = imu_data.linear_acceleration[0];
        imu_data.linear_acceleration[0] = x_acc_tmp;
        imu_data.linear_acceleration[1] = y_acc_tmp;

        double x_ang_tmp = -imu_data.angular_velocity[1];
        double y_ang_tmp = imu_data.angular_velocity[0];
        imu_data.angular_velocity[0] = x_ang_tmp;
        imu_data.angular_velocity[1] = y_ang_tmp;
    }

    double timestamp = GetCurrentSeconds(); // 获取当前时间戳
    imu_data.time = timestamp; // 更新IMU数据的时间戳   

    m_mtx_buffer.lock();

    if (timestamp < m_dlast_timestamp_imu)
    {
        m_deq_IMUData.clear();
        m_deq_IMUData2RTK.clear();
    }

    m_dlast_timestamp_imu = timestamp;

    m_deq_IMUData.push_back(imu_data);
    if (m_buse_rtk)
    {
        m_deq_IMUData2RTK.push_back(imu_data);
    }
    // 监视缓存区的大小，大小超出限制就pop掉最一开始的数据
    if (m_deq_IMUData.size() > 1000)
    {
        m_deq_IMUData.pop_front();
        // ROS_WARN("imu buffer is too large!");
    }

    if (m_deq_IMUData2RTK.size() > 1000)
    {
        m_deq_IMUData2RTK.pop_front();
        // ROS_WARN("imu_rtk buffer is too large!");
    }

    //零速检测
    ImuData imu_data_(imu_data.angular_velocity[0], imu_data.angular_velocity[1],imu_data.angular_velocity[2],
                     imu_data.linear_acceleration[0], imu_data.linear_acceleration[1], imu_data.linear_acceleration[2]);

    zero_vel_detect.stop_detect(imu_data_);

    m_mtx_buffer.unlock();
}

void LocSubcribe::ProcessGnssData(const communication::GNSSData &gnss_data)
{
    // 处理GNSS数据
    if (m_buse_rtk == false || gnss_data.rtk_status < RTK_STATUS_THRESHOLD)
    {
        // 如果使用RTK且RTK状态小于阈值，则不处理GNSS数据
        std::cout << "RTK status is below threshold, GNSS data will not be processed." << std::endl;
        return;
    }
    double timestamp = GetCurrentSeconds();
    m_mtx_buffer.lock();
    if (timestamp < m_dlast_timestamp_gnss)
    {
        // 如果GNSS时间戳小于上次记录的时间戳，则清空缓存
        m_deq_GnssData.clear();
    }
    m_dlast_timestamp_gnss = timestamp;

    // 通过gps_qual给出协方差
    double pose_std = 0.05;//固定解下的标准差
    if(gnss_data.rtk_status == 5)
    {
        pose_std = 0.5;
    }
    else if(gnss_data.rtk_status != 4)
    {
        pose_std = 20.0;
    }

    double posecov = pose_std * pose_std;
    // gnss_data.llh_std[0] = posecov; // 纬度标准差
    // gnss_data.llh_std[1] = posecov; // 经度标准差
    // gnss_data.llh_std[2] = 2.0 * posecov; // 海拔标准差
    m_mtx_buffer.unlock();

    Eigen::Vector3d enupose;
    enupose = m_ptr_gnss2enu->LLAToENU(gnss_data.longitude * M_PI / 180, gnss_data.latitude * M_PI / 180, gnss_data.altitude);
   
    GnssENU gnss_enu;
    gnss_enu.enu_x = enupose[0];// gnss_data.local_E ;   北
    gnss_enu.enu_y = enupose[1];// gnss_data.local_N;    东
    gnss_enu.enu_z = enupose[2];//  地
    gnss_enu.latitude = gnss_data.latitude;
    gnss_enu.longitude = gnss_data.longitude;
    gnss_enu.altitude = gnss_data.altitude;
    gnss_enu.timestamp = timestamp;
    gnss_enu.status = gnss_data.rtk_status; // RTK状态
    gnss_enu.covariance[0] = posecov; // 纬度协方差
    gnss_enu.covariance[7] = posecov; // 经度协方差
    gnss_enu.covariance[14] = 2.0 * posecov; // 海拔协方差
    m_deq_GnssData.push_back(gnss_enu);
    if (m_deq_GnssData.size() > 50)
    {
        m_deq_GnssData.pop_front();
    }
    m_bGnssDataReady = true; // 标记GNSS数据已准备好 

    communication::PoseData pose;
    pose.time = timestamp;
    pose.position = enupose.cast<float>();
    if(m_path_data.poses_.size() > 100)
    {
        m_path_data.poses_.erase(m_path_data.poses_.begin());
    }
    m_path_data.poses_.push_back(pose);
}

void LocSubcribe::ProcessLidarData(const communication::CloudXYZRIData &lidar_data)
{
    m_mtx_buffer.lock();
    //lidar_data.time = GetCurrentSeconds(); // 获取当前时间戳
    if (!m_first_lidar) 
    {
        m_first_lidar = true;
    }
    
    double current_time = GetCurrentSeconds();
    if (current_time < m_last_timestamp_lidar)
    {
        cout<<"lidar loop back, clear buffer"<<endl;
        m_lidar_buffer.clear();
    }

    PointCloudXYZI::Ptr ptr(new PointCloudXYZI());
    m_LidarPreprocess->process(lidar_data, ptr);
    //数据异常
    if (ptr->points.back().curvature / 1000.0 > 1.0)
    {
        cout<<"lidar data error, curvature: "<<ptr->points.back().curvature / 1000.0<<endl;
        m_mtx_buffer.unlock();
        return;
    }

    //************将点云由左后上转到前左上*******************/
    for (auto &point : ptr->points)
    {
        double x_tmp = -point.y;
        double y_tmp = point.x;
        point.x = x_tmp;
        point.y = y_tmp;
    }

    for (auto &point : ptr->points)
    {
        // 对雷达点云绕z轴旋转105度
        double x_tmp = -0.2588 * point.x + 0.9659 * point.y;
        double y_tmp = -0.9659 * point.x - 0.2588 * point.y;
        point.x = x_tmp;
        point.y = y_tmp;
    }

    // const double angle_deg = 105;
    // double angle_rad = angle_deg * M_PI / 180.0;
    // Eigen::AngleAxisd rotation(-angle_rad, Eigen::Vector3d::UnitZ());
    // for (auto &point : ptr->points)
    // {
    //     // 对雷达点云绕z轴旋转105度
    // #if 0
    //     double x_tmp = -0.2588 * point.x + 0.9659 * point.y;
    //     double y_tmp = -0.9659 * point.x - 0.2588 * point.y;
    //     point.x = x_tmp;
    //     point.y = y_tmp;
    // #else

    //     Eigen::Vector3d xy(point.x, point.y, 0);
    //     Eigen::Vector3d xy_totated = rotation * xy;
    //     point.x = xy_totated(0);
    //     point.y = xy_totated(1);
    //     // double x_tmp = cos(angle_rad)* point.x + sin(angle_rad) * point.y;
    //     // double y_tmp = -sin(angle_rad) * point.x + cos(angle_rad) * point.y;
    //     // point.x = x_tmp;
    //     // point.y = y_tmp;
    // #endif      
    // }

    // 如果雷达点云没有数据，直接return
    if (ptr->points.size() < 100)
    {
        m_mtx_buffer.unlock();
        return;
    }

    // 定位程序需要避免缓存区数据堆积
    m_lidar_buffer.clear();
    m_time_buffer.clear();
    m_time_end_buffer.clear();

    m_lidar_buffer.push_back(ptr);
    //m_time_end_buffer.push_back(lidar_data.time);
    m_time_end_buffer.push_back(current_time); // 使用当前时间戳作为雷达帧结束时间

    // cout<<"lidar size:"<<ptr->points.size()<<endl;

    m_time_buffer.push_back(current_time - ptr->points.back().curvature / 1000.0); // 因为rslidar的时间戳是雷达帧结束时间，所以要转成开始时间.

    // double dt = (lidar_data.time - m_last_timestamp_lidar);
    // if( m_last_timestamp_lidar > 0 && (fabs(dt - 0.1) > 0.041 ) )
    // {
    //     cout << "lidar data time is abnormal, dt = "<< lidar_data.time - m_last_timestamp_lidar<< endl;
    // }

    m_last_timestamp_lidar = current_time; // 更新上次雷达数据的时间戳
    m_mtx_buffer.unlock();

    m_process_lidar = ptr; // 
}

void LocSubcribe::ProcessPoseData(const communication::PoseData &pose_data)
{
     if(m_bflag_manualpos == false)
     {
        m_manualpos.manualpos = Eigen::Vector3d(pose_data.position[0], pose_data.position[1], pose_data.position[2]);
        m_manualpos.ext_q = Eigen::Quaterniond(pose_data.orientation[0], pose_data.orientation[1],
                                            pose_data.orientation[2], pose_data.orientation[3]);
        
        m_bflag_manualpos = true;
        std::cout << "Reloc position updated: "
                << "Position: (" << m_manualpos.manualpos.x() << ", "
                << m_manualpos.manualpos.y() << ", "
                << m_manualpos.manualpos.z() << "), "
                << std::endl ;
     } 
}

void LocSubcribe::PublishDSGlobalMap(const communication::CloudNormalData::CLOUD_NormalType_PTR& DSpointcloudmap)
{
    if (DSpointcloudmap->points.empty()) 
    {
        std::cout << "DS Global Map is empty, not publishing." << std::endl;
        return;
    }
    communication::CloudData cloud_data = CloudNormalDataToCloudData(DSpointcloudmap);
    // 发布点云数据
    m_pcloud_data_publisher_ptr->Publish(cloud_data);
}

void LocSubcribe::Publish_frame_world(const communication::CloudNormalData::CLOUD_NormalType_PTR &pointcloudWorld)
{
    if (pointcloudWorld->points.empty()) 
    {
        std::cout << "Point cloud world is empty, not publishing." << std::endl;
        return;
    }

    communication::CloudData cloud_data = CloudNormalDataToCloudData(pointcloudWorld);
    cloud_data.time = m_lidar_end_time; // 设置点云数据的时间戳为雷达结束时间

    // 发布点云数据
    m_pcloud_world_publisher_ptr->Publish(cloud_data);
}

void LocSubcribe::Publish_frame_body(const communication::CloudNormalData::CLOUD_NormalType_PTR &pointcloudBody)
{
    if (pointcloudBody->points.empty()) 
    {
        std::cout << "Point cloud body is empty, not publishing." << std::endl;
        return;
    }

    communication::CloudData cloud_data = CloudNormalDataToCloudData(pointcloudBody);
    cloud_data.time = m_lidar_end_time; // 设置点云数据的时间戳为雷达结束时间

    // 发布点云数据
    m_pcloud_body_publisher_ptr->Publish(cloud_data);
}

void LocSubcribe::Publish_LocalMap(const communication::CloudNormalData::CLOUD_NormalType_PTR &local_map)
{
    // if (local_map->points.empty()) 
    // {
    //     std::cout << "Local map is empty, not publishing." << std::endl;
    //     return;
    // }

    communication::CloudData cloud_data = CloudNormalDataToCloudData(local_map);
    cloud_data.time = GetCurrentSeconds(); // 设置点云数据的时间戳为雷达结束时间

    // 发布点云数据
    m_local_map_publisher_ptr->Publish(cloud_data);
}

void LocSubcribe::PublishOdometry(const communication::PoseVelData &odometry_data)
{
    // 发布里程计数据
    m_podometry_publisher_ptr->Publish(odometry_data);
}

void LocSubcribe::Publish_TFData(const communication::PoseData &tf_data)
{
    m_tf_data_publisher_ptr->Publish(tf_data);
}

void LocSubcribe::Publish_Path(const communication::PathData &path_data)
{
    // 发布路径数据
    m_path_data_publisher_ptr->Publish(path_data);
}

void LocSubcribe::PublishGnssPath()
{
    if (m_path_data.poses_.empty()) 
    {
        std::cout << "GNSS path is empty, not publishing." << std::endl;
        return;
    }
    // 发布GNSS路径数据
    m_gnss_path_publisher_ptr->Publish(m_path_data);
}


communication::CloudData LocSubcribe::CloudNormalDataToCloudData(const communication::CloudNormalData::CLOUD_NormalType_PTR &cloud_normal_data)
{
    communication::CloudData cloud_data;
    cloud_data.time = GetCurrentSeconds(); // 替换为获取系统时间的函数
    cloud_data.cloud_ptr.reset(new pcl::PointCloud<pcl::PointXYZI>());
    
    for (size_t i = 0; i < cloud_normal_data->points.size(); ++i) 
    {
        communication::CloudData::POINT point;
        point.x = cloud_normal_data->points[i].x;
        point.y = cloud_normal_data->points[i].y;
        point.z = cloud_normal_data->points[i].z;
        point.intensity = cloud_normal_data->points[i].intensity; // 使用强度值
        cloud_data.cloud_ptr->points.push_back(point);
    }
    return cloud_data;
}

bool LocSubcribe::TryGetOneLidarFrame(MeasureGroup& meas) 
{
    /* 如果lidar帧的时间戳晚于上一次包结束时间,说明lidar数据延迟太大,导致上一次打包时判定lidar为故障,
     * 因此需要丢弃过时的lidar帧 */
    while ((!m_lidar_buffer.empty()) && (m_time_end_buffer.front() <= m_last_time_packed))
    {
        cout << setprecision(17);
        cout << "雷达队列长度 : " << m_lidar_buffer.size() << endl;
        cout << "*********雷达帧尾:" << m_time_end_buffer.front() << endl;
        cout << "*********上一次打包结束时间:" << m_last_time_packed << endl;
        cout << "雷达帧尾 - 上一次打包结束时间" << m_time_end_buffer.front() - m_last_time_packed << endl;
        m_lidar_buffer.pop_front();
        m_time_buffer.pop_front();
        m_time_end_buffer.pop_front();
    }

    if (m_lidar_buffer.empty()) 
    {
        return false;
    }

    meas.lidar = m_lidar_buffer.front();
    meas.lidar_beg_time = m_time_buffer.front();

    m_lidar_buffer.pop_front();
    m_time_buffer.pop_front();
    m_time_end_buffer.pop_front();

    if (meas.lidar->points.size() <= 5) 
    {
        m_lidar_end_time = meas.lidar_beg_time + m_lidar_mean_scantime;
        std::cout << "Warning: Too few lidar points!" << std::endl;
    }
    else if (meas.lidar->points.back().curvature / double(1000) < 0.5 * m_lidar_mean_scantime)
    {
        m_lidar_end_time = meas.lidar_beg_time + m_lidar_mean_scantime;
    }
    else
    {
        // scan_num ++;
        m_lidar_end_time = meas.lidar_beg_time + meas.lidar->points.back().curvature / double(1000);
        // lidar_mean_scantime += (meas.lidar->points.back().curvature / double(1000) - lidar_mean_scantime) / scan_num;
        m_lidar_mean_scantime = 0.1;
    }

    return true;
}

bool LocSubcribe::SyncData(MeasureGroup& meas)
{
    if (!m_first_lidar) 
    {
        return false;
    }

    if (!m_initialized_last_time) 
    {
        m_last_time_packed = GetCurrentSeconds();  // 替换为获取系统时间的函数
        cout << setprecision(17);
        cout << "第一次last_time_packed:" << m_last_time_packed << endl;
        meas.package_end_time = m_last_time_packed;
        m_initialized_last_time = true;
        return false;
    }

    // 获取一帧雷达数据
    static bool lidar_pushed = false;
    static bool flag_roswarn_lidar = false;
    if (!lidar_pushed && !m_lidar_buffer.empty()) 
    {
        //cout<<"SyncData: 获取一帧雷达数据"<<endl;
        if (TryGetOneLidarFrame(meas)) 
        {
            meas.lidar_end_time = m_lidar_end_time;
            meas.lidar_vaild = true;
            lidar_pushed = true;
            m_package_end_time_last = meas.package_end_time;
            meas.package_end_time = meas.lidar_end_time;

            if (flag_roswarn_lidar == true)
            {
                cout<<"Lidar Data Recovery!"<<endl;
                flag_roswarn_lidar = false;
            }
        }
        else 
        {
            double current_time = GetCurrentSeconds();
            if ((current_time - m_last_time_packed) > (m_time_interval + 0.1)) 
            {
                cout << setprecision(17);
                cout << "故障当前时间：" << current_time << endl;
                if (flag_roswarn_lidar == false && m_bimu_vaild)
                {
                    cout<<"Lidar Data Error !"<<endl;
                    flag_roswarn_lidar = true;
                }

                lidar_pushed = true;
                // 雷达数据指向空指针
                PointCloudXYZI::Ptr point_cloud_tmp(new PointCloudXYZI());
                meas.lidar = point_cloud_tmp;
                meas.lidar_vaild = false;
                meas.lidar_beg_time = meas.package_end_time;
                meas.lidar_end_time = meas.package_end_time + m_time_interval;
                m_package_end_time_last = meas.package_end_time;
                meas.package_end_time += m_time_interval;
            }
        }
    }

    /* 如果lidar还没取出(或判断为故障) */
    if (!lidar_pushed) 
    {
        return false;
    }

    // 获取IMU数据
    if (m_bimu_vaild)
    {
        //cout<<"SyncData: 获取IMU数据"<<endl;
        // 判断imu异常(lidar打包后0.5s都没有接收到imu数据)
        if ((GetCurrentSeconds() - meas.package_end_time) > 0.5)
        {
            cout << "Syc imudata error time = " << GetCurrentSeconds() << endl;
            cout << "dt:" << GetCurrentSeconds() - meas.package_end_time << endl;
            cout<<"IMU data error!!!"<<endl;
            m_bimu_vaild = false;
            lidar_pushed = false; // imu异常需要把lidar pop出去，否则会一直卡在这里
            if (m_deq_IMUData.empty())
            {
                cout << "imu队列空的" << endl;
                m_dimu_fault_time = meas.package_end_time;
            }
            else
                m_dimu_fault_time = m_deq_IMUData.back().time;
            // flg_exit = true;
            return false;
        }
    }
    else
    {
        lidar_pushed = false;
        return false;
    }

     // 确保具备提取imu数据的条件，即最后一个imu时间大于包的结束时间
    if (m_dlast_timestamp_imu <= meas.package_end_time )
        return false;

    /*** push imu data, and pop from imu buffer ***/
    if (m_deq_IMUData.empty())
        return false;
    double imu_time = m_deq_IMUData.front().time;
    meas.imu.clear();
    while (!m_deq_IMUData.empty() && (imu_time <= meas.package_end_time)) 
    {
        double imu_time = m_deq_IMUData.front().time;  // 使用IMUData结构的时间戳
        if (imu_time > meas.package_end_time) {
            break;
        }
        meas.imu.push_back(m_deq_IMUData.front());
        m_deq_IMUData.pop_front();
    }

    // 获取腿式里程计数据
    // if (!m_leg_buffer.empty()) 
    // {
    //     meas.leg.clear();
    //     while (!m_leg_buffer.empty()) 
    //     {
    //         double leg_time = m_leg_buffer.front().timestamp;  // 使用自定义的里程计数据结构
    //         if (leg_time > meas.package_end_time) 
    //         {
    //             break;
    //         }
    //         meas.leg.push_back(m_leg_buffer.front());
    //         m_leg_buffer.pop_front();
    //     }
    //     meas.leg_vaild = !meas.leg.empty();
    // }

    //zero velocity检测
    bool zero_vel = false;
    if (m_bzero_detect_enable)
    {
        zero_vel = zero_vel_detect.get_stop_status();
        if(zero_vel != meas.zero_vel_vaild)
        {
            cout << "zero_vel_ :" << zero_vel << endl;
        }
    }
    meas.zero_vel_vaild  = zero_vel;

     // 判断leg数据是否有效
    if (!meas.leg.empty())
    {
        meas.leg_vaild = true;
    }
    else
    {
        meas.leg_vaild = false;
    }


    lidar_pushed = false;
    m_last_time_packed = meas.package_end_time;

    return true;
}

#ifdef WIN32
void LocSubcribe::MainLoop(PTP_CALLBACK_INSTANCE Instance, PVOID pContext)
#else
void* LocSubcribe::MainLoop(void *pContext)
#endif
{
    ASSERT(pContext);

    if (NULL == pContext)
    {
#ifdef WIN32
        return;
#else
        return NULL;
#endif // WIN32
    }

    LocSubcribe* pLS = static_cast<LocSubcribe*>(pContext);

    while (pLS && (NULL != pLS->m_pThread) && (!pLS->m_pThread->IsTerminate()))
    {
        pLS->Run();
        SleepMS(5);
    }

#ifdef WIN32
        return;
#else
        return NULL;
#endif // WIN32
}

cx_int LocSubcribe::Run()
{
    UpdateIMUData();
    UpdateGnssData();
    UpdateLidarData();
    UpdateManualPos();
    return 0;
}

void LocSubcribe::UpdateIMUData()
{
    if(!m_ptr_imu_subscriber_->IsBufferEmpty())
    {
        // 如果IMU数据缓冲区为空，则从订阅者获取数据
        std::deque<communication::IMUData> IMUData_Deq = m_ptr_imu_subscriber_->GetBuffer(true);
        for (auto& imu_data : IMUData_Deq) 
        {
            ProcessImuData(imu_data);
        }
    }
}

void LocSubcribe::UpdateGnssData()
{
    if(!m_ptr_gnss_subscriber_->IsBufferEmpty())
    {
        std::deque<communication::GNSSData> GnssData_Deq = m_ptr_gnss_subscriber_->GetBuffer(true);
        for (const auto& gnss_data : GnssData_Deq) 
        {
            ProcessGnssData(gnss_data);
        }
    } 
}

void LocSubcribe::UpdateLidarData()
{
    if(!m_ptr_lidar_subscriber_->IsBufferEmpty())
    {
        std::deque<communication::CloudXYZRIData> LidarData_Deq = m_ptr_lidar_subscriber_->GetBuffer(true);
        for (const auto& lidar_data : LidarData_Deq) 
        {
            // 处理LiDAR数据
            ProcessLidarData(lidar_data);
        }
    }
}

void LocSubcribe::UpdateManualPos()
{
    if(!m_ptr_manaul_pose_data_subscriber->IsBufferEmpty())
    {
        printf("UpdateManualPos\n");
        // 如果手动位置数据缓冲区为空，则从订阅者获取数据
        std::deque<communication::PoseData> PoseData_Deq = m_ptr_manaul_pose_data_subscriber->GetBuffer(true);
        communication::PoseData pose_data = PoseData_Deq.back();
        ProcessPoseData(pose_data);
    }
}

void LocSubcribe::UpdateWebPos()
{
    if(!m_ptr_web_pose_data_subscriber->IsBufferEmpty())
    {
        printf("UpdateWebPos\n");
        // 如果手动位置数据缓冲区为空，则从订阅者获取数据
        std::deque<communication::PoseData> PoseData_Deq = m_ptr_web_pose_data_subscriber->GetBuffer(true);
        communication::PoseData pose_data = PoseData_Deq.back();
        ProcessPoseData(pose_data);
    }
}

bool LocSubcribe::IsNeedReloc()
{
    bool need_reloc = false;
    if(m_bflag_manualpos == true)
    {
        need_reloc = m_bflag_manualpos;
        m_bflag_manualpos = false; // 重置标志位
        if (need_reloc)
        {
            std::cout << "Manual relocation is needed." << std::endl;   
        }
        // else
        // {
        //     std::cout << "No manual relocation needed." << std::endl;
        // }
    }
    return need_reloc;
}

bool LocSubcribe::IsGnssDataReady()
{
    m_mtx_buffer.lock();
    bool gnss_ready = m_bGnssDataReady;
    if (gnss_ready)
    {
        m_bGnssDataReady = false; // 重置标志位
    }
    m_mtx_buffer.unlock();
    if (gnss_ready)
    {
        std::cout << "GNSS data is ready." << std::endl;   
    }
    // else
    // {
    //     std::cout << "GNSS data is not ready." << std::endl;
    // }
    return gnss_ready;
}

ManualPos LocSubcribe::GetManualPos()
{
    return m_manualpos;
}

GnssENU LocSubcribe::GetGNSSPoseData()
{
    GnssENU posdata = m_deq_GnssData.back();
    return posdata;
}

PointCloudXYZI::Ptr LocSubcribe::GetProcessLidarData()
{
    return m_process_lidar;
}

LocSubcribe *GetSingleton4LocSubcribe()
{
    static CXSingleton<LocSubcribe>   s_LocSubcribe;
    return s_LocSubcribe.GetSingletonInstance();
}
