#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <pcl_conversions/pcl_conversions.h>

#include "ros2/core/subscriber_base_ros2.h"
#include "data_types/lidar_point_cloud.h"

namespace communication::ros2
{

    class CloudDataSubscriberRos2 : public SubscriberBaseRos2<CloudData, sensor_msgs::msg::PointCloud2>
    {
    public:
        CloudDataSubscriberRos2(rclcpp::Node::SharedPtr node,
                                const std::string &topic,
                                // typename SubscriberBaseRos2<CloudData, sensor_msgs::msg::PointCloud2>::CallbackType callback = nullptr,
                                size_t max_buffer_size = 10)
            : SubscriberBaseRos2<CloudData, sensor_msgs::msg::PointCloud2>(node, topic, max_buffer_size)
        {
        }

    protected:
        virtual void FromMsg(const sensor_msgs::msg::PointCloud2 &msg, CloudData &data) override
        {
            data.time = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9;
            // data.frame_id = msg.header.frame_id;

            // if (data.cloud_ptr == nullptr)
            // {
            //     data.cloud_ptr.reset(new pcl::PointCloud<pcl::PointXYZ>);
            // }
            pcl::fromROSMsg(msg, *data.cloud_ptr);
        }
    };

} // namespace communication::ros2

#endif
