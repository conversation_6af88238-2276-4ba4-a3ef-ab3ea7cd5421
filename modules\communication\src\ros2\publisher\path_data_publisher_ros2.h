#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <nav_msgs/msg/path.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>

#include "ros2/core/publisher_base_ros2.h"
#include "data_types/path_data.h"
#include "data_types/pose_data.h"

namespace communication::ros2
{

    class PathDataPublisherRos2 : public PublisherBaseRos2<PathData, nav_msgs::msg::Path>
    {
    public:
        PathDataPublisherRos2(rclcpp::Node::SharedPtr node,
                              const std::string &topic,
                              const std::string &frame_id = "map",
                              size_t max_buffer_size = 10)
            : PublisherBaseRos2<PathData, nav_msgs::msg::Path>(node, topic, max_buffer_size),
              frame_id_(frame_id) {}

    protected:
        virtual void ToMsg() override
        {
            // Clear previous data
            this->msg_.poses.clear();

            // Set header
            this->msg_.header.stamp = rclcpp::Time(static_cast<int64_t>(this->data_.time_ * 1e9));
            this->msg_.header.frame_id = frame_id_;

            // Convert each pose in the path
            for (const auto &pose : this->data_.poses_)
            {
                geometry_msgs::msg::PoseStamped pose_msg;
                pose_msg.header.stamp = rclcpp::Time(static_cast<int64_t>(pose.time * 1e9));
                pose_msg.header.frame_id = frame_id_;

                pose_msg.pose.position.x = pose.position(0);
                pose_msg.pose.position.y = pose.position(1);
                pose_msg.pose.position.z = pose.position(2);

                pose_msg.pose.orientation.x = pose.orientation(0);
                pose_msg.pose.orientation.y = pose.orientation(1);
                pose_msg.pose.orientation.z = pose.orientation(2);
                pose_msg.pose.orientation.w = pose.orientation(3);

                this->msg_.poses.push_back(pose_msg);
            }
        }

    private:
        std::string frame_id_;
    };

} // namespace communication::ros2

#endif
