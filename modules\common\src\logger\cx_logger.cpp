#include "cx_logger.h"

#include <fcntl.h>
#include <sys/stat.h>
#include <unistd.h>

#include <iostream>
#include <mutex>
namespace common_lib {
cx_logger &cx_logger::getInstance() {
  static std::mutex mutex;               // 定义静态mutex
  static cx_logger *instance = nullptr;  // 使用指针来延迟实例化

  if (instance == nullptr) {                  // 检查实例是否为空
    std::lock_guard<std::mutex> lock(mutex);  // 使用lock_guard来锁定mutex
    if (instance == nullptr) {                // 再次检查实例是否为空
      instance = new cx_logger();             // 创建实例
    }
  }
  return *instance;  // 返回实例引用
}

void cx_logger::Init(const std::string &config_file_path, const std::string &out_file_path,
                     const std::string &modules) {
  // 创建日志文件夹
  std::string outlog_file_path = out_file_path + "/outlogs_" + modules;
  std::cout << "outlog_file_path:" << outlog_file_path << std::endl;
  mkdir(outlog_file_path.c_str(), 0755);
#ifdef USE_LOG4CPLUS
  log4cplus::Initializer initializer();
  std::cout << "config_file_path:" << config_file_path << std::endl;
  log4cplus::PropertyConfigurator::doConfigure(LOG4CPLUS_TEXT(config_file_path));

  // 生成日志文件名
  auto now = std::chrono::system_clock::now();
  std::time_t now_time = std::chrono::system_clock::to_time_t(now);
  std::tm now_tm = *std::localtime(&now_time);

  // 日志文件名处理
  std::ostringstream oss;
  char time_str[20];
  std::strftime(time_str, sizeof(time_str), "%Y%m%d%H%M%S", &now_tm);
  // 拼接日志文件路径和文件名，确保路径以斜杠结尾
  std::string log_file_path;
  if (out_file_path.empty() || out_file_path.back() != '/') {
    log_file_path = out_file_path + "/" + modules;
  }
  oss << log_file_path << "_log_" << time_str << ".log";
  log_file_path = oss.str();

  // 打印日志文件路径以便调试
  std::cout << "log_file_path: " << log_file_path << std::endl;

  // 验证路径是否存在并可写
  struct stat info;
  if (stat(out_file_path.c_str(), &info) != 0 || !S_ISDIR(info.st_mode)) {
    if (mkdir(out_file_path.c_str(), 0755) != 0) {  // 如果目录不存在，则尝试创建
      std::cerr << "Failed to create directory: " << out_file_path << std::endl;
      return;  // 创建失败
    }
  }

  // 设置文件名模式
  log4cplus::tstring file_name_pattern = LOG4CPLUS_C_STR_TO_TSTRING(log_file_path);
  log4cplus::SharedAppenderPtr appender(new log4cplus::RollingFileAppender(
      file_name_pattern,
      5 * 1024 * 1024,  // MaxFileSize（从配置文件读取的占位值，实际会被覆盖）
      3,                // MaxBackupIndex
      true,             // immediateFlush
      true              // append
      ));
  // 设置 layout
  log4cplus::tstring pattern = LOG4CPLUS_TEXT("%d{%Y-%m-%d %H:%M:%S} [%t] %-5p %c - %m%n");
  std::unique_ptr<log4cplus::Layout> layout(new log4cplus::PatternLayout(pattern));
  appender->setLayout(std::move(layout));

  // 2. 获取 Root Logger
  log4cplus::Logger logger = log4cplus::Logger::getInstance(modules);
  logger.addAppender(appender);
#endif
}
cx_logger::cx_logger() {}

cx_logger::~cx_logger() {
#ifdef USE_LOG4CPLUS
  log4cplus::deinitialize();
#endif
}

#ifdef USE_LOG4CPLUS
log4cplus::Logger cx_logger::getSubLogger(const std::string &logger_name) {
  return log4cplus::Logger::getInstance(logger_name);
}
#endif
}  // namespace common_lib