#ifndef PATH_FOLLOWER_H
#define PATH_FOLLOWER_H

#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <mutex>
#include <chrono>
#include <iostream>
#include <fstream>
#include <atomic>
#include <thread>

#include <yaml-cpp/yaml.h>
#include <eigen3/Eigen/Dense>

// 包含公共结构体定义
#include "common_types.h"

namespace path_follower {

// 使用公共命名空间中的类型
using planning_common::TimeStamp;
using planning_common::Header;
using planning_common::Point;
using planning_common::Vector3;
using planning_common::Quaternion;
using planning_common::Pose;
using planning_common::PoseStamped;
using planning_common::TwistMsg;
using planning_common::OdometryData;
using planning_common::PathData;
using planning_common::TwistData;
using planning_common::BoolMsg;
using planning_common::Int8Msg;

const double PI = 3.1415926;

/**
 * @brief 路径跟随器配置参数结构体
 */
struct PathFollowerConfig {
    // 传感器配置
    double sensorOffsetX;
    double sensorOffsetY;
    
    // 发布控制
    int pubSkipNum;
    
    // 驱动模式
    bool twoWayDrive;
    
    // 控制参数
    double lookAheadDis;
    double yawRateGain;
    double stopYawRateGain;
    double maxYawRate;
    double maxSpeed;
    double maxAccel;
    
    // 切换参数
    double switchTimeThre;
    double dirDiffThre;
    double stopDisThre;
    double slowDwnDisThre;
    
    // 倾斜检测参数
    bool useInclRateToSlow;
    double inclRateThre;
    double slowRate1;
    double slowRate2;
    double slowTime1;
    double slowTime2;
    
    bool useInclToStop;
    double inclThre;
    double stopTime;
    
    // 行为参数
    bool noRotAtGoal;
    
    // 默认构造函数
    PathFollowerConfig() {
        // 设置默认值（与原pathFollower.cpp一致）
        sensorOffsetX = 0.0;
        sensorOffsetY = 0.0;
        pubSkipNum = 1;
        twoWayDrive = false;
        lookAheadDis = 0.5;
        yawRateGain = 1.0;
        stopYawRateGain = 1.0;
        maxYawRate = 30.0;
        maxSpeed = 0.8;
        maxAccel = 3.0;
        switchTimeThre = 1.0;
        dirDiffThre = 0.1;
        stopDisThre = 0.2;
        slowDwnDisThre = 1.0;
        useInclRateToSlow = false;
        inclRateThre = 120.0;
        slowRate1 = 0.25;
        slowRate2 = 0.5;
        slowTime1 = 2.0;
        slowTime2 = 2.0;
        useInclToStop = false;
        inclThre = 45.0;
        stopTime = 5.0;
        noRotAtGoal = true;
    }
};

// 回调函数类型定义
using OdometryCallback = std::function<void(const std::shared_ptr<OdometryData>&)>;
using PathCallback = std::function<void(const std::shared_ptr<PathData>&)>;
using BoolCallback = std::function<void(const std::shared_ptr<BoolMsg>&)>;
using Int8Callback = std::function<void(const std::shared_ptr<Int8Msg>&)>;
using TwistCallback = std::function<void(const std::shared_ptr<TwistData>&)>;

/**
 * @brief 去ROS化的路径跟随器类
 */
class PathFollower {
public:
    // 构造函数和析构函数
    PathFollower(const std::string& configPath = "");
    ~PathFollower();

    // 初始化函数
    bool initialize();
    bool loadConfig(const std::string& configPath);

    // 数据输入接口
    void updateOdometry(const OdometryData& odom);
    void updatePath(const PathData& path);
    void updateMode(const BoolMsg& mode);
    void updateGoal(const PoseStamped& goal);
    void updateWebGoal(const PoseStamped& goal);
    void updateStop(const Int8Msg& stop);

    // 输出回调设置
    void setSpeedPublishCallback(TwistCallback callback);

    // 控制接口
    void start();
    void stop();

    // 参数设置
    void setMaxSpeed(double speed);
    void setLookAheadDistance(double distance);
    void setTwoWayDrive(bool enable);
    void setNoRotationAtGoal(bool enable);
    void setYawRateGain(double gain);
    void setMaxYawRate(double rate);
    void setMaxAcceleration(double accel);

    // 状态查询
    bool isInitialized() const;
    bool isRunning() const;
    bool isPathValid() const;
    bool hasReachedGoal() const;
    double getDistanceToGoal() const;
    double getCurrentSpeed() const;
    double getCurrentYawRate() const;
    int getCurrentPathPointID() const;
    PathFollowerConfig getConfig() const;
    TwistData getCmdVel() const {return cmd_vel;}

    // 打印状态
    void printStatus();

    // 主循环方法
    void controlLoop();

private:
    // 配置参数
    PathFollowerConfig config_;
    std::string configPath_;

    // 运行状态
    bool initialized_;
    std::atomic<bool> running_;
    std::thread worker_thread_;
    std::mutex dataMutex_;

    // 回调函数
    TwistCallback speedPublishCallback_;

    // 原始变量保留 (与原pathFollower.cpp中的全局变量对应)
    bool adjustmode;
    double goalX, goalY, goalZ;
    int nav_start;
    double modifySpeed;
    float joyYaw;
    float vehicleX, vehicleY, vehicleZ;
    float vehicleRoll, vehiclePitch, vehicleYaw;
    float vehicleXRec, vehicleYRec, vehicleZRec;
    float vehicleRollRec, vehiclePitchRec, vehicleYawRec;
    float vehicleYawRate, vehicleSpeed;
    double odomTime;
    double slowInitTime;
    double stopInitTime;
    int pathPointID;
    bool pathInit;
    bool navFwd;
    double switchTime;
    int safetyStop;
    PathData path;
    bool rotinit;
    double odometryTime;
    int pubSkipCount;

    TwistData cmd_vel;

    // 私有方法 (保留原始函数功能)
    void odomHandler(const std::shared_ptr<OdometryData>& odom);
    void pathHandler(const std::shared_ptr<PathData>& path);
    void modeHandler(const std::shared_ptr<BoolMsg>& mode);
    void goalHandler(const std::shared_ptr<PoseStamped>& goal);
    void webgoalHandler(const std::shared_ptr<PoseStamped>& goal);
    void stopHandler(const std::shared_ptr<Int8Msg>& stop);

    // 工具方法
    double getCurrentTime();
    void initializeVariables();
    bool loadYamlConfig(const std::string& configPath);

    // 主要控制算法
    void performPathFollowing();
};

} // namespace path_follower

#endif // PATH_FOLLOWER_H
