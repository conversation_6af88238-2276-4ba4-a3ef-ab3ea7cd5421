#ifndef VELOCITY_SMOOTHER_H_
#define VELOCITY_SMOOTHER_H_

/*****************************************************************************
 ** Includes
 *****************************************************************************/

#include <string>
#include <vector>
#include <mutex>
#include <thread>
#include <chrono>
#include <functional>
#include <memory>
#include <iostream>
#include <algorithm>
#include <cmath>
#include <atomic>

// YAML解析库 (可选)
#ifdef USE_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

/*****************************************************************************
 ** Preprocessing
 *****************************************************************************/

#define PERIOD_RECORD_SIZE    5
#define ZERO_VEL_COMMAND      TwistMsg()
#define IS_ZERO_VEOCITY(a)   ((a.linear.x == 0.0) && (a.angular.z == 0.0))

/*****************************************************************************
** 数据结构定义
*****************************************************************************/

/**
 * @brief Vector3数据结构
 */
struct Vector3 {
    double x, y, z;
    
    Vector3() : x(0.0), y(0.0), z(0.0) {}
    Vector3(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
    
    Vector3& operator=(const Vector3& other) {
        x = other.x;
        y = other.y;
        z = other.z;
        return *this;
    }
    
    bool operator==(const Vector3& other) const {
        return (x == other.x) && (y == other.y) && (z == other.z);
    }
    
    bool operator!=(const Vector3& other) const {
        return !(*this == other);
    }
};

/**
 * @brief Twist数据结构
 */
struct TwistMsg {
    Vector3 linear;
    Vector3 angular;
    
    TwistMsg() {}
    TwistMsg(double linear_x, double angular_z) {
        linear.x = linear_x;
        angular.z = angular_z;
    }
    
    TwistMsg& operator=(const TwistMsg& other) {
        linear = other.linear;
        angular = other.angular;
        return *this;
    }
    
    bool operator==(const TwistMsg& other) const {
        return (linear == other.linear) && (angular == other.angular);
    }
    
    bool operator!=(const TwistMsg& other) const {
        return !(*this == other);
    }
};

/**
 * @brief Odometry数据结构
 */
struct OdometryMsg {
    struct Header {
        double stamp;
        std::string frame_id;
        Header() : stamp(0.0), frame_id("odom") {}
    } header;
    
    struct PoseWithCovariance {
        struct Pose {
            Vector3 position;
            struct Quaternion {
                double x, y, z, w;
                Quaternion() : x(0), y(0), z(0), w(1) {}
            } orientation;
        } pose;
        std::vector<double> covariance; // 6x6 covariance matrix
    } pose;
    
    struct TwistWithCovariance {
        TwistMsg twist;
        std::vector<double> covariance; // 6x6 covariance matrix
    } twist;
    
    OdometryMsg() {
        pose.covariance.resize(36, 0.0);
        twist.covariance.resize(36, 0.0);
    }
};

/**
 * @brief 时间结构
 */
class TimeStamp {
public:
    TimeStamp() : time_point_(std::chrono::steady_clock::now()) {}
    
    static TimeStamp now() {
        return TimeStamp();
    }
    
    double toSec() const {
        auto duration = time_point_.time_since_epoch();
        return std::chrono::duration<double>(duration).count();
    }
    
    TimeStamp operator-(const TimeStamp& other) const {
        TimeStamp result;
        result.time_point_ = time_point_ - (other.time_point_ - std::chrono::steady_clock::time_point{});
        return result;
    }
    
private:
    std::chrono::steady_clock::time_point time_point_;
};

/**
 * @brief 参数配置结构
 */
struct ParamsConfig {
    double speed_lim_v;
    double speed_lim_w;
    double accel_lim_v;
    double accel_lim_w;
    double decel_factor;
    
    ParamsConfig() 
        : speed_lim_v(1.0), speed_lim_w(1.0), accel_lim_v(1.0), 
          accel_lim_w(1.0), decel_factor(1.0) {}
};

/**
 * @brief 动态重配置服务器
 */
class DynamicReconfigureServer {
public:
    using CallbackType = std::function<void(ParamsConfig&, uint32_t)>;
    
    DynamicReconfigureServer() = default;
    
    void setCallback(CallbackType callback) {
        callback_ = callback;
    }
    
    void updateConfig(const ParamsConfig& config) {
        ParamsConfig mutable_config = config;
        if (callback_) {
            callback_(mutable_config, 0);
        }
    }
    
private:
    CallbackType callback_;
};

/*****************************************************************************
** Namespaces
*****************************************************************************/

namespace yocs_velocity_smoother {

/*****************************************************************************
** VelocitySmoother
*****************************************************************************/

class VelocitySmoother {
public:
    explicit VelocitySmoother(const std::string& name);

    ~VelocitySmoother() {
        if (dynamic_reconfigure_server != nullptr)
            delete dynamic_reconfigure_server;
    }

    bool init();
    bool initFromConfig(const std::string& config_file = "");
    void spin();
    void shutdown() { shutdown_req = true; }
    
    // 互斥锁
    std::mutex locker;

    // 回调接口
    void setVelocityCallback(std::function<void(const std::shared_ptr<TwistMsg>&)> callback);
    void setOdometryCallback(std::function<void(const std::shared_ptr<OdometryMsg>&)> callback);
    void setRobotVelCallback(std::function<void(const std::shared_ptr<TwistMsg>&)> callback);
    void setSmoothVelPublishCallback(std::function<void(const std::shared_ptr<TwistMsg>&)> callback);
    
    // 数据输入接口
    void inputVelocity(const TwistMsg& msg);
    void inputOdometry(const OdometryMsg& msg);
    void inputRobotVel(const TwistMsg& msg);
    
    // 参数设置接口
    void setSpeedLimits(double speed_lim_v, double speed_lim_w);
    void setAccelLimits(double accel_lim_v, double accel_lim_w);
    void setDecelFactor(double decel_factor);
    void setFrequency(double frequency);
    void setQuiet(bool quiet);
    void setRobotFeedback(int feedback_type);
    
    // 参数获取接口
    double getSpeedLimV() const { return speed_lim_v; }
    double getSpeedLimW() const { return speed_lim_w; }
    double getAccelLimV() const { return accel_lim_v; }
    double getAccelLimW() const { return accel_lim_w; }
    double getDecelFactor() const { return decel_factor; }
    double getFrequency() const { return frequency; }
    bool getQuiet() const { return quiet; }
    int getRobotFeedback() const { return static_cast<int>(robot_feedback); }
    
    // 状态查询接口
    bool isInitialized() const { return initialized_; }
    bool isInputActive() const { return input_active; }
    TwistMsg getLastCmdVel() const { return last_cmd_vel; }
    TwistMsg getCurrentVel() const { return current_vel; }
    TwistMsg getTargetVel() const { return target_vel; }
    
    // 运行控制接口
    void start();
    void stop();
    bool isRunning() const { return running_; }

private:
    enum RobotFeedbackType {
        NONE,
        ODOMETRY,
        COMMANDS
    } robot_feedback;  /**< What source to use as robot velocity feedback */

    // 回调函数
    void velocityCB(const std::shared_ptr<const TwistMsg>& msg);
    void robotVelCB(const std::shared_ptr<const TwistMsg>& msg);
    void odometryCB(const std::shared_ptr<const OdometryMsg>& msg);
    
    // 辅助函数
    double sign(double x) { return x < 0.0 ? -1.0 : +1.0; }
    double median(std::vector<double> values);
    void reconfigCB(ParamsConfig &config, uint32_t unused_level);
    
    // 配置相关函数
    bool loadConfiguration(const std::string& config_file);
    bool loadConfigurationFromYAML(const std::string& yaml_file);
    bool loadConfigurationFromText(const std::string& config_file);
    void setDefaultConfiguration();
    void applyConfiguration();
    void validateParameters();
    void printStatus() const;

    // 成员变量
    std::string name;
    bool quiet;
    bool shutdown_req;
    bool input_active;
    int pr_next;
    DynamicReconfigureServer* dynamic_reconfigure_server;
    bool initialized_;
    bool running_;
    bool thread_running_;
    std::thread worker_thread_;
    
    // 速度相关参数
    double speed_lim_v;
    double speed_lim_w;
    double accel_lim_v;
    double accel_lim_w;
    double decel_factor;
    double decel_lim_v;
    double decel_lim_w;
    double frequency;
    
    // 时间相关变量
    TimeStamp last_cb_time;
    std::vector<double> period_record;
    double cb_avg_time;
    
    // 速度相关变量
    TwistMsg target_vel;
    TwistMsg current_vel;
    TwistMsg last_cmd_vel;
    
    // 回调函数
    std::function<void(const std::shared_ptr<TwistMsg>&)> velocity_callback_;
    std::function<void(const std::shared_ptr<OdometryMsg>&)> odometry_callback_;
    std::function<void(const std::shared_ptr<TwistMsg>&)> robot_vel_callback_;
    std::function<void(const std::shared_ptr<TwistMsg>&)> smooth_vel_publish_callback_;
    std::function<void(ParamsConfig&, uint32_t)> dynamic_reconfigure_callback;
};

} // namespace yocs_velocity_smoother

#endif // VELOCITY_SMOOTHER_H_
