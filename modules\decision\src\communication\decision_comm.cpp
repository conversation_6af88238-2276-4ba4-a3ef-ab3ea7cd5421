#include "decision_comm.h"
#include "yaml_config.h"

namespace decision{

DecisionComm::DecisionComm(const std::string& config_file_path){

    //load config 
    common_lib::YamlConfig& config = common_lib::YamlConfig::GetInstance();
    config.LoadFile(config_file_path);

    std::string module_name = config.GetParam<std::string>("module", "decision_node");
    std::string imu_topic = config.GetParam<std::string>("topics/imu", "/imu");
    std::string terrain_topic = config.GetParam<std::string>("topics/terrain", "/terrain");
    std::string odometry_topic = config.GetParam<std::string>("topics/localization", "/Odometry");
    std::string global_path_topic = config.GetParam<std::string>("topics/global_path", "/path");
    std::string decision_topic = config.GetParam<std::string>("topics/decision_out", "/decision/decision_out");

    communication_ptr_ = std::make_shared<communication::Communication>(module_name);
    communication_ptr_->Initialize(config_file_path);
    imu_subscriber_ptr_ = communication_ptr_->CreateImuDataSubscriber(imu_topic);
    terrain_subscriber_ptr_ = communication_ptr_->CreateCloudDataSubscriber(terrain_topic);
    localization_subscriber_ptr_ = communication_ptr_->CreateOdometrySubscriber(odometry_topic);
    globalpath_subscriber_ptr_ = communication_ptr_->CreatePathDataSubscriber(global_path_topic);

    decision_out_publisher_ptr_ = communication_ptr_->CreateDecisionOutPublisher(decision_topic);
}

bool DecisionComm::SubscribImuData(std::deque<IMUData>& dq_imu_data){
    dq_imu_data = imu_subscriber_ptr_->GetBuffer();
    return !dq_imu_data.empty();
}

bool DecisionComm::SubscribTerrainCloudData(CloudData& cloud_data){
    auto dq_cloud_data = terrain_subscriber_ptr_->GetBuffer();
    if (!dq_cloud_data.empty()){
        cloud_data = dq_cloud_data.back();
        return true;
    }
    else{
        return false;
    }
}

bool DecisionComm::SubscribGlobalPathData(PathData& path_data)
{
    auto dq_path_data = globalpath_subscriber_ptr_->GetBuffer();
    if (!dq_path_data.empty())
    {
        path_data = dq_path_data.back();
        return true;
    }
    else
    {
        return false;
    }
}

bool DecisionComm::SubscribLocalizationData(PoseVelData &odometry)
{
    auto dq_odo_data = localization_subscriber_ptr_->GetBuffer();
    if (!dq_odo_data.empty())
    {
        odometry = dq_odo_data.back();
        return true;
    }
    else
    {
        return false;
    }
}

bool DecisionComm::PublishDecisionData(const DecisionData& decision_out)
{
    if(decision_out_publisher_ptr_){
        decision_out_publisher_ptr_->Publish(decision_out);
        return true;
    }
    return false;
}

bool DecisionComm::IsTerminated(){
    if(communication_ptr_){
        return communication_ptr_->IsTerminated();
    }
    return true;
}
void DecisionComm::ShutDown(){
    communication_ptr_->ShutDown();
}

}