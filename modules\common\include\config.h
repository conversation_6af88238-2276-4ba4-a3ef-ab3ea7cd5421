#ifndef CONFIG_H_
#define CONFIG_H_

#include <yaml-cpp/yaml.h>

#include <iostream>
#include <mutex>
#include <optional>
#include <sstream>
#include <string>
#include <vector>
namespace common_lib {

class Config {
 public:
  // Delete copy constructor and assignment operator
  Config(const Config &) = delete;
  Config &operator=(const Config &) = delete;

  // Get singleton instance
  static Config &GetInstance() {
    static Config instance;
    return instance;
  }

  // Load configuration from file
  void Init(const std::string &file_path);

  // Get value with default (returns default if path doesn't exist)
  template <typename T>
  T getParam(const std::string &path, const T &default_value) const;

 private:
  Config() = default;
  ~Config() = default;

  // Split path into tokens (supports both '/' and '.' as separators)
  std::vector<std::string> SplitPath(const std::string &path) const;

  std::string config_file_path_;
};

template <typename T>
T Config::getParam(const std::string &path, const T &default_value) const {
  T result = default_value;

  std::vector<std::string> nodes = SplitPath(path);
  const YAML::Node &current_node = YAML::LoadFile(config_file_path_);
  YAML::Node target_node = current_node;
  bool node_valid = true;

  // Traverse the path
  for (const auto &node : nodes) {
    std::cout << "Current node type: " << target_node.Type() << std::endl;
    std::cout << "Looking for key: " << node << std::endl;
    // 检查当前节点是否是映射类型
    if (!target_node.IsMap() || !target_node[node]) {
      node_valid = false;
      break;
    }
    target_node = target_node[node];
  }

  // 检查最终节点是否有效并可转换为目标类型
  if (node_valid && target_node.IsDefined() && !target_node.IsNull()) {
    result = target_node.as<T>();
  }

  return result;
}

}  // namespace common_lib

#endif  // CONFIG_H_