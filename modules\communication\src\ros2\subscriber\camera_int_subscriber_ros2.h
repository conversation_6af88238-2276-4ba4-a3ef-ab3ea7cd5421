#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/camera_info.hpp>

#include "ros2/core/subscriber_base_ros2.h"
#include "data_types/camera_info.h"

namespace communication::ros2
{

    class CameraIntSubscriberRos2 : public SubscriberBaseRos2<CameraInfo, sensor_msgs::msg::CameraInfo>
    {
    public:
        CameraIntSubscriberRos2(rclcpp::Node::SharedPtr node,
                                const std::string &topic,
                                // typename SubscriberBaseRos2<CameraInfo, sensor_msgs::msg::CameraInfo>::CallbackType callback = nullptr,
                                size_t max_buffer_size = 10)
            : SubscriberBaseRos2<CameraInfo, sensor_msgs::msg::CameraInfo>(node, topic, max_buffer_size)
        {
        }

    protected:
        virtual void FromMsg(const sensor_msgs::msg::CameraInfo &msg, CameraInfo &data) override
        {
            // data.time = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9;
            // data.frame_id = msg.header.frame_id;

            data.width = msg.width;
            data.height = msg.height;
            data.fx = msg.k[0];
            data.fy = msg.k[4];
            data.ppx = msg.k[2];
            data.ppy = msg.k[5];
            data.valid = true;
        }
    };

} // namespace communication::ros2

#endif
