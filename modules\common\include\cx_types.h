﻿#ifndef _CX_TYPES_H_
#define _CX_TYPES_H_

#include <map>
#include <string>
#define MAX_PATH 260

typedef unsigned char cx_uint8;
typedef signed char cx_int8;
typedef unsigned short int cx_uint16;
typedef signed short int cx_int16;
typedef unsigned int cx_uint32;
typedef signed int cx_int32;
typedef unsigned long long cx_uint64;
typedef long long cx_int64;

typedef char cx_char;
typedef unsigned char cx_uchar;
typedef cx_uchar cx_byte;

typedef wchar_t cx_wchar;

typedef unsigned int cx_uint;
typedef int cx_int;
typedef unsigned short cx_ushort;
typedef short cx_short;
typedef unsigned long cx_ulong;
typedef long cx_long;
typedef unsigned long cx_ulong;
typedef unsigned long long cx_ullong;
typedef long long cx_llong;

typedef float cx_float;
typedef double cx_double;
typedef bool cx_bool;
typedef unsigned short cx_word;
typedef unsigned int cx_dword;

typedef std::uint64_t hash_t;

typedef void *cx_handle;
typedef time_t cx_time;

typedef std::basic_string<cx_char> cx_string;
typedef std::basic_string<cx_wchar> cx_wstring;

typedef std::map<std::string, std::string> MessageMap;
typedef std::map<std::string, std::string> PARAMS;

typedef void (*FUNC_CallBack)(int iEventID, int ipara1, int ipata2, int ipara3);

#ifdef LINUX

typedef void *LPVOID;

#endif

#endif  // !_cx_TYPES_H_
