# GlobalTrajecGenerate Configuration File
# 全局轨迹生成器配置文件
# 完全保留原有global_traj_generate.cpp的所有参数和功能

# 基本参数配置
indexIncrement: 20    # 向前预移动的航迹点索引增量 (原有默认值)
indexNum: 10          # 向前预移动的点增量 (原有默认值)

# 预设配置方案
presets:
  # 默认配置
  default:
    indexIncrement: 20
    indexNum: 10
    description: "默认配置，适用于一般轨迹跟踪任务"
  
  # 精确跟踪配置
  precise_tracking:
    indexIncrement: 10
    indexNum: 5
    description: "精确跟踪配置，适用于需要精确路径跟踪的场景"
  
  # 快速跟踪配置
  fast_tracking:
    indexIncrement: 30
    indexNum: 15
    description: "快速跟踪配置，适用于高速移动场景"
  
  # 平滑跟踪配置
  smooth_tracking:
    indexIncrement: 25
    indexNum: 12
    description: "平滑跟踪配置，适用于需要平滑轨迹的场景"
  
  # 密集路径配置
  dense_path:
    indexIncrement: 5
    indexNum: 3
    description: "密集路径配置，适用于路径点密集的场景"
  
  # 稀疏路径配置
  sparse_path:
    indexIncrement: 40
    indexNum: 20
    description: "稀疏路径配置，适用于路径点稀疏的场景"

# 参数说明和取值范围
parameter_info:
  indexIncrement:
    description: "向前预移动的航迹点索引增量，控制局部目标点的前瞻距离"
    type: "int"
    range: "1 - 100"
    unit: "索引数"
    default: 20
    notes:
      - "值越大，前瞻距离越远，轨迹跟踪越平滑但可能偏离路径"
      - "值越小，前瞻距离越近，轨迹跟踪越精确但可能不够平滑"
      - "建议根据路径复杂度和机器人速度调整"
    
  indexNum:
    description: "向前预移动的点增量，用于寻找最近轨迹点时的搜索步长"
    type: "int"
    range: "1 - 50"
    unit: "索引数"
    default: 10
    notes:
      - "值越大，搜索步长越大，适用于机器人快速移动的场景"
      - "值越小，搜索步长越小，适用于机器人慢速移动的场景"
      - "应该小于或等于indexIncrement的一半"

# 应用场景配置
scenarios:
  # 室内导航场景
  indoor_navigation:
    indexIncrement: 15
    indexNum: 8
    description: "室内环境导航，中等前瞻距离，适中搜索步长"
    
  # 户外导航场景
  outdoor_navigation:
    indexIncrement: 30
    indexNum: 15
    description: "户外环境导航，较大前瞻距离，适应高速移动"
    
  # 仓库作业场景
  warehouse_operation:
    indexIncrement: 10
    indexNum: 5
    description: "仓库作业，精确路径跟踪，小前瞻距离"
    
  # 巡逻场景
  patrol_mission:
    indexIncrement: 25
    indexNum: 12
    description: "巡逻任务，平滑轨迹跟踪，中等参数"
    
  # 搬运场景
  transport_mission:
    indexIncrement: 12
    indexNum: 6
    description: "搬运任务，稳定轨迹跟踪，较小参数"
    
  # 探索场景
  exploration_mission:
    indexIncrement: 35
    indexNum: 18
    description: "探索任务，大前瞻距离，适应未知环境"

# 路径类型优化配置
path_types:
  # 直线路径
  straight_line:
    indexIncrement: 30
    indexNum: 15
    description: "直线路径优化，大前瞻距离提高效率"
    
  # 曲线路径
  curved_path:
    indexIncrement: 15
    indexNum: 8
    description: "曲线路径优化，中等参数保证跟踪精度"
    
  # 复杂路径
  complex_path:
    indexIncrement: 10
    indexNum: 5
    description: "复杂路径优化，小参数保证精确跟踪"
    
  # S形路径
  s_shaped_path:
    indexIncrement: 12
    indexNum: 6
    description: "S形路径优化，适中参数平衡精度和平滑度"
    
  # 螺旋路径
  spiral_path:
    indexIncrement: 8
    indexNum: 4
    description: "螺旋路径优化，小参数适应急转弯"

# 机器人速度适配配置
robot_speed:
  # 低速 (0-0.5 m/s)
  low_speed:
    indexIncrement: 8
    indexNum: 4
    description: "低速移动配置，小前瞻距离精确跟踪"
    
  # 中速 (0.5-1.5 m/s)
  medium_speed:
    indexIncrement: 20
    indexNum: 10
    description: "中速移动配置，默认参数平衡性能"
    
  # 高速 (1.5-3.0 m/s)
  high_speed:
    indexIncrement: 35
    indexNum: 18
    description: "高速移动配置，大前瞻距离保证稳定性"
    
  # 极高速 (>3.0 m/s)
  very_high_speed:
    indexIncrement: 50
    indexNum: 25
    description: "极高速移动配置，最大前瞻距离"

# 环境复杂度配置
environment_complexity:
  # 简单环境
  simple_environment:
    indexIncrement: 30
    indexNum: 15
    description: "简单环境，大参数提高效率"
    
  # 中等复杂环境
  medium_complexity:
    indexIncrement: 20
    indexNum: 10
    description: "中等复杂环境，默认参数"
    
  # 复杂环境
  complex_environment:
    indexIncrement: 12
    indexNum: 6
    description: "复杂环境，小参数保证精度"
    
  # 极复杂环境
  very_complex:
    indexIncrement: 8
    indexNum: 4
    description: "极复杂环境，最小参数最大精度"

# 调试和测试配置
debug:
  enable_logging: true
  log_level: "INFO"  # DEBUG, INFO, WARN, ERROR
  log_file: "global_trajec_generate.log"
  print_trajectory_info: true
  print_local_goal_info: true
  
test:
  enable_test_mode: false
  test_trajectory_points:
    - {x: 0.0, y: 0.0, z: 0.0, yaw: 0.0}
    - {x: 1.0, y: 0.0, z: 0.0, yaw: 0.0}
    - {x: 2.0, y: 1.0, z: 0.0, yaw: 1.57}
    - {x: 3.0, y: 2.0, z: 0.0, yaw: 1.57}
    - {x: 4.0, y: 3.0, z: 0.0, yaw: 0.0}
    - {x: 5.0, y: 3.0, z: 0.0, yaw: 0.0}

# 性能优化配置
performance:
  kdtree_search_radius: 1.0      # KD树搜索半径
  max_trajectory_points: 1000    # 最大轨迹点数量
  trajectory_simplification: false  # 是否启用轨迹简化
  memory_optimization: true      # 是否启用内存优化

# 安全配置
safety:
  max_index_increment: 100       # 最大索引增量限制
  max_index_num: 50             # 最大点增量限制
  trajectory_bounds_check: true  # 是否检查轨迹边界
  goal_distance_threshold: 0.1   # 目标点距离阈值

# 版本信息
version:
  config_version: "1.0"
  compatible_versions: ["1.0", "1.1"]
  last_modified: "2024-01-01"
  author: "GlobalTrajecGenerate NoROS Team"
  description: "去ROS化全局轨迹生成器配置文件，完全保留原有功能"

# 注意事项
notes:
  - "所有参数都完全保留了原有global_traj_generate.cpp的功能"
  - "indexIncrement控制前瞻距离，影响轨迹跟踪的平滑度"
  - "indexNum控制搜索步长，影响最近点查找的效率"
  - "参数修改后需要重新加载配置或重启节点"
  - "建议根据实际应用场景选择合适的预设配置"
  - "复杂环境建议使用较小参数，简单环境可使用较大参数"
