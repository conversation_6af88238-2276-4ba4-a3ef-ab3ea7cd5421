#pragma once
#include <pcl/point_types.h>
#include <pcl/point_cloud.h>


struct EIGEN_ALIGN16 PointXYZRI
{
  PCL_ADD_POINT4D;
  //float intensity;
  PCL_ADD_INTENSITY;
  float time;
  uint16_t ring;
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
};

POINT_CLOUD_REGISTER_POINT_STRUCT(PointXYZRI,
                                  (float, x, x)(float, y, y)(float, z, z)(float, intensity, intensity)(float, time, time)(std::uint16_t, ring, ring))

namespace communication {

struct CloudData {
    // using PointCloud = pcl::PointCloud<pcl::PointXYZI>;
    // using PointCloudPtr = PointCloud::Ptr;

    using POINT = pcl::PointXYZI;
    using CLOUD = pcl::PointCloud<POINT>;
    using CLOUD_PTR = CLOUD::Ptr;
    // using PointCloudPtr = CLOUD::Ptr;

    CloudData()
      :cloud_ptr(new CLOUD()) {
    }

    double time = 0.0;
    CLOUD_PTR cloud_ptr;
};

struct CloudNormalData {
    using PointXYZINormalType = pcl::PointXYZINormal;
    using CLOUD_NormalType = pcl::PointCloud<PointXYZINormalType>;
    using CLOUD_NormalType_PTR = CLOUD_NormalType::Ptr;

    CloudNormalData()
      :m_cloudPtr(new CLOUD_NormalType()) {
    }

    double m_time = 0.0; // time unit： s 
    CLOUD_NormalType_PTR m_cloudPtr;
};

struct CloudXYZRIData {
  // using PointCloud = pcl::PointCloud<pcl::PointXYZI>;
  // using PointCloudPtr = PointCloud::Ptr;

  // using POINT = pcl::PointXYZI;
  using CLOUD = pcl::PointCloud<PointXYZRI>;
  using CLOUD_PTR = CLOUD::Ptr;
  // using PointCloudPtr = CLOUD::Ptr;

  CloudXYZRIData()
    :cloud_ptr(new CLOUD()) {
  }

  double time = 0.0;
  CLOUD_PTR cloud_ptr;
};

//将CLOUD_NormalType转成CLOUD
// inline CloudData::CLOUD_PTR ConvertToCloudData(const CloudNormalData::CLOUD_NormalType_PTR& normal_data) {
//   CloudData::CLOUD_PTR cloud_data = std::make_shared<CloudData::CLOUD>();
//   cloud_data->points.reserve(normal_data->points.size());
//   for (const auto& point : normal_data->points) {
//       CloudData::POINT new_point;
//       new_point.x = point.x;
//       new_point.y = point.y;
//       new_point.z = point.z;
//       new_point.intensity = point.intensity;
//       cloud_data.cloud_ptr->points.push_back(new_point);
//   }
//   cloud_data.cloud_ptr->width = normal_data.m_cloudPtr->width;
//   cloud_data.cloud_ptr->height = normal_data.m_cloudPtr->height;
//   cloud_data.cloud_ptr->is_dense = normal_data.m_cloudPtr->is_dense;
//   return cloud_data;
// }

// 将CloudNormalData类型转换到CloudData类型
inline CloudData ConvertToCloudData(const CloudNormalData& normal_data) {
  CloudData cloud_data;
 // cloud_data.cloud_ptr = std::make_shared<CloudData::CLOUD>();
  cloud_data.time = normal_data.m_time;
  cloud_data.cloud_ptr->points.reserve(normal_data.m_cloudPtr->points.size());
  for (const auto& point : normal_data.m_cloudPtr->points) {
      CloudData::POINT new_point;
      new_point.x = point.x;
      new_point.y = point.y;
      new_point.z = point.z;
      new_point.intensity = point.intensity;
      cloud_data.cloud_ptr->points.push_back(new_point);
  }
  cloud_data.cloud_ptr->width = normal_data.m_cloudPtr->width;
  cloud_data.cloud_ptr->height = normal_data.m_cloudPtr->height;
  cloud_data.cloud_ptr->is_dense = normal_data.m_cloudPtr->is_dense;
  return cloud_data;
}

} // namespace communication
