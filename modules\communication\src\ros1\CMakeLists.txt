# communication/CMakeLists.txt
cmake_minimum_required(VERSION 3.5)
project(ros1_comm)

# 全局设置库文件输出目录
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../../lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../../lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../../bin)


set(CMAKE_CXX_STANDARD 17)

add_definitions(-DROS1_COMMUNICATION)
# add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/../../msg_interface/ros1)
find_package(OpenCV REQUIRED)
find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  sensor_msgs
  geometry_msgs
  nav_msgs
  message_generation
  pcl_ros
  image_transport
  cv_bridge
  # ros1_common_msgs  # 依赖的其他自定义包
  # OpenCV
)

add_message_files(
  FILES
  Pose6D.msg
  CanBusData.msg
  Extrinsics.msg
  DecisionOutput.msg
)

generate_messages(
  DEPENDENCIES
  std_msgs
  geometry_msgs
)

catkin_package(
  CATKIN_DEPENDS
  std_msgs
  message_runtime
)

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
  /usr/include/opencv4/
   ${OpenCV_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/../../include
  ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include
)

set(CMAKE_CXX_STANDARD 17)
#set(CMAKE_CXX_FLAGS "-std=c++17 -march=native")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall")

add_library(${PROJECT_NAME} SHARED
  communication_impl_ros1.cpp
  subscriber/imu_data_subscriber_ros1.cpp
  subscriber/gnss_data_subscriber_ros1.cpp
  subscriber/lidar_data_subscriber_ros1.cpp
  subscriber/odometry_subscriber_ros1.cpp
  subscriber/simple_data_subscriber_ros1.cpp
  subscriber/cloud_data_subscriber_ros1.cpp
  subscriber/path_data_subscriber_ros1.cpp
  subscriber/pose_data_subscriber_ros1.cpp
  subscriber/tf_data_subscriber_ros1.cpp
  subscriber/twist_data_subscriber_ros1.cpp
  subscriber/int8_data_subscriber_ros1.cpp

  publisher/camera_data_publisher_ros1.cpp
  publisher/simple_data_publisher_ros1.cpp
  publisher/cloud_data_publisher_ros1.cpp
  publisher/odometry_data_publisher_ros1.cpp
  publisher/path_data_publisher_ros1.cpp
  publisher/pose_data_publisher_ros1.cpp
  publisher/tf_data_publisher_ros1.cpp
  publisher/twist_data_publisher_ros1.cpp
  publisher/int8_data_publisher_ros1.cpp
  publisher/map_data_publisher_ros1.cpp
)

link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../../../common/lib

)
target_link_libraries(${PROJECT_NAME}
                    ${catkin_LIBRARIES} 
                    ${OpenCV_LIBRARIES}
                    common_lib 
)




