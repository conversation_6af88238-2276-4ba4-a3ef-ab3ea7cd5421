#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <cv_bridge/cv_bridge.h>

#include "ros2/core/publisher_base_ros2.h"
#include "data_types/image_data.h"

namespace communication::ros2
{

    class CameraDataPublisherRos2 : public PublisherBaseRos2<ImageData, sensor_msgs::msg::Image>
    {
    public:
        CameraDataPublisherRos2(rclcpp::Node::SharedPtr node,
                                const std::string &topic,
                                size_t max_buffer_size = 10)
            : PublisherBaseRos2<ImageData, sensor_msgs::msg::Image>(node, topic, max_buffer_size) {}

    protected:
        virtual void ToMsg() override
        {
            // msg.header.frame_id = data.frame_id;

            // cv_bridge::CvImage cv_img;
            // cv_img.image = data.image;
            // // cv_img.encoding = data.encoding;
            // cv_img.toImageMsg(msg);

            // 使用 cv_bridge 转换（自动处理编码和内存）
            auto cv_bridge_msg = cv_bridge::CvImage(
                                     std_msgs::msg::Header(),
                                     "bgr8",
                                     data_.image)
                                     .toImageMsg();
            // 设置时间戳和坐标系
            cv_bridge_msg->header.frame_id = "camera";
            cv_bridge_msg->header.stamp = rclcpp::Time(data_.time * 1e9);

            msg_ = *cv_bridge_msg;
        }
    };

} // namespace communication::ros2

#endif
