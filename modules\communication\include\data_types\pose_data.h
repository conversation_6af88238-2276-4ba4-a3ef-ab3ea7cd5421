#pragma once

#include <Eigen/Dense>

namespace communication {

class PoseData {
public:
    double time = 0.0;
    Eigen::Vector3f position = Eigen::Vector3f::Zero(); //位置
    Eigen::Vector4f orientation = Eigen::Vector4f::Zero(); //四元数

// public:
//     Eigen::Quaternionf GetQuaternion() {
//         Eigen::Quaternionf q;
//         q = pose.block<3, 3>(0, 0);
//         return q;
//     }

//     Eigen::Vector3f GetPosition() {
//         return pose.block<3, 1>(0, 3);
//     }
};

class PoseVelData {
    public:
        PoseData pose_data;  // 位姿数据

        Eigen::Vector3f vel = Eigen::Vector3f::Zero();
        Eigen::Vector3f angle_vel = Eigen::Vector3f::Zero();  // 角速度，弧度/s
    
    // public:
    //     Eigen::Quaternionf GetQuaternion() {
    //         Eigen::Quaternionf q;
    //         q = pose.block<3, 3>(0, 0);
    //         return q;
    //     }
    
    //     Eigen::Vector3f GetPosition() {
    //         return pose.block<3, 1>(0, 3);
    //     }
    };

// 带标准差的位姿
class CPoseDataWithStdStamped {
public:
    PoseData pose_data;
    Eigen::Vector3d m_positionStd = Eigen::Vector3d::Zero();  // 位置标准差，
    Eigen::Vector3d m_attitudeStd = Eigen::Vector3d::Zero();  // 姿态标准差，
};

}  // namespace msfl