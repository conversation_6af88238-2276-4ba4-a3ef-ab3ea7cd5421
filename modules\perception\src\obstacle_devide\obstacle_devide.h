#ifndef ___TERRAIN__ANALYSIS__HH_
#define ___TERRAIN__ANALYSIS__HH_


#include <memory>

#include "common/utils.h"
#include "obstacle_devide/patchworkpp.h"

namespace perception{



/* 以雷达为中心的 robot 外形尺寸 */
typedef struct{  
    float       min_x;              
    float       min_y;
    float       min_z;
    float       max_x;
    float       max_y;
    float       max_z;
}RobotBox;


typedef struct{
    RobotBox                    robot_box;
    patchwork::Params           patchwork_param;

}ObstacleDevideParameter;

 /***********************
 * 分割地面、静态、动态障碍物
 **********************/

class ObstacleDevide{
public:
    ObstacleDevide(const ObstacleDevideParameter& parameter);
    ~ObstacleDevide();

    void devide_cloud(const PointCloudT::Ptr& cloud, PointCloudT::Ptr& ground_cloud,
                        PointCloudT::Ptr& dynamic_cloud,PointCloudT::Ptr& static_cloud);

protected:
    ObstacleDevideParameter                         parameter_;
    std::unique_ptr<patchwork::PatchWorkpp>         patchworkpp_;
                             
};

}
#endif