#ifndef RRT_STAR_GLOBAL_PLANNER_H
#define RRT_STAR_GLOBAL_PLANNER_H


#include "communication.h"

#include <cmath>
#include <vector>
#include <string>
#include <memory>
#include <functional>
#include <iostream>
#include <fstream>
#include <random>
#include <thread>
#include <mutex>
#include <chrono>
#include <sstream>
#include <iomanip>

// YAML解析库 (可选)
#include <yaml-cpp/yaml.h>

#include "common_types.h"

namespace RRTstar_planner {
// 使用公共命名空间中的类型
using planning_common::PathData;
using planning_common::PoseStamped;
using planning_common::OdometryData;
using planning_common::Pose;
using planning_common::Point;
using planning_common::PoseArray;

// 定义Publisher模板类型别名
template<typename T>
using Publisher = std::function<void(const T&)>;

/**
 * @brief 去ROS化的可视化标记结构 (替代visualization_msgs::Marker)
 */
struct Marker {
    struct Header {
        double stamp;
        std::string frame_id;
        Header() : stamp(0.0), frame_id("map") {}
    } header;
    
    enum Type {
        POINTS = 8,
        LINE_STRIP = 4,
        LINE_LIST = 5
    };
    
    enum Action {
        ADD = 0,
        DELETE = 2,
        DELETEALL = 3
    };
    
    int id;
    Type type;
    Action action;
    
    struct Scale {
        double x, y, z;
        Scale() : x(0.1), y(0.1), z(0.1) {}
        Scale(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
    } scale;
    
    struct Color {
        double r, g, b, a;
        Color() : r(1.0), g(0.0), b(0.0), a(1.0) {}
        Color(double r_, double g_, double b_, double a_) : r(r_), g(g_), b(b_), a(a_) {}
    } color;
    
    struct Point {
        double x, y, z;
        Point() : x(0), y(0), z(0) {}
        Point(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
    };
    
    std::vector<Point> points;
    
    Marker() : id(0), type(POINTS), action(ADD) {}
    
    void clear() { points.clear(); }
    void addPoint(double x, double y, double z = 0.0) {
        points.emplace_back(x, y, z);
    }
};

/**
 * @brief 去ROS化的代价地图结构 (替代costmap_2d::Costmap2D)
 */
struct Costmap2D {
    std::vector<std::vector<unsigned char>> data;
    double resolution;      // 分辨率 (m/pixel)
    double origin_x, origin_y;  // 原点坐标
    int width, height;      // 地图尺寸
    
    Costmap2D() : resolution(0.05), origin_x(0), origin_y(0), width(0), height(0) {}
    Costmap2D(int w, int h, double res, double ox = 0.0, double oy = 0.0)
        : resolution(res), origin_x(ox), origin_y(oy), width(w), height(h) {
        data.resize(height, std::vector<unsigned char>(width, 0));
    }
    
    bool worldToMap(double wx, double wy, int& mx, int& my) const {
        mx = static_cast<int>((wx - origin_x) / resolution);
        my = static_cast<int>((wy - origin_y) / resolution);
        return (mx >= 0 && mx < width && my >= 0 && my < height);
    }
    
    bool mapToWorld(int mx, int my, double& wx, double& wy) const {
        if (mx < 0 || mx >= width || my < 0 || my >= height) return false;
        wx = origin_x + (mx + 0.5) * resolution;
        wy = origin_y + (my + 0.5) * resolution;
        return true;
    }
    
    unsigned char getCost(int mx, int my) const {
        if (mx < 0 || mx >= width || my < 0 || my >= height) return 255;
        return data[my][mx];
    }
    
    void setCost(int mx, int my, unsigned char cost) {
        if (mx >= 0 && mx < width && my >= 0 && my < height) {
            data[my][mx] = cost;
        }
    }
    
    bool loadFromFile(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) return false;
        
        file >> width >> height >> resolution >> origin_x >> origin_y;
        data.resize(height, std::vector<unsigned char>(width));
        
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                int cost;
                file >> cost;
                data[y][x] = static_cast<unsigned char>(cost);
            }
        }
        
        file.close();
        return true;
    }
    
    void saveToFile(const std::string& filename) const {
        std::ofstream file(filename);
        if (!file.is_open()) return;
        
        file << width << " " << height << " " << resolution << " " << origin_x << " " << origin_y << std::endl;
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                file << static_cast<int>(data[y][x]) << " ";
            }
            file << std::endl;
        }
        
        file.close();
    }
};

/**
 * @brief RRT*节点结构 (保持原有结构)
 */
struct Node {
    double x;
    double y;
    int node_id;
    int parent_id;
    double cost;
    
    Node() : x(0), y(0), node_id(-1), parent_id(-1), cost(0.0) {}
    Node(double x_, double y_, int id = -1, int parent = -1, double c = 0.0)
        : x(x_), y(y_), node_id(id), parent_id(parent), cost(c) {}
    
    bool operator==(const Node& node) const {
        return (fabs(x - node.x) < 0.0001) && (fabs(y - node.y) < 0.0001) && 
               (node_id == node.node_id) && (parent_id == node.parent_id) && 
               (fabs(cost - node.cost) < 0.0001);
    }

    bool operator!=(const Node& node) const {
        if ((fabs(x - node.x) > 0.0001) || (fabs(y - node.y) > 0.0001) || 
            (node_id != node.node_id) || (parent_id != node.parent_id) || 
            (fabs(cost - node.cost) > 0.0001))
            return true;
        else
            return false;
    }
};

/**
 * @brief 路径获取模式枚举 (保持原有枚举)
 */
enum GetPlanMode {
    TREE1 = 1,
    TREE2 = 2,
    CONNECT1TO2 = 3,
    CONNECT2TO1 = 4,
};

/**
 * @brief 去ROS化的RRT*规划器类 (保留所有原有功能和函数、变量)
 */
class RRTstarPlanner {
public:
    /**
     * @brief Default constructor of the plugin
     */
    RRTstarPlanner();

    RRTstarPlanner(std::string name, Costmap2D* costmap);

    /**
     * @brief  Initialization function for the PlannerCore object
     * @param  name The name of this planner
     * @param  costmap A pointer to the costmap to use for planning
     */
    void initialize(std::string name, Costmap2D* costmap);

    /**
     * @brief Given a goal pose in the world, compute a plan
     * @param start The start pose
     * @param goal The goal pose
     * @param plan The plan... filled by the planner
     * @return True if a valid plan was found, false otherwise
     */
    bool makePlan(const PoseStamped& start,
                  const PoseStamped& goal,
                  std::vector<PoseStamped>& plan);

    void getPathFromTree1ConnectTree2(std::vector<Node>& tree1,
                                      std::vector<Node>& tree2,
                                      Node& connect_node,
                                      std::vector<PoseStamped>& plan);

    void getPathFromTree(std::vector<Node>& tree1,
                         std::vector<Node>& tree2,
                         Node& connect_node,
                         std::vector<PoseStamped>& plan,
                         GetPlanMode mode);
    
    /*
     * @brief Compute the euclidean distance (straight-line distance) between two points
     * @param px1 point 1 x
     * @param py1 point 1 y
     * @param px2 point 2 x
     * @param py2 point 2 y
     * @return the distance computed
     */
    double distance(double px1, double py1, double px2, double py2);

    /**
     * @brief Generate random points.
     * @return the a random point in the map.
     */
    std::pair<double, double> sampleFree(); //随机采样点

    /**
     * @brief Check if there is a collision.
     * @param x coordinate (cartesian system)
     * @param y coordinate (cartesian system)
     * @return True is the point collides and false otherwise
     */
    bool collision(double x, double y); //是否为障碍物

    /**
     * @brief Check whether there are obstacles around.
     * @param x coordinate
     * @param y coordinate
     * @return True is the there are obstacles around
     */
    bool isAroundFree(double wx, double wy);

    bool isConnect(Node new_node, std::vector<Node>& another_tree, std::vector<Node>& current_tree, Node& connect_node);

    /**
     * @brief Given the nodes set and an point the function returns the closest node of the node
     * @param nodes the set of nodes
     * @param p_rand the random point (x,y) in the plane
     * return the closest node
     */
    Node getNearest(std::vector<Node> nodes, std::pair<double, double> p_rand); //搜索最近的节点

    /**
     * @brief Select the best parent. Check if there is any node around the newnode with cost less than its parent node cost. 
     * If yes choose this less cost node as the new parent of the newnode.
     * @param nn the parent of the newnode
     * @param newnode the node that will checked if there is a better parent for it
     * @param nodes the set of nodes
     * @return the same newnode with the best parent node
     * 
     */
    Node chooseParent(Node nn, Node newnode, std::vector<Node> nodes); //选择父节点

    /*
     * 该功能检查周围所有节点的父节点的开销是否仍小于新节点。
     * 如果存在父节点成本较高的节点，则该节点的新父节点现在是newnode。
     * 参数:nodes节点集。
     * 参数:newnode 新节点
     */
    void rewire(std::vector<Node>& nodes, Node newnode);

    /*
     * @brief The function generate the new point between the epsilon_min and epsilon_max along the line p_rand and nearest node. 
     *        This new point is a node candidate. It will a node if there is no obstacles between its nearest node and itself.
     * @param px1 point 1 x
     * @param py1 point 1 y
     * @param px2 point 2 x
     * @param py2 point 2 y
     * @return the new point
     */
    std::pair<double, double> steer(double x1, double y1, double x2, double y2); //生成新的树枝

    bool obstacleFree(Node node_nearest, double px, double py); //检查树枝是否碰撞障碍物

    /**
     * @brief Check if the distance between the goal and the newnode is less than the goal_radius. If yes the newnode is the goal.
     * @param px1 point 1 x
     * @param py1 point 1 y
     * @param px2 point 2 x
     * @param py2 point 2 y
     * *@return True if distance is less than the xy tolerance (GOAL_RADIUS), False otherwise
     */
    bool pointCircleCollision(double x1, double y1, double x2, double y2, double radius);

    void optimizationOrientation(std::vector<PoseStamped>& plan);

    void insertPointForPath(std::vector<std::pair<double, double>>& pathin, double param);

    int optimizationPath(std::vector<std::pair<double, double>>& plan, double movement_angle_range = M_PI/4); //优化路径

    bool isLineFree(const std::pair<double, double> p1, const std::pair<double, double> p2);

    void cutPathPoint(std::vector<std::pair<double, double>>& plan); //优化路径

    double inline normalizeAngle(double val, double min = -M_PI, double max = M_PI); //标准化角度

    // 新增的去ROS化接口
    void setMapFromFile(const std::string& map_file);
    void setMapFromData(const std::vector<std::vector<unsigned char>>& map_data, 
                       double resolution, double origin_x, double origin_y);
    
    // 修复Publisher函数声明
    void pubTreeMarker(Publisher<Marker>& marker_pub, Marker marker, int id);
    
    // 状态查询
    bool isInitialized() const { return initialized_; }
    std::string getFrameId() const { return frame_id_; }
    double getResolution() const { return resolution_; }
    
    // 参数设置
    void setMaxNodesNum(size_t max_nodes) { max_nodes_num_ = max_nodes; }
    void setPlanTimeout(double timeout) { plan_time_out_ = timeout; }
    void setSearchRadius(double radius) { search_radius_ = radius; }
    void setGoalRadius(double radius) { goal_radius_ = radius; }
    void setEpsilonMin(double epsilon) { epsilon_min_ = epsilon; }
    void setEpsilonMax(double epsilon) { epsilon_max_ = epsilon; }
    void setPathPointSpacing(double spacing) { path_point_spacing_ = spacing; }
    void setAngleDifference(double angle) { angle_difference_ = angle; }

    // ========== 新增的初始化和控制函数 ==========
    
    /**
     * @brief 从配置文件初始化规划器
     * @param config_file 配置文件路径
     * @return 初始化是否成功
     */
    bool initializeFromConfig(const std::string& config_file);
    
    /**
     * @brief 启动规划器
     * @return 启动是否成功
     */
    bool start();
    
    /**
     * @brief 停止规划器
     */
    void stop();
    
    /**
     * @brief 暂停规划器
     */
    void pause();
    
    /**
     * @brief 恢复规划器
     */
    void resume();
    
    /**
     * @brief 重置规划器状态
     */
    void reset();
    
    /**
     * @brief 清理规划器资源
     */
    void cleanup();
    
    /**
     * @brief 设置规划器名称
     * @param name 规划器名称
     */
    void setName(const std::string& name);
    
    /**
     * @brief 设置地图数据
     * @param costmap 代价地图指针
     */
    void setCostmap(Costmap2D* costmap);
    
    /**
     * @brief 设置规划模式
     * @param mode 规划模式 (0: 标准模式, 1: 快速模式, 2: 精确模式)
     */
    void setPlanningMode(int mode);
    
    /**
     * @brief 设置目标偏向采样概率
     * @param probability 概率值 (0.0-1.0)
     */
    void setGoalBiasProbability(double probability);
    
    /**
     * @brief 设置是否使用信息采样
     * @param use_informed 是否使用信息采样
     */
    void setUseInformedSampling(bool use_informed);
    
    /**
     * @brief 设置是否使用双向搜索
     * @param use_bidirectional 是否使用双向搜索
     */
    void setUseBidirectionalSearch(bool use_bidirectional);
    
    /**
     * @brief 设置是否启用可视化
     * @param enable 是否启用可视化
     */
    void setEnableVisualization(bool enable);
    
    /**
     * @brief 设置是否启用调试输出
     * @param enable 是否启用调试输出
     */
    void setEnableDebug(bool enable);
    
    /**
     * @brief 设置规划频率
     * @param frequency 规划频率 (Hz)
     */
    void setPlanningFrequency(double frequency);
    
    /**
     * @brief 设置碰撞检测阈值
     * @param threshold 碰撞检测阈值
     */
    void setCollisionThreshold(unsigned char threshold);
    
    /**
     * @brief 设置路径平滑参数
     * @param smoothing_factor 平滑因子 (0.0-1.0)
     */
    void setPathSmoothingFactor(double smoothing_factor);
    
    /**
     * @brief 设置路径简化参数
     * @param simplify_tolerance 简化容差
     */
    void setPathSimplifyTolerance(double simplify_tolerance);
    
    // ========== 新增的状态查询函数 ==========
    
    /**
     * @brief 检查规划器是否正在运行
     * @return 是否正在运行
     */
    bool isRunning() const;
    
    /**
     * @brief 检查规划器是否已暂停
     * @return 是否已暂停
     */
    bool isPaused() const;
    
    /**
     * @brief 获取规划器名称
     * @return 规划器名称
     */
    std::string getName() const;
    
    /**
     * @brief 获取当前规划模式
     * @return 规划模式
     */
    int getPlanningMode() const;
    
    /**
     * @brief 获取目标偏向采样概率
     * @return 目标偏向采样概率
     */
    double getGoalBiasProbability() const;
    
    /**
     * @brief 获取是否使用信息采样
     * @return 是否使用信息采样
     */
    bool getUseInformedSampling() const;
    
    /**
     * @brief 获取是否使用双向搜索
     * @return 是否使用双向搜索
     */
    bool getUseBidirectionalSearch() const;
    
    /**
     * @brief 获取是否启用可视化
     * @return 是否启用可视化
     */
    bool getEnableVisualization() const;
    
    /**
     * @brief 获取是否启用调试输出
     * @return 是否启用调试输出
     */
    bool getEnableDebug() const;
    
    /**
     * @brief 获取规划频率
     * @return 规划频率 (Hz)
     */
    double getPlanningFrequency() const;
    
    /**
     * @brief 获取碰撞检测阈值
     * @return 碰撞检测阈值
     */
    unsigned char getCollisionThreshold() const;
    
    /**
     * @brief 获取路径平滑因子
     * @return 路径平滑因子
     */
    double getPathSmoothingFactor() const;
    
    /**
     * @brief 获取路径简化容差
     * @return 路径简化容差
     */
    double getPathSimplifyTolerance() const;
    
    /**
     * @brief 获取地图信息
     * @param width 地图宽度 (输出)
     * @param height 地图高度 (输出)
     * @param resolution 地图分辨率 (输出)
     * @param origin_x 地图原点x坐标 (输出)
     * @param origin_y 地图原点y坐标 (输出)
     * @return 是否有有效地图
     */
    bool getMapInfo(int& width, int& height, double& resolution, 
                   double& origin_x, double& origin_y) const;
    
    /**
     * @brief 获取当前参数配置
     * @return 参数字符串
     */
    std::string getParameterString() const;
    
    /**
     * @brief 获取规划器状态信息
     * @return 状态信息字符串
     */
    std::string getStatusString() const;
    
    /**
     * @brief 获取最后一次规划的时间
     * @return 规划时间 (秒)
     */
    double getLastPlanningTime() const;
    
    /**
     * @brief 获取最后一次规划的节点数
     * @return 节点数
     */
    size_t getLastPlanningNodes() const;
    
    /**
     * @brief 获取最后一次规划的路径长度
     * @return 路径长度 (米)
     */
    double getLastPlanningPathLength() const;
    
    /**
     * @brief 获取规划成功率统计
     * @param total_attempts 总尝试次数 (输出)
     * @param successful_attempts 成功次数 (输出)
     * @return 成功率 (0.0-1.0)
     */
    double getPlanningSuccessRate(size_t& total_attempts, size_t& successful_attempts) const;
    
    /**
     * @brief 获取平均规划时间
     * @return 平均规划时间 (秒)
     */
    double getAveragePlanningTime() const;
    
    /**
     * @brief 获取平均规划节点数
     * @return 平均节点数
     */
    size_t getAveragePlanningNodes() const;
    
    /**
     * @brief 获取平均路径长度
     * @return 平均路径长度 (米)
     */
    double getAveragePathLength() const;
    
    /**
     * @brief 重置统计信息
     */
    void resetStatistics();
    
    /**
     * @brief 打印规划器状态
     */
    void printStatus() const;
    
    /**
     * @brief 打印参数配置
     */
    void printParameters() const;
    
    /**
     * @brief 打印统计信息
     */
    void printStatistics() const;

    // ========== 新增的数据接收接口 ==========
    
    /**
     * @brief 更新里程计数据
     * @param odom 里程计数据
     */
    void updateOdometry(const OdometryData& odom);
    
    /**
     * @brief 更新目标点数据
     * @param goal 目标点数据
     */
    void updateGoal(const PoseStamped& goal);
    
    /**
     * @brief 更新位姿数据
     * @param pose 位姿数据
     */
    void updatePoseData(const PoseStamped& pose);
    
    /**
     * @brief 获取当前机器人位姿
     * @return 当前位姿
     */
    PoseStamped getCurrentPose() const;
    
    /**
     * @brief 获取当前目标位姿
     * @return 当前目标位姿
     */
    PoseStamped getCurrentGoal() const;
    
    /**
     * @brief 检查是否有有效的当前位姿
     * @return 是否有有效位姿
     */
    bool hasValidPose() const;
    
    /**
     * @brief 检查是否有有效的目标位姿
     * @return 是否有有效目标
     */
    bool hasValidGoal() const;

protected:
    Costmap2D* costmap_;
    std::string frame_id_;

private:
    Marker marker_tree_;
    Marker marker_tree_2_;
    
    // 保留所有原有变量
    size_t max_nodes_num_;
    double plan_time_out_;
    double search_radius_;
    double goal_radius_;
    double epsilon_min_;
    double epsilon_max_;

    //路径优化参数
    double path_point_spacing_;
    double angle_difference_;

    double resolution_;
    bool initialized_;
    
    // 随机数生成器
    std::mt19937 rng_;
    std::uniform_real_distribution<double> uniform_dist_;
    
    // ========== 新增的成员变量 ==========
    
    // 规划器状态
    std::string planner_name_;
    bool is_running_;
    bool is_paused_;
    int planning_mode_;  // 0: 标准模式, 1: 快速模式, 2: 精确模式
    
    // 规划参数
    double goal_bias_probability_;
    bool use_informed_sampling_;
    bool use_bidirectional_search_;
    bool enable_visualization_;
    bool enable_debug_;
    double planning_frequency_;
    unsigned char collision_threshold_;
    double path_smoothing_factor_;
    double path_simplify_tolerance_;
    
    // 统计信息
    struct PlanningStatistics {
        size_t total_attempts;
        size_t successful_attempts;
        double total_planning_time;
        size_t total_planning_nodes;
        double total_path_length;
        double last_planning_time;
        size_t last_planning_nodes;
        double last_path_length;
        
        PlanningStatistics() : total_attempts(0), successful_attempts(0), 
                              total_planning_time(0.0), total_planning_nodes(0), 
                              total_path_length(0.0), last_planning_time(0.0), 
                              last_planning_nodes(0), last_path_length(0.0) {}
    } statistics_;
    
    // 线程安全
    mutable std::mutex statistics_mutex_;
    mutable std::mutex state_mutex_;
    
    // ========== 新增的位姿数据存储 ==========
    
    // 当前机器人位姿
    PoseStamped current_pose_;
    bool has_current_pose_;
    
    // 当前目标位姿
    PoseStamped current_goal_;
    bool has_current_goal_;
    
    // 位姿数据互斥锁
    mutable std::mutex pose_mutex_;
    
    // 新增路径存储
    std::vector<PoseStamped> last_plan_;
    mutable std::mutex plan_mutex_;

    void publishPath();

    // Static helper function
    static planning_common::Quaternion createQuaternionFromYaw(double yaw);
};

} // RRTstar_planner namespace

#endif // RRT_STAR_GLOBAL_PLANNER_H
