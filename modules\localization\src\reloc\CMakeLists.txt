#Create main reloc library
file(GLOB_RECURSE RELOC_ALG_FILES
    "alg/basis/log/*.cc"
    # "alg/basis/spdlog/*.cpp"
    # "alg/basis/ivox3d/*.cpp"
    # "alg/basis/*.hpp"
    "alg/localMapExtract/*.cpp"
    "alg/matchRateCal/*.cpp"
    "alg/relocService/*.cpp"
    "reloc_plugin/*.cpp"
)

add_library(${PROJECT_NAME}_reloc SHARED
    relocalization.cpp
    ${RELOC_ALG_FILES}
)

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/alg
    ${CMAKE_CURRENT_SOURCE_DIR}/alg/basis
    ${CMAKE_CURRENT_SOURCE_DIR}/alg/basis/log
    ${CMAKE_CURRENT_SOURCE_DIR}/alg/basis/spdlog
    ${CMAKE_CURRENT_SOURCE_DIR}/alg/basis/ivox3d
    ${CMAKE_CURRENT_SOURCE_DIR}/alg/localMapExtract
    ${CMAKE_CURRENT_SOURCE_DIR}/alg/matchRateCal
    ${CMAKE_CURRENT_SOURCE_DIR}/alg/relocService
    ${CMAKE_CURRENT_SOURCE_DIR}/reloc_plugin

    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/model
)

target_link_libraries(${PROJECT_NAME}_reloc
    ${EIGEN3_LIBRARIES}
    ${PCL_LIBRARIES}
    #fmt::fmt
    common_lib
    communication_core
)



