/**
 * Represents the context of the environment in which the decision-making process occurs.
 */

 #pragma once

 #include <deque>
 #include "path_data.h"
 #include "lidar_point_cloud.h"
 #include "imu_data.h"
 #include "pose_data.h"

 namespace decision{

using namespace communication;
enum SlopeDirection
{
    NONE,
    UP_SLOPE,
    DOWN_SLOPE
};

enum StairDirection { 
    NO_STAIRS, 
    ASCENDING, 
    DESCENDING
};


// 环境上下文结构体
struct EnvironmentContext {
    // 传感器数据
    CloudData obstacles; //障碍物点云
    PathData global_path; //全局规划路径
    PathData dynamic_trajectories; //动态障碍物轨迹
    PoseVelData localization; // 自身定位数据
    std::deque<IMUData> imu_data_dq; //IMU数据
    
    // 环境分析结果
    bool frontal_obstacle_detected = false;
    bool corridor_detected = false;
    bool collision_risk = false;
    bool yield_required = false;
    bool path_blocked = false;
    bool slope_detected = false;
    bool stairs_detected = false;
    bool rough_terrain_detected = false;

    float left_min_dist = 0.5f;
    float right_min_dist = 0.5f;

    float frontal_min_dist = 10.0f;
    float corridor_width = 0.0f;
    float corridor_offset = 0.0f;
    float slope_angle = 0.0f;
    float terrain_roughness = 0.0f;
    
    SlopeDirection slope_direction = NONE;
    StairDirection stair_direction = NO_STAIRS;
};

 } // namespace decision{