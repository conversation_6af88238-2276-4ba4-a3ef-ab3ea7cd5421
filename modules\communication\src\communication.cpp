#include "communication.h"

#ifdef COMMUNICATION_TYPE_ROS1
#include "communication_impl_ros1.h"
#elif defined COMMUNICATION_TYPE_ROS2
#include "communication_impl_ros2.h"
#else
#include error : "Unsupported communication type specified. Please define COMMUNICATION_TYPE as ROS1 or ROS2."
#endif

namespace communication
{

    Communication::Communication(const std::string &module_name)
        : module_name_(module_name),
          communication_impl_(nullptr)
    {
    }

    Communication::~Communication()
    {
        if (IsTerminated() && communication_impl_)
        {
            //             communication_impl_.reset();
            // #ifdef COMMUNICATION_TYPE_ROS1
            //             ros::shutdown();
            // #elif defined COMMUNICATION_TYPE_ROS2
            //             rclcpp::shutdown();
            // #else
            // #endif
        }
    }

    bool Communication::Initialize(const std::string &config_file_path)
    {
        // Load the configuration file path
        // TODO: Add logic to load the configuration file and set up parameters
#ifdef COMMUNICATION_TYPE_ROS1
        type_ = CommunicationType::ROS1;
        // Create the ROS1 communication implementation
        int argc = 0;
        char **argv = nullptr;
        ros::init(argc, argv, module_name_);
        communication_impl_ = std::make_shared<communication::ros1::CommunicationRos1Impl>(module_name_);
#elif defined COMMUNICATION_TYPE_ROS2
        type_ = CommunicationType::ROS2;
        // Initialize ROS2
        rclcpp::init(0, nullptr); // Fallback to empty initialization
        // ROS2 node initialization is handled in CommunicationRos2Impl constructor
        communication_impl_ = std::make_shared<communication::ros2::CommunicationRos2Impl>(module_name_);
#else
        // Handle unsupported communication types
        throw std::runtime_error("Unsupported communication type specified.");
#endif

        // Initialize the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->Initialize(type_);
        }
        else
        {
            return false; // Failed to initialize communication implementation
        }
    }

    // Create subscribers for IMU, GNSS, and LiDAR data
    std::shared_ptr<ImuDataSubscriberBase> Communication::CreateImuDataSubscriber(const std::string &imu_topic)
    {
        // Create and return an IMU data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateImuDataSubscriber(imu_topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<GnssDataSubscriberBase> Communication::CreateGnssDataSubscriber(const std::string &gnss_topic)
    {
        // Create and return a GNSS data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateGnssDataSubscriber(gnss_topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<LidarDataSubscriberBase> Communication::CreateLidarDataSubscriber(const std::string &lidar_topic)
    {
        // Create and return a LiDAR data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateLidarDataSubscriber(lidar_topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<OdometrySubscriberBase> Communication::CreateOdometrySubscriber(const std::string &topic)
    {
        // Create and return an odometry subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateOdometrySubscriber(topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<IntDataSubscriberBase> Communication::CreateIntDataSubscriber(const std::string &topic)
    {
        // Create and return an integer data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateIntDataSubscriber(topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<DoubleDataSubscriberBase> Communication::CreateDoubleDataSubscriber(const std::string &topic)
    {
        // Create and return a double data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateDoubleDataSubscriber(topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<StringDataSubscriberBase> Communication::CreateStringDataSubscriber(const std::string &topic)
    {
        // Create and return a string data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateStringDataSubscriber(topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<BoolDataSubscriberBase> Communication::CreateBoolDataSubscriber(const std::string &topic)
    {
        // Create and return a boolean data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateBoolDataSubscriber(topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<CloudDataSubscriberBase> Communication::CreateCloudDataSubscriber(const std::string &topic)
    {
        // Create and return a cloud data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateCloudDataSubscriber(topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<PathDataSubscriberBase> Communication::CreatePathDataSubscriber(const std::string &topic)
    {
        // Create and return a path data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreatePathDataSubscriber(topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<PoseDataSubscriberBase> Communication::CreatePoseDataSubscriber(const std::string &topic)
    {
        // Create and return a pose data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreatePoseDataSubscriber(topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<CameraIntSubscriberBase> Communication::CreateCameraIntSubscriber(const std::string &topic)
    {
        // Create and return a  camera intrinsics data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateCameraIntSubscriber(topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }
    std::shared_ptr<CameraExtSubscriberBase> Communication::CreateCameraExtSubscriber(const std::string &topic)
    {
        // Create and return a  camera extrinsics data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateCameraExtSubscriber(topic);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<ImageDataSubscriberBase> Communication::CreateImageDataSubscriber(const std::string &topic, const std::string &pixel_type)
    {
        // Create and return a  camera data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateImageDataSubscriber(topic, pixel_type);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<TFDataSubscriberBase> Communication::CreateTFDataSubscriber(const std::string &target_frame_id, const std::string &source_frame_id)
    {
        // Create and return a TF data subscriber based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateTFDataSubscriber(target_frame_id, source_frame_id);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<ImageDataPublisherBase> Communication::CreateImageDataPublisher(const std::string &topic,
                                                                                    const std::string &pixel_type, size_t max_buffer_size)
    {
        if (communication_impl_)
        {
            return communication_impl_->CreateImageDataPublisher(topic, pixel_type);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<OdometryPublisherBase> Communication::CreateOdometryPublisher(const std::string &topic,
                                                                                  const std::string &frame_id, const std::string &child_frame_id, size_t max_buffer_size)
    {
        // Create and return an odometry publisher based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateOdometryPublisher(topic, frame_id, child_frame_id, max_buffer_size);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<IntDataPublisherBase> Communication::CreateIntDataPublisher(const std::string &topic, size_t max_buffer_size)
    {
        // Create and return an integer data publisher based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateIntDataPublisher(topic, max_buffer_size);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<DoubleDataPublisherBase> Communication::CreateDoubleDataPublisher(const std::string &topic, size_t max_buffer_size)
    {
        // Create and return a double data publisher based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateDoubleDataPublisher(topic, max_buffer_size);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<StringDataPublisherBase> Communication::CreateStringDataPublisher(const std::string &topic, size_t max_buffer_size)
    {
        // Create and return a string data publisher based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateStringDataPublisher(topic, max_buffer_size);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<BoolDataPublisherBase> Communication::CreateBoolDataPublisher(const std::string &topic, size_t max_buffer_size)
    {
        // Create and return a boolean data publisher based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateBoolDataPublisher(topic, max_buffer_size);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<CloudDataPublisherBase> Communication::CreateCloudDataPublisher(const std::string &topic,
                                                                                    const std::string &frame_id, size_t max_buffer_size)
    {
        // Create and return a cloud data publisher based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreateCloudDataPublisher(topic, frame_id, max_buffer_size);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<PathDataPublisherBase> Communication::CreatePathDataPublisher(const std::string &topic,
                                                                                  const std::string &frame_id, size_t max_buffer_size)
    {
        // Create and return a path data publisher based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreatePathDataPublisher(topic, frame_id, max_buffer_size);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    std::shared_ptr<PoseDataPublisherBase> Communication::CreatePoseDataPublisher(const std::string &topic, size_t max_buffer_size)
    {
        // Create and return a pose data publisher based on the communication implementation
        if (communication_impl_)
        {
            return communication_impl_->CreatePoseDataPublisher(topic, max_buffer_size);
        }
        else
        {
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

std::shared_ptr<TFDataPublisherBase> Communication::CreateTFDataPublisher(const std::string& frame_id, const std::string& child_frame_id) {
    // Create and return a TF data publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateTFDataPublisher(frame_id, child_frame_id);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<TwistDataPublisherBase> Communication::CreateTwistDataPublisher(const std::string &topic,
                const std::string& frame_id, size_t max_buffer_size) {
    // Create and return a twist data publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateTwistDataPublisher(topic, frame_id, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<TwistDataSubscriberBase> Communication::CreateTwistDataSubscriber(const std::string &topic) {
    // Create and return a twist data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateTwistDataSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<Int8DataSubscriberBase> Communication::CreateInt8DataSubscriber(const std::string &topic) {
    // Create and return an int8 data subscriber based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateInt8DataSubscriber(topic);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<Int8DataPublisherBase> Communication::CreateInt8DataPublisher(const std::string &topic, size_t max_buffer_size) {
    // Create and return an int8 data publisher based on the communication implementation
    if (communication_impl_) {
        return communication_impl_->CreateInt8DataPublisher(topic, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

std::shared_ptr<OccupancyGridPublisherBase> Communication::CreateMapPublisher(const std::string &topic, const std::string &frame_id, size_t max_buffer_size) {
    if (communication_impl_) {
        return communication_impl_->CreateMapPublisher(topic, frame_id, max_buffer_size);
    } else {
        throw std::runtime_error("Communication implementation is not initialized.");
    }
}

    // Run the communication implementation
    void Communication::Run()
    {
        // Start the communication implementation
        // This can include starting threads, initializing subscribers, publishers, etc.
        if (communication_impl_)
        {
            communication_impl_->Run(); // Call the Run method of the communication implementation
        }
        else
        {
            // Handle the case where communication_impl_ is not initialized
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    void Communication::RunOnce()
    {
        // Start the communication implementation asynchronously
        // This can include starting threads, initializing subscribers, publishers, etc.
        if (communication_impl_)
        {
            communication_impl_->RunOnce(); // Call the Run method of the communication implementation
        }
        else
        {
            // Handle the case where communication_impl_ is not initialized
            throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    bool Communication::IsTerminated() const
    {
        // Check if the communication implementation is terminated
        if (communication_impl_)
        {
            return communication_impl_->IsTerminated();
        }
        else
        {
            return true;
            // Handle the case where communication_impl_ is not initialized
            // throw std::runtime_error("Communication implementation is not initialized.");
        }
    }

    void Communication::ShutDown()
    {
        if (communication_impl_)
        {
            communication_impl_.reset();
#ifdef COMMUNICATION_TYPE_ROS1
            ros::shutdown();
#elif defined COMMUNICATION_TYPE_ROS2
            rclcpp::shutdown();
#else
#endif
        }
    }

} // namespace communication {
