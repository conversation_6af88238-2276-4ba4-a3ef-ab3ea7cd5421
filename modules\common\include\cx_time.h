#ifndef CMTIME_H
#define CMTIME_H

#include "cx_pubhead.h"
namespace common_lib {
class CXTime {
 public:
  CXTime();
};

#ifdef LINUX

cx_long GetTickCount();

#endif

cx_string GetNameStringViaDatatime();
cx_uint64 GetTimestamp();
cx_double GetCurrentSeconds();


// cx_double GetMSecs();
// cx_uint64 GetMSecsSinceEpoch();
// void SetCurGPSTime(cx_double dGpsSec);
// cx_double GetCurGPSTime();
}  // namespace common_lib
#endif  // CMTIME_H
