#if COMMUNICATION_TYPE == ROS2

#include "communication_impl_ros2.h"
#include "publisher/simple_data_publisher_ros2.h"
#include "publisher/odometry_data_publisher_ros2.h"
#include "publisher/cloud_data_publisher_ros2.h"
#include "publisher/path_data_publisher_ros2.h"
#include "publisher/pose_data_publisher_ros2.h"
#include "publisher/camera_data_publisher_ros2.h"
#include "publisher/tf_data_publisher_ros2.h"
#include "subscriber/simple_data_subscriber_ros2.h"
#include "subscriber/odometry_data_subscriber_ros2.h"
#include "subscriber/cloud_data_subscriber_ros2.h"
#include "subscriber/path_data_subscriber_ros2.h"
#include "subscriber/pose_data_subscriber_ros2.h"
#include "subscriber/lidar_data_subscriber_ros2.h"
#include "subscriber/gnss_data_subscriber_ros2.h"
#include "subscriber/imu_data_subscriber_ros2.h"
#include "subscriber/camera_data_subscriber_ros2.h"
#include "subscriber/camera_int_subscriber_ros2.h"
#include "subscriber/camera_ext_subscriber_ros2.h"
#include "subscriber/tf_data_subscriber_ros2.h"

namespace communication::ros2
{

    CommunicationRos2Impl::CommunicationRos2Impl(const std::string &module_name) : CommunicationImpl(module_name)
    {
        node_ = std::make_shared<rclcpp::Node>(module_name);
        executor_.add_node(node_);
        // it_ = std::make_shared<image_transport::ImageTransport>(node_);
    }

    CommunicationRos2Impl::~CommunicationRos2Impl()
    {
        // Cleanup resources
        // node_->cleanup_publishers();
        executor_.remove_node(node_);
    }

    // Subscriber implementations
    std::shared_ptr<ImageDataSubscriberBase> CommunicationRos2Impl::CreateImageDataSubscriber(
        const std::string &topic, const std::string &pixel_type)
    {
        return std::make_shared<CameraDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<CameraIntSubscriberBase> CommunicationRos2Impl::CreateCameraIntSubscriber(
        const std::string &topic)
    {
        return std::make_shared<CameraIntSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<CameraExtSubscriberBase> CommunicationRos2Impl::CreateCameraExtSubscriber(
        const std::string &topic)
    {
        return std::make_shared<CameraExtSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<ImuDataSubscriberBase> CommunicationRos2Impl::CreateImuDataSubscriber(
        const std::string &topic)
    {
        return std::make_shared<IMUDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<GnssDataSubscriberBase> CommunicationRos2Impl::CreateGnssDataSubscriber(
        const std::string &topic)
    {
        return std::make_shared<GNSSDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<LidarDataSubscriberBase> CommunicationRos2Impl::CreateLidarDataSubscriber(
        const std::string &topic)
    {
        return std::make_shared<LidarDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<OdometrySubscriberBase> CommunicationRos2Impl::CreateOdometrySubscriber(
        const std::string &topic)
    {
        return std::make_shared<OdometryDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<IntDataSubscriberBase> CommunicationRos2Impl::CreateIntDataSubscriber(
        const std::string &topic)
    {
        return std::make_shared<IntDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<DoubleDataSubscriberBase> CommunicationRos2Impl::CreateDoubleDataSubscriber(
        const std::string &topic)
    {
        return std::make_shared<DoubleDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<StringDataSubscriberBase> CommunicationRos2Impl::CreateStringDataSubscriber(
        const std::string &topic)
    {
        return std::make_shared<StringDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<BoolDataSubscriberBase> CommunicationRos2Impl::CreateBoolDataSubscriber(
        const std::string &topic)
    {
        return std::make_shared<BoolDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<CloudDataSubscriberBase> CommunicationRos2Impl::CreateCloudDataSubscriber(
        const std::string &topic)
    {
        return std::make_shared<CloudDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<PathDataSubscriberBase> CommunicationRos2Impl::CreatePathDataSubscriber(
        const std::string &topic)
    {
        return std::make_shared<PathDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<PoseDataSubscriberBase> CommunicationRos2Impl::CreatePoseDataSubscriber(
        const std::string &topic)
    {
        return std::make_shared<PoseDataSubscriberRos2>(node_, topic);
    }

    std::shared_ptr<TFDataSubscriberBase> CommunicationRos2Impl::CreateTFDataSubscriber(
        const std::string &target_frame_id, const std::string &source_frame_id)
    {
        return std::make_shared<TFDataSubscriberRos2>(node_, target_frame_id, source_frame_id);
    }

    // Publisher implementations
    std::shared_ptr<ImageDataPublisherBase> CommunicationRos2Impl::CreateImageDataPublisher(
        const std::string &topic, const std::string &pixel_type, size_t max_buffer_size)
    {
        return std::make_shared<CameraDataPublisherRos2>(node_, topic, max_buffer_size);
    }

    std::shared_ptr<OdometryPublisherBase> CommunicationRos2Impl::CreateOdometryPublisher(
        const std::string &topic, const std::string &frame_id,
        const std::string &child_frame_id, size_t max_buffer_size)
    {
        return std::make_shared<OdometryDataPublisherRos2>(node_, topic, frame_id, child_frame_id, max_buffer_size);
    }

    std::shared_ptr<IntDataPublisherBase> CommunicationRos2Impl::CreateIntDataPublisher(
        const std::string &topic, size_t max_buffer_size)
    {
        return std::make_shared<IntDataPublisherRos2>(node_, topic, max_buffer_size);
    }

    std::shared_ptr<DoubleDataPublisherBase> CommunicationRos2Impl::CreateDoubleDataPublisher(
        const std::string &topic, size_t max_buffer_size)
    {
        return std::make_shared<DoubleDataPublisherRos2>(node_, topic, max_buffer_size);
    }

    std::shared_ptr<StringDataPublisherBase> CommunicationRos2Impl::CreateStringDataPublisher(
        const std::string &topic, size_t max_buffer_size)
    {
        return std::make_shared<StringDataPublisherRos2>(node_, topic, max_buffer_size);
    }

    std::shared_ptr<BoolDataPublisherBase> CommunicationRos2Impl::CreateBoolDataPublisher(
        const std::string &topic, size_t max_buffer_size)
    {
        return std::make_shared<BoolDataPublisherRos2>(node_, topic, max_buffer_size);
    }

    std::shared_ptr<CloudDataPublisherBase> CommunicationRos2Impl::CreateCloudDataPublisher(
        const std::string &topic, const std::string &frame_id, size_t max_buffer_size)
    {
        return std::make_shared<CloudDataPublisherRos2>(node_, topic, frame_id, max_buffer_size);
    }

    std::shared_ptr<PathDataPublisherBase> CommunicationRos2Impl::CreatePathDataPublisher(
        const std::string &topic, const std::string &frame_id, size_t max_buffer_size)
    {
        return std::make_shared<PathDataPublisherRos2>(node_, topic, frame_id, max_buffer_size);
    }

    std::shared_ptr<PoseDataPublisherBase> CommunicationRos2Impl::CreatePoseDataPublisher(
        const std::string &topic, size_t max_buffer_size)
    {
        return std::make_shared<PoseDataPublisherRos2>(node_, topic, "map", max_buffer_size);
    }

    std::shared_ptr<TFDataPublisherBase> CommunicationRos2Impl::CreateTFDataPublisher(
        const std::string &frame_id, const std::string &child_frame_id)
    {
        return std::make_shared<TFDataPublisherRos2>(node_, frame_id, child_frame_id);
    }

    bool CommunicationRos2Impl::Initialize(const CommunicationType type)
    {
        return true;
    }

    void CommunicationRos2Impl::Run()
    {
        // rclcpp::spin(node_);
        executor_.spin();
    }

    void CommunicationRos2Impl::RunOnce()
    {
        // rclcpp::spin_some(node_);
        executor_.spin_some();
    }

    bool CommunicationRos2Impl::IsTerminated() const
    {
        return !rclcpp::ok();
    }

} // namespace communication::ros2

#endif
