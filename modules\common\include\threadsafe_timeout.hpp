//
// Created by zx on 22-12-1.
//

#ifndef _CPP_DEFINE_TIME_DATA_H___H___
#define _CPP_DEFINE_TIME_DATA_H___H___
#include <chrono>
#include <mutex>
namespace common_lib {
/*****************************
 * 时间类，方便与 ros::Time 互转
 *****************************/
class TimePoint {
 public:
  TimePoint() { tp_ = std::chrono::system_clock::now(); }
  ~TimePoint() {}
  TimePoint(const TimePoint &) = default;
  TimePoint &operator=(const TimePoint &) = default;
  static TimePoint now() { return TimePoint(); }

  double toSec() const {
    auto duration = tp_.time_since_epoch();
    double seconds = std::chrono::duration_cast<std::chrono::duration<double>>(duration).count();
    return seconds;
  }
  TimePoint &fromSec(double second) {
    auto seconds_part = static_cast<int64_t>(second);  //
    auto nanoseconds_part = static_cast<int64_t>((second - seconds_part) * 1e9);
    tp_ = std::chrono::system_clock::time_point(std::chrono::seconds(seconds_part) +
                                                std::chrono::nanoseconds(nanoseconds_part));
    return *this;
  }
  // 计算两时间差，double 单位：秒
  double operator-(const TimePoint &other) const {
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(tp_ - other.tp_);
    double time = double(duration.count()) * std::chrono::microseconds::period::num /
                  std::chrono::microseconds::period::den;
    return time;
  }

 protected:
  std::chrono::system_clock::time_point tp_;
};

/***********************************
 * 模板类，用于将数据封装成线程安全、超时判断
 ************************************/

template <typename T>
class ThreadSafeTimed {
 public:
  ThreadSafeTimed();
  void operator=(const T &value);
  void set_timeout(double timeout);
  bool timeout() const;
  T get() const;
  T &operator()() const;

 protected:
  T data_;
  TimePoint time_stamp_;
  std::mutex mutex_;
  double timeout_;
};

template <typename T>
ThreadSafeTimed<T>::ThreadSafeTimed()
    : timeout_(0.1) {}

template <typename T>
void ThreadSafeTimed<T>::set_timeout(double timeout) {
  std::lock_guard<std::mutex> lck(mutex_);
  timeout_ = timeout;
}

template <typename T>
void ThreadSafeTimed<T>::operator=(const T &value) {
  std::lock_guard<std::mutex> lck(mutex_);
  data_ = value;
  time_stamp_ = TimePoint::now();
}

template <typename T>
bool ThreadSafeTimed<T>::timeout() const {
  return TimePoint::now() - time_stamp_ > timeout_;
}

template <typename T>
T ThreadSafeTimed<T>::get() const {
  std::lock_guard<std::mutex> lck(mutex_);
  return data_;
}

template <typename T>
T &ThreadSafeTimed<T>::operator()() const {
  std::lock_guard<std::mutex> lck(mutex_);
  return data_;
}
}  // namespace common_lib

#endif  //_CPP_DEFINE_TIME_DATA_H_
