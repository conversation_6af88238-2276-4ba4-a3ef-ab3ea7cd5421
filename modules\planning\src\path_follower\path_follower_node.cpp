#include "communication.h"
#include "path_follower.h"
#include <chrono>
#include <iomanip>
#include <iostream>
#include <memory>
#include <thread>

using namespace path_follower;

static bool g_running = true;
// 全局对象
std::unique_ptr<PathFollower> path_follower_ptr;
std::shared_ptr<communication::PathDataSubscriberBase> path_subscriber_ptr;
std::shared_ptr<communication::OdometrySubscriberBase> odom_subscriber_ptr;
std::shared_ptr<communication::Int8DataSubscriberBase> stop_subscriber_ptr;
std::shared_ptr<communication::BoolDataSubscriberBase> mode_subscriber_ptr;
std::shared_ptr<communication::PoseDataSubscriberBase> goal_subscriber_ptr;
//std::shared_ptr<communication::PoseDataSubscriberBase> webgoal_subscriber_ptr;
std::shared_ptr<communication::TwistDataPublisherBase> cmd_vel_publisher_ptr;

void signalHandler(int signum) {
  std::cout << "\n收到信号 " << signum << "，正在关闭路径跟随器..."
            << std::endl;
  g_running = false;
  if (path_follower_ptr) {
    path_follower_ptr->stop();
  }
  std::cout << " 路径跟随器节点已安全退出" << std::endl;
}

void DataReceiveThread() {

  while (g_running) {
    if (!odom_subscriber_ptr->IsBufferEmpty()) {

      auto msg = odom_subscriber_ptr->GetBuffer();
      OdometryData odom;

      odom.header.stamp = msg.front().pose_data.time;
      odom.pose.pose.position.x = msg.front().pose_data.position[0];
      odom.pose.pose.position.y = msg.front().pose_data.position[1];
      odom.pose.pose.position.z = msg.front().pose_data.position[2];
      odom.pose.pose.orientation.x = msg.front().pose_data.orientation[0];
      odom.pose.pose.orientation.y = msg.front().pose_data.orientation[1];
      odom.pose.pose.orientation.z = msg.front().pose_data.orientation[2];
      odom.pose.pose.orientation.w = msg.front().pose_data.orientation[3];

      path_follower_ptr->updateOdometry(odom);
    }

    if (!path_subscriber_ptr->IsBufferEmpty()) {
      auto msg = path_subscriber_ptr->GetBuffer();
      path_follower::PathData path;
      path.header.stamp = msg.front().time_;
      path.poses_.clear();
      for (const auto& pose_data : msg.front().poses_) {
        path_follower::PoseStamped pt;
        pt.time = pose_data.time;
        pt.pose.position.x = pose_data.position[0];
        pt.pose.position.y = pose_data.position[1];
        pt.pose.position.z = pose_data.position[2];
        pt.pose.orientation.x = pose_data.orientation[0];
        pt.pose.orientation.y = pose_data.orientation[1];
        pt.pose.orientation.z = pose_data.orientation[2];
        pt.pose.orientation.w = pose_data.orientation[3];
        path.poses_.push_back(pt);
      }
 
      path_follower_ptr->updatePath(path);
    }

    // 接收 /istop 停止信号
    if (!stop_subscriber_ptr->IsBufferEmpty()) {
      auto msg = stop_subscriber_ptr->GetBuffer();
        path_follower::Int8Msg stop_msg;
        stop_msg.data = msg.front().data;
        std::cout<<"stop_msg.data: "<<stop_msg.data<<std::endl;
        path_follower_ptr->updateStop(stop_msg);
      
    }

    // 接收 /adjustmode 调整模式信号
    if (!mode_subscriber_ptr->IsBufferEmpty()) {
      auto msg = mode_subscriber_ptr->GetBuffer();
      
        path_follower::BoolMsg mode_msg;
        mode_msg.data = msg.front();
      
        path_follower_ptr->updateMode(mode_msg);
      
    }

    // 接收 /move_base_simple/goal 目标点
    if (!goal_subscriber_ptr->IsBufferEmpty()) {
      auto msg = goal_subscriber_ptr->GetBuffer();
      
        path_follower::PoseStamped goal_msg;
        goal_msg.time = msg.front().time;
        goal_msg.pose.position.x = msg.front().position[0];
        goal_msg.pose.position.y = msg.front().position[1];
        goal_msg.pose.position.z = msg.front().position[2];
        goal_msg.pose.orientation.x = msg.front().orientation[0];
        goal_msg.pose.orientation.y = msg.front().orientation[1];
        goal_msg.pose.orientation.z = msg.front().orientation[2];
        goal_msg.pose.orientation.w = msg.front().orientation[3];
        std::cout << "Received goal_msg: "
                  << "time=" << goal_msg.time << ", "
                  << "position=(" << goal_msg.pose.position.x << ", "
                  << goal_msg.pose.position.y << ", "
                  << goal_msg.pose.position.z << "), "
                  << "orientation=(" << goal_msg.pose.orientation.x << ", "
                  << goal_msg.pose.orientation.y << ", "
                  << goal_msg.pose.orientation.z << ", "
                  << goal_msg.pose.orientation.w << ")"
                  << std::endl;
        path_follower_ptr->updateGoal(goal_msg);
      
    }
  }
}

void DataSendThread() {
  while (true) {

    if (path_follower_ptr->isRunning()) {

      communication::TwistData msg;
      path_follower::TwistData current_vel = path_follower_ptr->getCmdVel();
      msg.time = std::chrono::duration_cast<std::chrono::seconds>(
                     std::chrono::system_clock::now().time_since_epoch())
                     .count();
      msg.linear_vel = Eigen::Vector3d(
          current_vel.linear.x, current_vel.linear.y, current_vel.linear.z);

      // 转换角速度
      msg.angular_vel = Eigen::Vector3d(
          current_vel.angular.x, current_vel.angular.y, current_vel.angular.z);
      cmd_vel_publisher_ptr->Publish(msg);
    }
  }
}

int main(int argc, char **argv) {
  std::cout << "=== 路径跟随器节点启动 ===" << std::endl;
  try {
    std::string config_path = "../config/path_follower.yaml";
    // 创建通信模块实例
    auto communication_ptr =
        std::make_shared<communication::Communication>("path_follower");
    if (!communication_ptr->Initialize("config/communication_config.yaml")) {
      std::cerr << " 通信模块初始化失败" << std::endl;
      return -1;
    }
    // 创建路径跟随器
    std::cout << "\n 创建路径跟随器..." << std::endl;
    path_follower_ptr = std::make_unique<PathFollower>(config_path);
    // 创建订阅器和发布器
    path_subscriber_ptr =
        communication_ptr->CreatePathDataSubscriber("/local_path");
    odom_subscriber_ptr =
        communication_ptr->CreateOdometrySubscriber("/Odometry");
    stop_subscriber_ptr = communication_ptr->CreateInt8DataSubscriber("/istop");
    mode_subscriber_ptr =
        communication_ptr->CreateBoolDataSubscriber("/adjustmode");
    goal_subscriber_ptr =
        communication_ptr->CreatePoseDataSubscriber("/move_base_simple/goal");
    //webgoal_subscriber_ptr =
        //communication_ptr->CreatePoseDataSubscriber("/web_goal_pose");
    cmd_vel_publisher_ptr =
        communication_ptr->CreateTwistDataPublisher("/cmd_vel", "map", 1);
    // 设置速度回调

    // 初始化跟随器
    std::cout << "\n 初始化路径跟随器..." << std::endl;
    if (!path_follower_ptr->initialize()) {
      std::cerr << " 路径跟随器初始化失败！" << std::endl;
      return -1;
    }
    // 启动跟随器
    std::cout << "\n 启动路径跟随器..." << std::endl;
    path_follower_ptr->start();
    // 打印状态信息
    std::cout << "\n 跟随器状态:" << std::endl;
    path_follower_ptr->printStatus();
    std::cout << "\n 路径跟随器节点启动成功！" << std::endl;



    // 启动数据接收线程
    std::thread data_receive_thread(DataReceiveThread);
    data_receive_thread.detach();

    std::thread data_send_thread(DataSendThread);
    data_send_thread.detach();

    // 阻塞主线程，运行通信模块
    communication_ptr->Run();
  } catch (const std::exception &e) {
    std::cerr << " 程序异常: " << e.what() << std::endl;
    return -1;
  }

  return 0;
}
