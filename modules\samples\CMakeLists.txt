cmake_minimum_required(VERSION 3.5)
project(samples)

# Set output directories
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)

if(NOT DEFINED CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Debug")
endif()

if(NOT DEFINED PLATFORM)
    set(PLATFORM "x86_64")
endif()

if(NOT DEFINED COMMUNICATION_TYPE)
    set(COMMUNICATION_TYPE "ROS1")
endif()

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall")
set(CMAKE_CXX_FLAGS_RELEASE "-O1")

message("Starting to parse CMake file for samples project.")

# Include directories
include_directories(
  ${CMAKE_CURRENT_SOURCE_DIR}/../common/include
)

add_executable(${PROJECT_NAME}_core common/common_samples.cpp)

link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../common/lib
)
target_link_libraries(${PROJECT_NAME}_core
                    common_lib 
)
