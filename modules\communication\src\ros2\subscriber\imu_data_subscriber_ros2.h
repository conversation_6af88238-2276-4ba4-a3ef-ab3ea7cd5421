#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/imu.hpp>

#include "ros2/core/subscriber_base_ros2.h"
#include "data_types/imu_data.h"

namespace communication::ros2
{

    class IMUDataSubscriberRos2 : public SubscriberBaseRos2<IMUData, sensor_msgs::msg::Imu>
    {
    public:
        IMUDataSubscriberRos2(rclcpp::Node::SharedPtr node,
                              const std::string &topic,
                              //   typename SubscriberBaseRos2<IMUData, sensor_msgs::msg::Imu>::CallbackType callback = nullptr,
                              size_t max_buffer_size = 10)
            : SubscriberBaseRos2<IMUData, sensor_msgs::msg::Imu>(node, topic, max_buffer_size)
        {
        }

    protected:
        virtual void FromMsg(const sensor_msgs::msg::Imu &msg, IMUData &data) override
        {
            data.time = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9;
            // data.frame_id = msg.header.frame_id;

            // Orientation
            data.orientation[0] = msg.orientation.x;
            data.orientation[1] = msg.orientation.y;
            data.orientation[2] = msg.orientation.z;
            data.orientation[3] = msg.orientation.w;

            // Angular velocity
            data.angular_velocity[0] = msg.angular_velocity.x;
            data.angular_velocity[1] = msg.angular_velocity.y;
            data.angular_velocity[2] = msg.angular_velocity.z;

            // Linear acceleration
            data.linear_acceleration[0] = msg.linear_acceleration.x;
            data.linear_acceleration[1] = msg.linear_acceleration.y;
            data.linear_acceleration[2] = msg.linear_acceleration.z;
        }
    };

} // namespace communication::ros2

#endif
