#pragma once

#if COMMUNICATION_TYPE == ROS1

#include <ros/ros.h>
// #include <deque>
// #include <mutex>
#include <sensor_msgs/PointCloud2.h>
#include <pcl_conversions/pcl_conversions.h>

// #include "lidar_point_cloud.h"
#include "publisher_base.h"

namespace communication::ros1
{

    class CloudDataPublisherRos1 : public CloudDataPublisherBase
    {
    public:
        CloudDataPublisherRos1(ros::NodeHandle &nh,
                               const std::string &topic,
                               const std::string &frame_id = "map",
                               size_t max_buffer_size = 10);

        virtual ~CloudDataPublisherRos1() = default;

    protected:
        virtual void PublishMsg() override
        {

            publisher_.publish(msg_);
        }

        virtual void ToMsg() override
        {
            // sensor_msgs::PointCloud2 msg;
            pcl::toROSMsg(*data_.cloud_ptr, msg_);
            msg_.header.stamp = ros::Time(data_.time);
            msg_.header.frame_id = frame_id_;
        }

        virtual int GetSubscriberCount() override
        {
            return publisher_.getNumSubscribers();
        }

    private:
        ros::NodeHandle &nh_;
        ros::Publisher publisher_;
        sensor_msgs::PointCloud2 msg_; // ROS message type for point cloud data
        std::string frame_id_;
    };

} // namespace communication::ros1{

#endif