# perception/CMakeLists.txt
cmake_minimum_required(VERSION 3.5)
project(localization)

# 设置编译类型和标准
set(CMAKE_BUILD_TYPE "Debug")
set(CMAKE_CXX_STANDARD 17)

find_package(Eigen3 REQUIRED)
find_package(PCL REQUIRED)
find_package(OpenCV REQUIRED)
# find_package(cv_bridge REQUIRED)

# Configure fmt library 
find_package(fmt REQUIRED)
find_package(TBB REQUIRED)

list(APPEND CMAKE_MODULE_PATH "/usr/share/cmake/geographiclib")
find_package(GeographicLib REQUIRED)
find_package(PkgConfig REQUIRED)

find_package(OpenMP QUIET)

pkg_check_modules(YAML_CPP REQUIRED yaml-cpp)

# Force PIC for fmt and all dependencies
set(CMAKE_POSITION_INDEPENDENT_CODE ON)
set(BUILD_SHARED_LIBS ON)

#启用fmt头文件模式 - 解决hidden symbol问题 
add_definitions(-DFMT_HEADER_ONLY)  # 新增行

set(CMAKE_POSITION_INDEPENDENT_CODE ON)
add_compile_options(-fPIC)

# 全局设置库文件输出目录
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)

message("Current CPU archtecture: ${CMAKE_SYSTEM_PROCESSOR}")
if(CMAKE_SYSTEM_PROCESSOR MATCHES "(x86)|(X86)|(amd64)|(AMD64)" )
  include(ProcessorCount)
  ProcessorCount(N)
  message("Processer number:  ${N}")
  if(N GREATER 4)
    add_definitions(-DMP_EN)
    add_definitions(-DMP_PROC_NUM=3)
    message("core for MP: 3")
  elseif(N GREATER 3)
    add_definitions(-DMP_EN)
    add_definitions(-DMP_PROC_NUM=2)
    message("core for MP: 2")
  else()
    add_definitions(-DMP_PROC_NUM=1)
  endif()
else()
  add_definitions(-DMP_PROC_NUM=1)
endif()

set(CMAKE_CXX_FLAGS "-std=c++17 -O3 -fPIC")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}   ${OpenMP_C_FLAGS}")

# set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS} -std=c++17 -g2 -O0 -ggdb -fprofile-arcs -ftest-coverage -fPIC")
# set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS} -std=c++17 -O2 -fPIC")

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${OpenCV_INCLUDE_DIRS}
    ${EIGEN3_INCLUDE_DIR}
    ${PCL_INCLUDE_DIRS}
    ${CMAKE_CURRENT_SOURCE_DIR}/../communication/include 
    ${CMAKE_CURRENT_SOURCE_DIR}/../communication/include/data_types
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/src
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include/platform
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include/thread
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include/model
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include/file
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include/config
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include/algorithm
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include/communication
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include/communication/MessagePump
    ${CMAKE_CURRENT_SOURCE_DIR}/src/common
    ${CMAKE_CURRENT_SOURCE_DIR}/src/common/eskf
    ${CMAKE_CURRENT_SOURCE_DIR}/src/common/eskf/ikd-Tree
    ${CMAKE_CURRENT_SOURCE_DIR}/src/communication
    ${CMAKE_CURRENT_SOURCE_DIR}/src/data_pretreat
    ${CMAKE_CURRENT_SOURCE_DIR}/src/reloc
    ${CMAKE_CURRENT_SOURCE_DIR}/src/eskf
    ${CMAKE_CURRENT_SOURCE_DIR}/src/reloc/alg
    ${CMAKE_CURRENT_SOURCE_DIR}/src/reloc/alg/basis
    ${CMAKE_CURRENT_SOURCE_DIR}/src/reloc/alg/basis/log
    ${CMAKE_CURRENT_SOURCE_DIR}/src/reloc/alg/basis/spdlog
    ${CMAKE_CURRENT_SOURCE_DIR}/src/reloc/alg/basis/ivox3d
    ${CMAKE_CURRENT_SOURCE_DIR}/src/reloc/alg/localMapExtract
    ${CMAKE_CURRENT_SOURCE_DIR}/src/reloc/alg/matchRateCal
    ${CMAKE_CURRENT_SOURCE_DIR}/src/reloc/alg/relocService
    ${CMAKE_CURRENT_SOURCE_DIR}/src/reloc/reloc_plugin
    ${CMAKE_CURRENT_SOURCE_DIR}/src/map_manager
    ${CMAKE_CURRENT_SOURCE_DIR}/src/map_manager/ikd-Tree
    ${CMAKE_CURRENT_SOURCE_DIR}/src/zero_detect
    ${CMAKE_CURRENT_SOURCE_DIR}/src/loc
    ${GTSAM_INCLUDE_DIR}
    ${GeographicLib_INCLUDE_DIRS}
)

# Add subdirectories for all components
add_subdirectory(src/common)
add_subdirectory(src/data_pretreat)
add_subdirectory(src/map_manager)
add_subdirectory(src/zero_detect)
add_subdirectory(src/communication)
add_subdirectory(src/reloc)
add_subdirectory(src/loc)


link_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../communication/lib
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/lib
    ${CMAKE_CURRENT_SOURCE_DIR}/lib
)

# Add subdirectories for all components
#add_subdirectory(src)

# 可执行文件
add_executable(${PROJECT_NAME}_node
    src/localization_node.cpp
)

target_link_libraries(${PROJECT_NAME}_node 
    ${PROJECT_NAME}_loc
    ${PROJECT_NAME}_pretreat
    ${PROJECT_NAME}_common
    ${PROJECT_NAME}_map_manager
    ${PROJECT_NAME}_reloc
    ${PROJECT_NAME}_zero_detect
    ${PROJECT_NAME}_communication
    common_lib
    communication_core
    ${GeographicLib_LIBRARIES}
    ${OpenMP_CXX_LIBRARIES}
    TBB::tbb
    fmt::fmt
    yaml-cpp
)




