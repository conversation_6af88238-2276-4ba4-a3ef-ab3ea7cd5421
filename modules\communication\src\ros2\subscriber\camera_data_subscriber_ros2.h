#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <cv_bridge/cv_bridge.h>

#include "ros2/core/subscriber_base_ros2.h"
#include "data_types/image_data.h"

namespace communication::ros2
{

    class CameraDataSubscriberRos2 : public SubscriberBaseRos2<ImageData, sensor_msgs::msg::Image>
    {
    public:
        CameraDataSubscriberRos2(rclcpp::Node::SharedPtr node,
                                 const std::string &topic,
                                 size_t max_buffer_size = 10)
            : SubscriberBaseRos2<ImageData, sensor_msgs::msg::Image>(node, topic, max_buffer_size) {}

    protected:
        virtual void FromMsg(const sensor_msgs::msg::Image &msg, ImageData &data) override
        {
            data.time = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9;
            // data.frame_id = msg.header.frame_id;

            cv_bridge::CvImagePtr cv_ptr = cv_bridge::toCvCopy(msg);
            data.image = cv_ptr->image;
            // data.encoding = cv_ptr->encoding;
        }
    };

} // namespace communication::ros2

#endif
