#include "subscriber/int8_data_subscriber_ros1.h"

#if COMMUNICATION_TYPE == ROS1
namespace communication::ros1 {
Int8DataSubscriberRos1::Int8DataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size)
    : Int8DataSubscriberBase(topic, max_buffer_size), nh_(nh) {
    subscriber_ = nh_.subscribe(topic, max_buffer_size, &Int8DataSubscriberRos1::Int8DataCallbackRos1, this);
}

void Int8DataSubscriberRos1::Int8DataCallbackRos1(const std_msgs::Int8::ConstPtr &int8_msg) {
    Int8Data int8_data;
    int8_data.time = ros::Time::now().toSec();  // Use current time since Int8 message has no header
    int8_data.data = int8_msg->data;

    // Store the converted data in the buffer
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    data_buffer_.push_back(int8_data);
    
    // Check if the buffer exceeds the maximum size
    if (data_buffer_.size() > max_buffer_size_) {
        data_buffer_.pop_front(); // Remove the oldest data if the buffer is full
    }
}

} // namespace communication::ros1
#endif // COMMUNICATION_TYPE == ROS1 