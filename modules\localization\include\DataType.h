#ifndef _DATA_TYPE_H_
#define _DATA_TYPE_H_
#include <pcl/point_types.h>
#include <pcl/point_cloud.h>
#include <Eigen/Eigen>
#include <pcl/pcl_base.h>
#include "cx_types.h"
#include <deque>
#include "communication.h"
using namespace std;
using namespace Eigen;
using namespace communication;

#define INIT_TIME (0.1)
#define LASER_POINT_COV (0.001) // 0.001

#define IS_VALID(a) ((abs(a) > 1e8) ? true : false)
#define PI_M (3.14159265358)
#define G_m_s2 (9.81)               // Gravaty const in GuangDong/China
#define NUM_MATCH_POINTS    (5)     

#define VEC_FROM_ARRAY(v)        v[0],v[1],v[2]
#define MAT_FROM_ARRAY(v)        v[0],v[1],v[2],v[3],v[4],v[5],v[6],v[7],v[8]
#define SKEW_SYM_MATRX(v)        0.0,-v[2],v[1],v[2],0.0,-v[0],-v[1],v[0],0.0

#define LASER_POINT_COV (0.001)

#define RTK_STATUS_THRESHOLD (4)

typedef CloudNormalData::PointXYZINormalType PointType;
typedef CloudNormalData::CLOUD_NormalType PointCloudXYZI;

typedef vector<PointType, Eigen::aligned_allocator<PointType>>  PointVector;
typedef Vector3d V3D;
typedef Matrix3d M3D;
typedef Vector3f V3F;
typedef Matrix3f M3F;

// M3D Eye3d(M3D::Identity());
// M3F Eye3f(M3F::Identity());
// V3D Zero3d(0, 0, 0);
// V3F Zero3f(0, 0, 0);

enum LID_TYPE
{
  AVIA = 1,
  VELO16,
  OUST64,
  RS32
}; //{1, 2, 3, 4}
enum TIME_UNIT
{
  SEC = 0,
  MS = 1,
  US = 2,
  NS = 3
};
enum Feature
{
  Nor,
  Poss_Plane,
  Real_Plane,
  Edge_Jump,
  Edge_Plane,
  Wire,
  ZeroPoint
};
enum Surround
{
  Prev,
  Next
};
enum E_jump
{
  Nr_nor,
  Nr_zero,
  Nr_180,
  Nr_inf,
  Nr_blind
};

struct Pose6D
{
    double  offset_time; // the offset time of IMU measurement w.r.t the first lidar point
    double  acc[3];       // the preintegrated total acceleration (global frame) at the Lidar origin
    double  gyr[3];      // the unbiased angular velocity (body frame) at the Lidar origin
    double  vel[3];      // the preintegrated velocity (global frame) at the Lidar origin
    double  pos[3];      // the preintegrated position (global frame) at the Lidar origin
    double  rot[9];      // the preintegrated rotation (global frame) at the Lidar origin
};

struct ManualPos
{
    Eigen::Vector3d manualpos;
    Eigen::Quaterniond ext_q;
};

struct GnssENU
{
    double enu_x; // 北向坐标
    double enu_y; // 东向坐标
    double enu_z; // 地向坐标
    double latitude; // 纬度
    double longitude; // 经度
    double altitude; // 高度
    double timestamp; // 时间戳
    int status; // RTK状态
    array<double, 36> covariance; // 协方差矩阵

    GnssENU()
    {
        enu_x = 0.0;
        enu_y = 0.0;
        enu_z = 0.0;
        latitude = 0.0;
        longitude = 0.0;
        altitude = 0.0;
        timestamp = 0.0;
        status = 0; // 默认状态为0
        covariance.fill(0.0); // 初始化协方差矩阵为零
    }
};

struct orgtype
{
  double range;
  double dista;
  double angle[2];
  double intersect;
  E_jump edj[2];
  Feature ftype;
  orgtype()
  {
    range = 0;
    edj[Prev] = Nr_nor;
    edj[Next] = Nr_nor;
    ftype = Nor;
    intersect = 2;
  }
};

// namespace velodyne_ros
// {
//   struct EIGEN_ALIGN16 Point
//   {
//     PCL_ADD_POINT4D;
//     //float intensity;
//     PCL_ADD_INTENSITY;
//     float time;
//     uint16_t ring;
//     EIGEN_MAKE_ALIGNED_OPERATOR_NEW
//   };
// } // namespace velodyne_ros
// POINT_CLOUD_REGISTER_POINT_STRUCT(velodyne_ros::Point,
//                                   (float, x, x)(float, y, y)(float, z, z)(float, intensity, intensity)(float, time, time)(std::uint16_t, ring, ring))

// namespace rslidar_ros
// {
//   struct EIGEN_ALIGN16 Point
//   {
//     PCL_ADD_POINT4D;
//     float intensity;
//     uint16_t ring = 0;
//     double timestamp = 0;
//     EIGEN_MAKE_ALIGNED_OPERATOR_NEW
//   };
// } // namespace rslidar_ros
// POINT_CLOUD_REGISTER_POINT_STRUCT(rslidar_ros::Point,
//                                   (float, x, x)(float, y, y)(float, z, z)(float, intensity, intensity)(uint16_t, ring, ring)(double, timestamp, timestamp))

// namespace ouster_ros
// {
//   struct EIGEN_ALIGN16 Point
//   {
//     PCL_ADD_POINT4D;
//     float intensity;
//     uint32_t t;
//     uint16_t reflectivity;
//     uint8_t ring;
//     uint16_t noise;
//     uint32_t range;
//     EIGEN_MAKE_ALIGNED_OPERATOR_NEW
//   };
// } // namespace ouster_ros

// // clang-format off
// POINT_CLOUD_REGISTER_POINT_STRUCT(ouster_ros::Point,
//     (float, x, x)
//     (float, y, y)
//     (float, z, z)
//     (float, intensity, intensity)
//     // use std::uint32_t to avoid conflicting with pcl::uint32_t
//     (std::uint32_t, t, t)
//     (std::uint16_t, reflectivity, reflectivity)
//     (std::uint8_t, ring, ring)
//     (std::uint16_t, noise, noise)
//     (std::uint32_t, range, range)
// )

/**
 * 6D位姿点云结构定义
*/
struct PointXYZIRPYT
{
    PCL_ADD_POINT4D     
    PCL_ADD_INTENSITY;  
    float roll;         
    float pitch;
    float yaw;
    double time;
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW   
} EIGEN_ALIGN16;                    

POINT_CLOUD_REGISTER_POINT_STRUCT (PointXYZIRPYT,
                                   (float, x, x) (float, y, y)
                                   (float, z, z) (float, intensity, intensity)
                                   (float, roll, roll) (float, pitch, pitch) (float, yaw, yaw)
                                   (double, time, time))

typedef PointXYZIRPYT  PointTypePose;

// /**
// * GPS UTC时间结构体
// * <AUTHOR>
// * @version 1.0
// * @updated 2022-9-7
// */
// struct GPSUTCTIME
// {
// public:

//     /**
//     * 年 初始值：0
//     */
//     cx_ushort usYear;
//     /**
//     * 月 初始值：0
//     */
//     cx_byte byMonth;
//     /**
//     * 日 初始值：0
//     */
//     cx_byte byDay;
//     /**
//     * 小时 初始值 0
//     */
//     cx_byte byHour;
//     /**
//     * 分  初始值：0
//     */
//     cx_byte byMinute;
//     /**
//     * 秒   初始值 0
//     */
//     cx_float bySecond;



//     GPSUTCTIME()
//     {
//         clear();
//     }
//     void clear()
//     {
//         bySecond = 0;
//         byMonth = 0;
//         byDay = 0;
//         byHour =0;
//         byMinute = 0;
//         usYear = 0;
//     }

//     cx_bool IsValid()
//     {
//         return ((usYear > 0) && (byMonth > 0) && (byDay > 0));
//     }

// };

// struct IMUData
// {
//     cx_uint16 iID;
//     GPSUTCTIME stUTCTime;
//     cx_uint64 uiTimestamps;
//     cx_double dTimestamps;
//     cx_double dAcc[3];      //三轴加速度;
//     cx_double dW[3];        //三轴角速度
//     cx_double dPose[3];     //三轴姿态，横滚，俯仰，偏航
//     cx_double dCanSpeed;
//     cx_float dTemp;         //温度
//     cx_bool bValid;

//     void clear()
//     {
//         for (cx_int i = 0; i < 3; ++i)
//         {
//             dAcc[i] = 0.0;
//             dW[i] = 0.0;
//             dPose[i] = 0.0;
//         }
//         dTemp = 0.0;
//         dCanSpeed = 0.0;
//         uiTimestamps = 0;
//         dTimestamps = 0;
//         iID = 0;
//         bValid = false;
//     }

//     IMUData()
//     {
//         clear();
//     }

//     void operator = (const IMUData& sign)
//     {
//         bValid = sign.bValid;
//         iID = sign.iID;
//         uiTimestamps = sign.uiTimestamps;
//         dTimestamps = sign.dTimestamps;
//         stUTCTime = sign.stUTCTime;
//         dTemp = sign.dTemp;
//         dCanSpeed = sign.dCanSpeed;
//         for (cx_int i = 0; i < 3; ++i)
//         {
//             dAcc[i] = sign.dAcc[i];
//             dW[i] = sign.dW[i];
//             dPose[i] = sign.dPose[i];
//         }
//     }
// };

struct MotionModel
{
   cx_double timestamp;  
   cx_double vel;
   cx_double angle;

    MotionModel()
    {
       timestamp = 0.0;  
       vel = 0.0;
       angle = 0.0;
    }

    void operator = (const MotionModel& MM)
    {
       timestamp = MM.timestamp; 
       vel = MM.vel;
       angle = MM.angle;
    }
};

// 传感器测量组 
struct MeasureGroup 
{
    cx_double lidar_beg_time;      // 雷达开始时间
    cx_double lidar_end_time;      // 雷达结束时间
    cx_double package_end_time;    // 测量包结束时间
    cx_bool lidar_vaild;    // 雷达数据是否有效
    cx_bool leg_vaild;      // 腿式里程计数据是否有效
    cx_bool zero_vel_vaild;      // 是否为零速

    PointCloudXYZI::Ptr lidar;  // 雷达点云数据
    std::deque<IMUData> imu; // IMU数据
    std::deque<MotionModel> leg; // 腿式里程计数据

    MeasureGroup()
    {
        lidar_beg_time = 0.0;
        leg_vaild = false;
        this->lidar.reset(new PointCloudXYZI());
        zero_vel_vaild = false;
    };
};

template<typename T>
auto set_pose6d(const double t, const Matrix<T, 3, 1> &a, const Matrix<T, 3, 1> &g, \
                const Matrix<T, 3, 1> &v, const Matrix<T, 3, 1> &p, const Matrix<T, 3, 3> &R)
{
    Pose6D rot_kp;
    rot_kp.offset_time = t;
    for (int i = 0; i < 3; i++)
    {
        rot_kp.acc[i] = a(i);
        rot_kp.gyr[i] = g(i);
        rot_kp.vel[i] = v(i);
        rot_kp.pos[i] = p(i);
        for (int j = 0; j < 3; j++)  rot_kp.rot[i*3+j] = R(i,j);
    }
    return move(rot_kp);
}

// float calc_dist(PointType p1, PointType p2)
// {
//     float d = (p1.x - p2.x) * (p1.x - p2.x) + (p1.y - p2.y) * (p1.y - p2.y) + (p1.z - p2.z) * (p1.z - p2.z);
//     return d;
// }

template<typename T>
bool esti_plane(Matrix<T, 4, 1> &pca_result, const PointVector &point, const T &threshold)
{
    Matrix<T, NUM_MATCH_POINTS, 3> A;
    Matrix<T, NUM_MATCH_POINTS, 1> b;
    A.setZero();
    b.setOnes();
    b *= -1.0f;

    //求A/Dx + B/Dy + C/Dz + 1 = 0 的参数 
    for (int j = 0; j < NUM_MATCH_POINTS; j++)
    {
        A(j,0) = point[j].x;
        A(j,1) = point[j].y;
        A(j,2) = point[j].z;
    }

    Matrix<T, 3, 1> normvec = A.colPivHouseholderQr().solve(b);

    T n = normvec.norm();
    //pca_result是平面方程的4个参数  /n是为了归一化
    pca_result(0) = normvec(0) / n;
    pca_result(1) = normvec(1) / n;
    pca_result(2) = normvec(2) / n;
    pca_result(3) = 1.0 / n;

    //如果几个点中有距离该平面>threshold的点 认为是不好的平面 返回false
    for (int j = 0; j < NUM_MATCH_POINTS; j++)
    {
        if (fabs(pca_result(0) * point[j].x + pca_result(1) * point[j].y + pca_result(2) * point[j].z + pca_result(3)) > threshold)
        {
            return false;
        }
    }
    return true;
}



#endif