#ifndef YAML_CONFIG_H
#define YAML_CONFIG_H

#include <yaml.h>
#include <algorithm>
#include <cstring>
#include <map>
#include <sstream>
#include <stdexcept>
#include <string>
#include <vector>

namespace common_lib {

class YamlConfig {
public:
    // Delete copy constructor and assignment operator
    YamlConfig(const YamlConfig&) = delete;
    YamlConfig& operator=(const YamlConfig&) = delete;

    // Get singleton instance
    static YamlConfig& GetInstance();

    // Load YAML file
    bool LoadFile(const std::string& filepath);

    // Get parameter value with default
    template <typename T>
    T GetParam(const std::string& path, const T& default_value) const {
        T value;
        if (GetParam(path, &value)) {
            return value;
        }
        return default_value;
    }

    // Get parameter value (no default)
    template <typename T>
    bool GetParam(const std::string& path, T* value) const {
        auto it = config_data_.find(path);
        if (it == config_data_.end()) {
            std::string nested_path = ConvertToNestedPath(path);
            it = config_data_.find(nested_path);
            if (it == config_data_.end()) {
                return false;
            }
        }
        return ConvertValue(it->second, value);
    }

private:
    YamlConfig();
    
    // 通用模板声明
    template <typename T>
    bool ConvertValue(const std::string& str, T* value) const {
        std::istringstream iss(str);
        iss >> *value;
        return !iss.fail();
    }

    void ParseNode(const yaml_node_t* node, const std::string& parent_path);
    std::string ConvertToNestedPath(const std::string& path) const;

    std::map<std::string, std::string> config_data_;
    yaml_document_t document_;
};

// 特化声明（放在命名空间内，类定义外）
template <>
bool YamlConfig::ConvertValue<std::string>(const std::string& str, std::string* value) const;

template <>
bool YamlConfig::ConvertValue<bool>(const std::string& str, bool* value) const;

template <>
bool YamlConfig::ConvertValue<int>(const std::string& str, int* value) const;

template <>
bool YamlConfig::ConvertValue<double>(const std::string& str, double* value) const;

template <>
bool YamlConfig::ConvertValue<float>(const std::string& str, float* value) const;

template <>
bool YamlConfig::ConvertValue<std::vector<double>>(const std::string& str, std::vector<double>* value) const;

}  // namespace common_lib

#endif  // YAML_CONFIG_H