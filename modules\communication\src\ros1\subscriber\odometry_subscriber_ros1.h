/**
 *  @file odometry_subscriber_ros1.h
 *  @brief This file contains the ROS1 implementation of the odometry subscriber.
 * *  It subscribes to odometry data from a specified ROS topic and processes the incoming messages.
 * *  It inherits from the OdometrySubscriberBase class.
 */
#pragma once

#if COMMUNICATION_TYPE == ROS1

#include <ros/ros.h>
#include <nav_msgs/Odometry.h>

#include "subscriber_base.h"

namespace communication::ros1{

class OdometrySubscriberRos1 :  public OdometrySubscriberBase{
    public:
        OdometrySubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size = 100);

        ~OdometrySubscriberRos1() = default;

        void OdometryCallBackRos1(const nav_msgs::Odometry::ConstPtr &odom_msg);

    private:
        ros::NodeHandle& nh_;
        ros::Subscriber subscriber_;
    };

} // namespace communication::ros1{

#endif
