#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <nav_msgs/msg/odometry.hpp>

#include "ros2/core/subscriber_base_ros2.h"
#include "data_types/pose_data.h"

namespace communication::ros2
{

    class OdometryDataSubscriberRos2 : public SubscriberBaseRos2<PoseVelData, nav_msgs::msg::Odometry>
    {
    public:
        OdometryDataSubscriberRos2(rclcpp::Node::SharedPtr node,
                                   const std::string &topic,
                                   //    typename SubscriberBaseRos2<OdometryData, nav_msgs::msg::Odometry>::CallbackType callback = nullptr,
                                   size_t max_buffer_size = 10)
            : SubscriberBaseRos2<PoseVelData, nav_msgs::msg::Odometry>(node, topic, max_buffer_size)
        {
        }

    protected:
        virtual void FromMsg(const nav_msgs::msg::Odometry &msg, PoseVelData &data) override
        {
            data.pose_data.time = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9;
            // data.frame_id = msg.header.frame_id;
            // data.child_frame_id = msg.child_frame_id;

            // Position
            data.pose_data.position[0] = msg.pose.pose.position.x;
            data.pose_data.position[1] = msg.pose.pose.position.y;
            data.pose_data.position[2] = msg.pose.pose.position.z;

            // Orientation
            data.pose_data.orientation[0] = msg.pose.pose.orientation.x;
            data.pose_data.orientation[1] = msg.pose.pose.orientation.y;
            data.pose_data.orientation[2] = msg.pose.pose.orientation.z;
            data.pose_data.orientation[3] = msg.pose.pose.orientation.w;

            // Linear velocity
            data.vel[0] = msg.twist.twist.linear.x;
            data.vel[1] = msg.twist.twist.linear.y;
            data.vel[2] = msg.twist.twist.linear.z;

            // Angular velocity
            data.angle_vel[0] = msg.twist.twist.angular.x;
            data.angle_vel[1] = msg.twist.twist.angular.y;
            data.angle_vel[2] = msg.twist.twist.angular.z;
        }
    };

} // namespace communication::ros2

#endif
