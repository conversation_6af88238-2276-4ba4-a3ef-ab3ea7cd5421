#include "cx_observer.h"
namespace common_lib {
void CXObservable::Attach(CXObserver *pObs) {
  if (!pObs)
    return;
  observers_.insert(pObs);
}

void CXObservable::Detach(CXObserver *pObs) {
  if (!pObs)
    return;
  observers_.erase(pObs);
}

void CXObservable::DetachAll() {
  observers_.clear();
}

void CXObservable::SetChanged() {
  change_flag_ = true;
}

void CXObservable::ClearChanged() {
  change_flag_ = false;
}

bool CXObservable::HasChanged() {
  return change_flag_;
}

int CXObservable::GetObserversCount() {
  return observers_.size();
}

void CXObservable::Notify(void *pArg /* = NULL */) {
  if (!HasChanged()) {
    return;
  }

  // cout << "notify observers��" << endl;
  ClearChanged();

  for (set<CXObserver *>::iterator itr = observers_.begin(); itr != observers_.end(); itr++) {
    (*itr)->Update(this, pArg);
  }
}

SUBJECT_TYPE CXObservable::GetType() {
  return subject_type_;
}
}  // namespace common_lib