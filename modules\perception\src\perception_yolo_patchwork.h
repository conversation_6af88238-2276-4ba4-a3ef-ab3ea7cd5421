#ifndef PERCEPTION__YOLO__PATCHWORK___H
#define PERCEPTION__YOLO__PATCHWORK___H

#include "perception_impl.h"
#include "image_segment/image_segment.h"
#include "pointcloud_prejection.h"
#include "depth_pointcloud/depth_pointcloud.h"
namespace perception{


class PerceptionYoloPatchwork: public PerceptionImpl{

public:
    PerceptionYoloPatchwork(const std::string& config_file_path);
    virtual ~PerceptionYoloPatchwork();

    void        SetOutputCallback(PerceptionOutputCallback callback);

    virtual     void PushCloud(PointCloudT::Ptr cloud);
    virtual     void PushRgbImage(cv::Mat rgb);
    virtual     void PushDepthImage(cv::Mat depth);
    virtual     void SetRgbIntrinsic(const CameraInfo& info );
    virtual     void SetDepthIntrinsic(const CameraInfo& info );
    virtual     void SetDepth2RgbTransform(const TransformExtrinsics& transform);

protected:
    void            LidarCloudLoop();
    void            YoloLoop();
    void            DepthCloudLoop();
    PerceptionOut   LidarCloudSegment(PointCloudT::Ptr cloudin);

private:

    
    std::unique_ptr<ObstacleDevide>         obstacle_devider_;

    BoolCondition                           lidar_arrived_signal_;
    BoolCondition                           rgb_arrived_signal_;
    BoolCondition                           depth_arrived_signal_;


private:
    bool                                exit_ =false;
    std::thread                         thread_lidar_cloud_;
    std::thread                         thread_yolo_;
    std::thread                         thread_depth_cloud_;

    bool                                rgb_intrinsic_recieved_ = false;
    bool                                depth_intrinsic_recieved_ = false;
    bool                                rgb2depth_transform_recieved_ =false ;

    ThreadSafeTimed<cv::Mat>            timed_rgb_;
    cv::Mat                             depth_image_;
    ThreadSafeTimed<PointCloudT::Ptr>   timed_stereo_cloud_;
    PointCloudT::Ptr                    lidar_cloud_;

    Eigen::Matrix4d         depth2rgb_transform_;
    CameraInfo              rgb_camera_info_;
    CameraInfo              depth_camera_info_;
    Eigen::VectorXd         lidar2rgb_transform_;  // tx,ty,tz , qx,qy,qz,qw
    float                   max_depth_;
    size_t                  depth_grid_;            //相机下采样网格参数



    std::unique_ptr<ImageSegment>               image_segment_;
    std::unique_ptr<Reprejection>               reprejection_;
    std::unique_ptr<DepthPointCloudConvert>     depth_pointcloud_convert_;
    std::string                                 depth_rknn_model_;
    
    
    PerceptionOut       output_;

};

}

#endif