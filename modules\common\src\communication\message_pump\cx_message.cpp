#include "cx_message.h"

#include "cx_time.h"
namespace common_lib {
CXMessage::CXMessage(cx_uint messageId) {
  m_messageId = messageId;
  m_wParam = 0;
  m_lParam = nullptr;
  m_time = GetTimestamp();
}

CXMessage::~CXMessage() {
  DELETE_S(m_lParam);
}
CXMessage &CXMessage::operator=(const CXMessage &rightValue) {
  if (this == &rightValue) {
    return *this;
  } else {
    this->m_messageId = rightValue.m_messageId;
    this->m_wParam = rightValue.m_wParam;
    this->m_lParam = rightValue.m_lParam;
    this->m_time = rightValue.m_time;

    return *this;
  }
}
}  // namespace common_lib