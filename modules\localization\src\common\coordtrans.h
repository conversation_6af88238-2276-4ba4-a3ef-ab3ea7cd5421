#pragma once

#include <cmath>
#include <Eigen/Core>
#include <Eigen/Eigen>
#include <Eigen/Dense>
#include "use-ikfom.h"

void pointBodyToWorld(PointType const *const pi, PointType *const ,state_ikfom &state_point);

void RGBpointBodyLidarToIMU(PointType const *const pi, PointType *const po, state_ikfom &state_point);

template <typename T>
void pointBodyToWorld(const Eigen::Matrix<T, 3, 1> &pi, Eigen::Matrix<T, 3, 1> &po, state_ikfom &state_point);