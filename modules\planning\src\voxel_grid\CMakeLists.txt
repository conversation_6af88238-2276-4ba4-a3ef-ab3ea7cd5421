cmake_minimum_required(VERSION 3.10)
project(voxel_grid)

# 查找catkin包
find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
    ${catkin_INCLUDE_DIRS}
)

# 源文件
file(GLOB VOXEL_GRID_SOURCES
    ${CMAKE_CURRENT_SOURCE_DIR}/*.cpp
)

# 创建库
add_library(voxel_grid ${VOXEL_GRID_SOURCES})

# 链接依赖
target_link_libraries(voxel_grid
    ${catkin_LIBRARIES}
)