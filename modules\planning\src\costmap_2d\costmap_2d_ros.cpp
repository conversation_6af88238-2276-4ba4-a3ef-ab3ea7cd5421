/*********************************************************************
 *
 * Software License Agreement (BSD License)
 *
 *  Copyright (c) 2008, 2013, Willow Garage, Inc.
 *  All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *   * Redistributions of source code must retain the above copyright
 *     notice, this list of conditions and the following disclaimer.
 *   * Redistributions in binary form must reproduce the above
 *     copyright notice, this list of conditions and the following
 *     disclaimer in the documentation and/or other materials provided
 *     with the distribution.
 *   * Neither the name of Willow Garage, Inc. nor the names of its
 *     contributors may be used to endorse or promote products derived
 *     from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 *  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 *  COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 *  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 *  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 *  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 *  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 *  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 *  POSSIBILITY OF SUCH DAMAGE.
 *
 * Author: Eitan Marder-Eppstein
 *         David V. Lu!!
 *********************************************************************/
#include <costmap_2d/layered_costmap.h>
#include <costmap_2d/costmap_2d_ros.h>
#include <cstdio>
#include <string>
#include <algorithm>
#include <vector>
#include <cmath>
#include <chrono>
#include <thread>

using namespace std;

namespace costmap_2d
{

void move_parameter(ros::NodeHandle& old_h, ros::NodeHandle& new_h, std::string name, bool should_delete = true)
{
  if (!old_h.hasParam(name))
    return;

  XmlRpc::XmlRpcValue value;
  old_h.getParam(name, value);
  new_h.setParam(name, value);
  if (should_delete) old_h.deleteParam(name);
}

Costmap2DROS::Costmap2DROS(const std::string& name) :
    layered_costmap_(NULL),
    name_(name),
    global_frame_("map"),
    robot_base_frame_("base_link"),
    transform_tolerance_(0.3),
    map_update_thread_shutdown_(false),
    stop_updates_(false),
    initialized_(true),
    stopped_(false),
    map_update_thread_(NULL),
    last_publish_(0),
    publish_cycle(0.1),
    publisher_(NULL),
    footprint_padding_(0.01)
{
  // Initialize default configuration
  Costmap2DConfig config;
  global_frame_ = config.global_frame;
  robot_base_frame_ = config.robot_base_frame;
  transform_tolerance_ = config.transform_tolerance;

  // check if we want a rolling window version of the costmap
  bool rolling_window = config.rolling_window;
  bool track_unknown_space = config.track_unknown_space;

  layered_costmap_ = new LayeredCostmap(global_frame_, rolling_window, track_unknown_space);

  // Set default footprint from configuration
  if (!config.footprint.empty()) {
    setUnpaddedRobotFootprint(makeFootprintFromString(config.footprint));
  } else {
    setUnpaddedRobotFootprint(makeFootprintFromRadius(config.robot_radius));
  }

  publisher_ = new Costmap2DPublisher(layered_costmap_->getCostmap(), global_frame_, "costmap");

  // create a thread to handle updating the map
  stop_updates_ = false;
  initialized_ = true;
  stopped_ = false;

  old_config_ = config;
}

void Costmap2DROS::setUnpaddedRobotFootprintPolygon(const planning_common::Polygon& footprint)
{
  setUnpaddedRobotFootprint(toPointVector(footprint));
}

Costmap2DROS::~Costmap2DROS()
{
  map_update_thread_shutdown_ = true;
  if (map_update_thread_ != NULL)
  {
    map_update_thread_->join();
    delete map_update_thread_;
  }
  if (publisher_ != NULL)
    delete publisher_;

  delete layered_costmap_;
}





void Costmap2DROS::reconfigureCB(Costmap2DConfig &config, uint32_t level)
{
  transform_tolerance_ = config.transform_tolerance;
  if (map_update_thread_ != NULL)
  {
    map_update_thread_shutdown_ = true;
    map_update_thread_->join();
    delete map_update_thread_;
    map_update_thread_ = NULL;
  }
  map_update_thread_shutdown_ = false;
  double map_update_frequency = config.update_frequency;

  double map_publish_frequency = config.publish_frequency;
  if (map_publish_frequency > 0)
    publish_cycle = 1.0 / map_publish_frequency;
  else
    publish_cycle = -1;

  // find size parameters
  double map_width_meters = config.width, map_height_meters = config.height, resolution = config.resolution, origin_x =
             config.origin_x,
         origin_y = config.origin_y;

  if (!layered_costmap_->isSizeLocked())
  {
    layered_costmap_->resizeMap((unsigned int)(map_width_meters / resolution),
                                (unsigned int)(map_height_meters / resolution), resolution, origin_x, origin_y);
  }

  // If the padding has changed, call setUnpaddedRobotFootprint() to
  // re-apply the padding.
  if (footprint_padding_ != config.footprint_padding)
  {
    footprint_padding_ = config.footprint_padding;
    setUnpaddedRobotFootprint(unpadded_footprint_);
  }

  readFootprintFromConfig(config, old_config_);

  old_config_ = config;

  // only construct the thread if the frequency is positive
  if(map_update_frequency > 0.0)
    map_update_thread_ = new boost::thread(std::bind(&Costmap2DROS::mapUpdateLoop, this, map_update_frequency));
}

void Costmap2DROS::readFootprintFromConfig(const Costmap2DConfig &new_config,
                                           const Costmap2DConfig &old_config)
{
  // Only change the footprint if footprint or robot_radius has
  // changed.  Otherwise we might overwrite a footprint sent by
  // configuration updates.
  if (new_config.footprint == old_config.footprint &&
      new_config.robot_radius == old_config.robot_radius)
  {
    return;
  }

  if (new_config.footprint != "" && new_config.footprint != "[]")
  {
    std::vector<planning_common::Point> new_footprint;
    if (makeFootprintFromString(new_config.footprint, new_footprint))
    {
        setUnpaddedRobotFootprint(new_footprint);
    }
    else
    {
        // Log error - invalid footprint string
        printf("Invalid footprint string from configuration\n");
    }
  }
  else
  {
    // robot_radius may be 0, but that must be intended at this point.
    setUnpaddedRobotFootprint(makeFootprintFromRadius(new_config.robot_radius));
  }
}

void Costmap2DROS::setUnpaddedRobotFootprint(const std::vector<planning_common::Point>& points)
{
  unpadded_footprint_ = points;
  padded_footprint_ = points;
  padFootprint(padded_footprint_, footprint_padding_);

  layered_costmap_->setFootprint(padded_footprint_);
}



void Costmap2DROS::mapUpdateLoop(double frequency)
{
  auto sleep_duration = std::chrono::milliseconds(static_cast<int>(1000.0 / frequency));

  while (!map_update_thread_shutdown_)
  {
    auto start_time = std::chrono::high_resolution_clock::now();

    updateMap();

    if (publish_cycle > 0 && layered_costmap_->isInitialized())
    {
      unsigned int x0, y0, xn, yn;
      layered_costmap_->getBounds(&x0, &xn, &y0, &yn);
      publisher_->updateBounds(x0, xn, y0, yn);

      auto now = std::chrono::high_resolution_clock::now();
      auto time_since_last_publish = std::chrono::duration<double>(now - std::chrono::high_resolution_clock::time_point()).count() - last_publish_;
      if (time_since_last_publish >= publish_cycle)
      {
        publisher_->publishCostmap();
        last_publish_ = std::chrono::duration<double>(now - std::chrono::high_resolution_clock::time_point()).count();
      }
    }

    std::this_thread::sleep_for(sleep_duration);

    auto end_time = std::chrono::high_resolution_clock::now();
    auto cycle_time = std::chrono::duration<double>(end_time - start_time).count();
    if (cycle_time > 1.0 / frequency)
    {
      printf("Map update loop missed its desired rate of %.4fHz... the loop actually took %.4f seconds\n",
             frequency, cycle_time);
    }
  }
}

void Costmap2DROS::updateMap()
{
  if (!stop_updates_)
  {
    // get global pose
    planning_common::PoseStamped pose;
    if (getRobotPose (pose))
    {
      double x = pose.pose.position.x,
             y = pose.pose.position.y,
             yaw = getYaw(pose.pose.orientation);

      layered_costmap_->updateMap(x, y, yaw);

      initialized_ = true;
    }
  }
}

void Costmap2DROS::start()
{
  std::vector < boost::shared_ptr<Layer> > *plugins = layered_costmap_->getPlugins();
  // check if we're stopped or just paused
  if (stopped_)
  {
    // if we're stopped we need to re-activate layers
    for (vector<boost::shared_ptr<Layer> >::iterator plugin = plugins->begin(); plugin != plugins->end();
        ++plugin)
    {
      (*plugin)->activate();
    }
    stopped_ = false;
  }
  stop_updates_ = false;

  // block until the costmap is re-initialized.. meaning one update cycle has run
  while (!initialized_ && map_update_thread_)
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
  }
}

void Costmap2DROS::stop()
{
  stop_updates_ = true;
  std::vector < boost::shared_ptr<Layer> > *plugins = layered_costmap_->getPlugins();
  // unsubscribe from topics
  for (vector<boost::shared_ptr<Layer> >::iterator plugin = plugins->begin(); plugin != plugins->end();
      ++plugin)
  {
    (*plugin)->deactivate();
  }
  initialized_ = false;
  stopped_ = true;
}

void Costmap2DROS::pause()
{
  stop_updates_ = true;
  initialized_ = false;
}

void Costmap2DROS::resume()
{
  stop_updates_ = false;

  // block until the costmap is re-initialized.. meaning one update cycle has run
  while (!initialized_)
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
  }
}


void Costmap2DROS::resetLayers()
{
  Costmap2D* top = layered_costmap_->getCostmap();
  top->resetMap(0, 0, top->getSizeInCellsX(), top->getSizeInCellsY());
  std::vector < boost::shared_ptr<Layer> > *plugins = layered_costmap_->getPlugins();
  for (vector<boost::shared_ptr<Layer> >::iterator plugin = plugins->begin(); plugin != plugins->end();
      ++plugin)
  {
    (*plugin)->reset();
  }
}

bool Costmap2DROS::getRobotPose(planning_common::PoseStamped& global_pose) const
{
  // In a non-ROS environment, we need to get the robot pose from another source
  // For now, return a default pose at origin - this should be replaced with
  // actual robot pose from your localization system
  global_pose.header.frame_id = global_frame_;
  global_pose.header.stamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
      std::chrono::high_resolution_clock::now().time_since_epoch()).count() / 1e9;

  global_pose.pose.position.x = 0.0;
  global_pose.pose.position.y = 0.0;
  global_pose.pose.position.z = 0.0;
  global_pose.pose.orientation.x = 0.0;
  global_pose.pose.orientation.y = 0.0;
  global_pose.pose.orientation.z = 0.0;
  global_pose.pose.orientation.w = 1.0;

  return true;
}

void Costmap2DROS::getOrientedFootprint(std::vector<planning_common::Point>& oriented_footprint) const
{
  planning_common::PoseStamped global_pose;
  if (!getRobotPose(global_pose))
    return;

  double yaw = getYaw(global_pose.pose.orientation);
  transformFootprint(global_pose.pose.position.x, global_pose.pose.position.y, yaw,
                     padded_footprint_, oriented_footprint);
}

}  // namespace costmap_2d
