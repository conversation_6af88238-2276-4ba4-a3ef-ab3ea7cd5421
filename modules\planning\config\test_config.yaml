# RRT*全局规划器测试配置文件
# 测试参数配置

# 测试模式配置
enable_ros_test: true           # 启用ROS数据测试
enable_simulation_test: true    # 启用模拟测试
enable_visualization: true      # 启用可视化
enable_debug_output: true       # 启用调试输出

# 测试时间配置
test_duration: 300.0            # 测试持续时间(秒)
goal_update_interval: 5.0       # 目标点更新间隔(秒)

# 机器人配置
robot_speed: 0.5                # 机器人移动速度(m/s)

# 文件配置
map_file: ""                    # 地图文件路径(空表示使用默认测试地图)
output_file: "test_results.txt" # 测试结果输出文件

# RRT*规划器参数配置
rrt_star:
  # 基本参数
  max_nodes_num: 10000          # 最大节点数
  plan_time_out: 5.0            # 规划超时时间(秒)
  search_radius: 1.0            # 搜索半径(米)
  goal_radius: 0.2              # 目标半径(米)
  
  # 扩展参数
  epsilon_min: 0.1              # 最小扩展距离(米)
  epsilon_max: 1.0              # 最大扩展距离(米)
  
  # 路径优化参数
  path_point_spacing: 0.1       # 路径点间距(米)
  angle_difference: 0.1         # 角度差异阈值(弧度)
  
  # 规划模式
  planning_mode: 0              # 0:标准模式, 1:快速模式, 2:精确模式
  goal_bias_probability: 0.2    # 目标偏向采样概率
  use_informed_sampling: true   # 使用信息采样
  use_bidirectional_search: true # 使用双向搜索
  
  # 可视化配置
  enable_visualization: true    # 启用可视化
  enable_debug: true            # 启用调试输出
  
  # 性能配置
  planning_frequency: 10.0      # 规划频率(Hz)
  collision_threshold: 50       # 碰撞检测阈值
  
  # 路径优化配置
  path_smoothing_factor: 0.5    # 路径平滑因子
  path_simplify_tolerance: 0.1  # 路径简化容差(米)

# 通信话题配置
topics:
  # 订阅话题
  odometry_topic: "/odom"                    # 里程计话题
  goal_topic: "/move_base_simple/goal"       # 目标点话题
  map_topic: "/map"                          # 地图话题
  
  # 发布话题
  path_topic: "/test_global_path"            # 全局路径话题
  accessable_topic: "/test_path_accessable"  # 路径可达性话题
  visualization_topic: "/test_tree_visualization" # 树可视化话题

# 测试场景配置
test_scenarios:
  # 场景1: 简单环境测试
  simple_test:
    name: "简单环境测试"
    start_pose: [-8.0, -8.0, 0.0]           # 起始位姿 [x, y, yaw]
    goal_poses:                              # 目标位姿列表
      - [8.0, 8.0, 0.0]
      - [-8.0, 8.0, 0.0]
      - [8.0, -8.0, 0.0]
    duration: 60.0                           # 场景持续时间(秒)
    
  # 场景2: 复杂环境测试
  complex_test:
    name: "复杂环境测试"
    start_pose: [-9.0, -9.0, 0.0]
    goal_poses:
      - [9.0, 9.0, 0.0]
      - [-9.0, 9.0, 0.0]
      - [9.0, -9.0, 0.0]
      - [0.0, 0.0, 0.0]
    duration: 120.0
    
  # 场景3: 随机目标测试
  random_test:
    name: "随机目标测试"
    start_pose: [-8.0, -8.0, 0.0]
    random_goals: true                       # 启用随机目标
    goal_count: 10                           # 随机目标数量
    duration: 180.0

# 性能评估配置
performance_evaluation:
  # 评估指标
  metrics:
    - success_rate                           # 成功率
    - planning_time                          # 规划时间
    - path_length                            # 路径长度
    - node_count                             # 节点数
    - path_smoothness                        # 路径平滑度
    
  # 统计配置
  statistics:
    min_attempts: 10                         # 最小尝试次数
    confidence_level: 0.95                   # 置信水平
    outlier_threshold: 3.0                   # 异常值阈值(标准差倍数)
    
  # 输出配置
  output:
    save_detailed_logs: true                 # 保存详细日志
    generate_plots: true                     # 生成图表
    export_csv: true                         # 导出CSV数据 