add_library(${PROJECT_NAME}_loc SHARED
    localization.cpp
)
find_package(OpenCV REQUIRED)
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
    
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/thread
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/model
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/algorithm
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/file
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/platform
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/config

    ${CMAKE_CURRENT_SOURCE_DIR}/../../../communication/
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../communication/include/data_types

    ${CMAKE_CURRENT_SOURCE_DIR}/../data_pretreat
    ${CMAKE_CURRENT_SOURCE_DIR}/../map_manager
    ${CMAKE_CURRENT_SOURCE_DIR}/../reloc
    ${CMAKE_CURRENT_SOURCE_DIR}/../reloc/alg
    ${CMAKE_CURRENT_SOURCE_DIR}/../reloc/alg/basis
    ${CMAKE_CURRENT_SOURCE_DIR}/../reloc/reloc_plugin
    ${CMAKE_CURRENT_SOURCE_DIR}/../zero_detect
    ${CMAKE_CURRENT_SOURCE_DIR}/../communication
    ${CMAKE_CURRENT_SOURCE_DIR}/../common
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/eskf
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/ikd-Tree
    ${EIGEN3_INCLUDE_DIR}
    ${PCL_INCLUDE_DIRS}
    ${OpenCV_INCLUDE_DIRS}
    ${GTSAM_INCLUDE_DIR}
    ${GeographicLib_INCLUDE_DIRS}
)

target_link_libraries(${PROJECT_NAME}_loc
    ${PROJECT_NAME}_common
    ${PROJECT_NAME}_pretreat
    ${PROJECT_NAME}_map_manager
    ${PROJECT_NAME}_reloc
    ${PROJECT_NAME}_zero_detect
    ${PROJECT_NAME}_communication
    common_lib
    communication_core
    ${EIGEN3_LIBRARIES}
    ${PCL_LIBRARIES}
    ${OpenCV_LIBS}
    ${GeographicLib_LIBRARIES}
    #fmt::fmt
    #TBB::tbb
    yaml-cpp
)

# set_target_properties(${PROJECT_NAME}_loc PROPERTIES
#     CXX_VISIBILITY_PRESET default
#     VISIBILITY_INLINES_HIDDEN OFF
#     POSITION_INDEPENDENT_CODE ON
# )
