#pragma once
#include <rclcpp/rclcpp.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <geometry_msgs/msg/transform_stamped.hpp>

#include "ros2/core/publisher_base_ros2.h"
#include "data_types/pose_data.h"

namespace communication::ros2
{

    class TFDataPublisherRos2 : public TFDataPublisherBase
    {
    public:
        TFDataPublisherRos2(rclcpp::Node::SharedPtr node,
                            const std::string &frame_id,
                            const std::string &child_frame_id)
            : TFDataPublisherBase(child_frame_id + "_" + frame_id + "_tf", 1), node_(node), tf_broadcaster_(node_), frame_id_(frame_id), child_frame_id_(child_frame_id)
        {
        }

        ~TFDataPublisherRos2() = default;

    protected:
        virtual void PublishMsg() override
        {
            tf_broadcaster_.sendTransform(transform_msg_);
        }

        virtual void ToMsg() override
        {
            transform_msg_.header.stamp = rclcpp::Time(static_cast<int64_t>(data_.time * 1e9)); // node_->now();
            transform_msg_.header.frame_id = frame_id_;
            transform_msg_.child_frame_id = child_frame_id_;
            transform_msg_.transform.translation.x = data_.position[0];
            transform_msg_.transform.translation.y = data_.position[1];
            transform_msg_.transform.translation.z = data_.position[2];
            transform_msg_.transform.rotation.x = data_.orientation[0];
            transform_msg_.transform.rotation.y = data_.orientation[1];
            transform_msg_.transform.rotation.z = data_.orientation[2];
            transform_msg_.transform.rotation.w = data_.orientation[3];
        }

        virtual int GetSubscriberCount() override
        {
            return 1; // TF doesn't have subscribers in traditional sense
        }

    private:
        rclcpp::Node::SharedPtr node_;
        tf2_ros::TransformBroadcaster tf_broadcaster_;
        geometry_msgs::msg::TransformStamped transform_msg_;
        std::string frame_id_;
        std::string child_frame_id_;
    };

} // namespace communication::ros2
