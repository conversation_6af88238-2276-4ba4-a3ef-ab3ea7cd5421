// calibration.cpp
#include "calibration.h"
#include <iostream>
#include <chrono>
#include <set>
#include <thread>
#include <cmath>

using namespace std;

namespace calibration {

inline double getCurrentTimeSeconds() {
    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration<double>(duration).count();
}

Calibration::Calibration(const std::string& name)
    : name_(name), is_running_(false), is_initialized_(false) {

    pid_x = std::make_unique<PIDController>(0.5, 0.0, 0.1);
    pid_y = std::make_unique<PIDController>(0.5, 0.0, 0.1);
    pid_yaw = std::make_unique<PIDController>(0.5, 0.0, 0.1);

    pid_x->setOutputLimits(-0.5, 0.5);
    pid_y->setOutputLimits(-0.1, 0.1);
    pid_yaw->setOutputLimits(-0.1, 0.1);
}

Calibration::~Calibration() {
    stop();
}

bool Calibration::init() {
    is_initialized_ = true;
    return true;
}

void Calibration::start() {
    if (!is_initialized_ || is_running_) return;
    is_running_ = true;
    worker_thread_ = std::thread(&Calibration::controlLoop, this);
}

void Calibration::stop() {
    if (!is_running_) return;
    is_running_ = false;
    if (worker_thread_.joinable()) worker_thread_.join();
}

void Calibration::setPIDParameters(double p_yaw, double i_yaw, double d_yaw,
                                   double p_x, double i_x, double d_x,
                                   double p_y, double i_y, double d_y) {
    if (is_initialized_ && pid_x && pid_y && pid_yaw) {
        pid_x->setParameters(p_x, i_x, d_x);
        pid_y->setParameters(p_y, i_y, d_y);
        pid_yaw->setParameters(p_yaw, i_yaw, d_yaw);
        std::cout << "set parameter ok" << std::endl;
    }
}

/**
 * @brief 设置误差限幅参数
 * @param errorYaw_max Yaw方向最大误差
 * @param errorYaw_min Yaw方向最小误差
 * @param errorX_max X方向最大误差
 * @param errorY_max Y方向最大误差
 */
void Calibration::setErrorLimits(double errorYaw_max, double errorYaw_min, 
                                double errorX_max, double errorY_max) {
    this->errorYaw_max = errorYaw_max;
    this->errorYaw_min = errorYaw_min;
    this->errorX_max = errorX_max;
    this->errorY_max = errorY_max;
}

/**
 * @brief 设置速度限幅参数
 * @param X_max X方向最大速度
 * @param Y_max Y方向最大速度
 * @param Yaw_max Yaw方向最大角速度
 */
void Calibration::setVelocityLimits(double X_max, double Y_max, double Yaw_max) {
    this->X_max = X_max;
    this->Y_max = Y_max;
    this->Yaw_max = Yaw_max;
    
    // 更新PID控制器的输出限幅
    if (pid_x) pid_x->setOutputLimits(-X_max, X_max);
    if (pid_y) pid_y->setOutputLimits(-Y_max, Y_max);
    if (pid_yaw) pid_yaw->setOutputLimits(-Yaw_max, Yaw_max);
}

/**
 * @brief 设置精度参数
 * @param set_yaw_precision Yaw方向精度
 * @param set_x_precision X方向精度
 * @param set_y_precision Y方向精度
 */
void Calibration::setPrecision(double set_yaw_precision, double set_x_precision, double set_y_precision) {
    this->set_yaw_precision = set_yaw_precision;
    this->set_x_precision = set_x_precision;
    this->set_y_precision = set_y_precision;
}

void Calibration::inputOdometry(const Odometry& odom) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    odomHandler(std::make_shared<const Odometry>(odom));
}

void Calibration::inputGoal(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    goalHandler(std::make_shared<const PoseStamped>(goal));
}

void Calibration::inputWebGoal(const PoseStamped& goal) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    goalHandler(std::make_shared<const PoseStamped>(goal));
}

void Calibration::inputMode(const BoolMsg& mode) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    nav_start = mode.data ? 1 : 0;
}

void Calibration::inputTerrainCloud(const PointCloud2& cloud) {
    // TODO: Implement point cloud processing if needed
}

void Calibration::setSpeedPublishCallback(std::function<void(const std::shared_ptr<TwistMsg>&)> cb) {
    speed_callback_ = cb;
}

void Calibration::setStopPublishCallback(std::function<void(const std::shared_ptr<Int8Msg>&)> cb) {
    stop_callback_ = cb;
}

void Calibration::setInnerStopPublishCallback(std::function<void(const std::shared_ptr<Int8Msg>&)> cb) {
    inner_stop_callback_ = cb;
}

void Calibration::setModePublishCallback(std::function<void(const std::shared_ptr<BoolMsg>&)> cb) {
    mode_callback_ = cb;
}

void Calibration::goalHandler(const std::shared_ptr<const PoseStamped>& goal) {
    goalX = goal->pose.position.x;
    goalY = goal->pose.position.y;
    goalYaw = goal->pose.orientation.toYaw();
    nav_start = 1;
    arrived = 0;
}

void Calibration::odomHandler(const std::shared_ptr<const Odometry>& odom) {
    vehicleX = odom->pose.position.x;
    vehicleY = odom->pose.position.y;
    vehicleYaw = odom->pose.orientation.toYaw();
}

double Calibration::normalizeAngle(double angle) {
    while (angle > M_PI) angle -= 2. * M_PI;
    while (angle < -M_PI) angle += 2. * M_PI;
    return angle;
}

void Calibration::controlLoop() {
    const double loop_rate = 100.0; // 100Hz
    const auto sleep_duration = std::chrono::duration<double>(1.0 / loop_rate);
    double last_linear_x = 0.0, last_linear_y = 0.0, last_angular_z = 0.0;
    double start_time = 0.0;
    bool pose_calibration = false;
    bool time_started = false;

    while (is_running_) {
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            
            if (nav_start == 1) {
                double errorX = cos(vehicleYaw) * (goalX - vehicleX) + sin(vehicleYaw) * (goalY - vehicleY);
                double errorY = -sin(vehicleYaw) * (goalX - vehicleX) + cos(vehicleYaw) * (goalY - vehicleY);
                double errorYaw = normalizeAngle(goalYaw - vehicleYaw);
                double use_errorYaw = errorYaw;

                auto cmd_vel = std::make_shared<TwistMsg>();

                if (std::abs(errorX) >= set_x_precision || std::abs(errorY) >= set_y_precision) {
                    pose_calibration = false;
                    if (pid_y) cmd_vel->linear_vel.y() = pid_y->compute(errorY);
                    last_linear_y = cmd_vel->linear_vel.y();
                    if (pid_x) cmd_vel->linear_vel.x() = pid_x->compute(errorX);
                    last_linear_x = cmd_vel->linear_vel.x();

                    std::cout << "errorX:" << errorX << ", errorY:" << errorY << std::endl;
                } else {
                    pose_calibration = true;
                    if (use_errorYaw < errorYaw_min) use_errorYaw = errorYaw_min;

                    if (errorYaw >= 0) {
                        if (pid_yaw) cmd_vel->angular_vel.z() = std::abs(pid_yaw->compute(use_errorYaw));
                    } else {
                        if (pid_yaw) cmd_vel->angular_vel.z() = -std::abs(pid_yaw->compute(use_errorYaw));
                    }
                    last_angular_z = cmd_vel->angular_vel.z();

                    std::cout << "web target yaw:" << goalYaw << ", unit:rad." << std::endl;
                    std::cout << "X:" << vehicleX << ", Y:" << vehicleY << ", angle:" << vehicleYaw << ", unit:rad." << std::endl;
                }

                if (std::abs(errorYaw) <= set_yaw_precision && pose_calibration) {
                    if (!time_started) {
                        start_time = getCurrentTimeSeconds();
                        time_started = true;
                    }
                    
                    double end_time = getCurrentTimeSeconds();
                    if (end_time - start_time >= 1.0 && arrived == 0) {
                        cmd_vel->linear_vel.x() = 0;
                        cmd_vel->linear_vel.y() = 0;
                        cmd_vel->angular_vel.z() = 0;
                        
                        if (mode_callback_) {
                            auto mode_msg = std::make_shared<BoolMsg>(false);
                            mode_callback_(mode_msg);
                        }
                        
                        if (stop_callback_) {
                            auto stop_msg = std::make_shared<Int8Msg>(1);
                            stop_callback_(stop_msg);
                        }
                        
                        if (inner_stop_callback_) {
                            auto inner_stop_msg = std::make_shared<Int8Msg>(1);
                            inner_stop_callback_(inner_stop_msg);
                        }
                        
                        arrived = 1;
                        std::cout << "***********已到达目标点***********" << std::endl;
                        std::cout << "X方向误差:" << errorX << ", Y方向误差:" << errorY 
                                  << ", 角度误差:" << errorYaw << ", unit:rad" << std::endl;
                        std::cout << "X:" << vehicleX << ", Y:" << vehicleY 
                                  << ", 角度:" << vehicleYaw << ", unit:rad" << std::endl;
                    }
                } else {
                    time_started = false;
                }

                if (speed_callback_) {
                    speed_callback_(cmd_vel);
                }
            }
        }

        printStatus();
        std::this_thread::sleep_for(sleep_duration);
    }
}

void Calibration::printStatus() const {
    std::cout << "[" << name_ << "] Status - "
              << "X: " << vehicleX << ", Y: " << vehicleY
              << ", Yaw: " << vehicleYaw << " | "
              << "GoalX: " << goalX << ", GoalY: " << goalY
              << ", GoalYaw: " << goalYaw
              << ", Arrived: " << (arrived ? "Yes" : "No") << std::endl;
}

} // namespace calibration
