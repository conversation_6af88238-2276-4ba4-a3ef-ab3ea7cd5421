#ifndef OBSTACLE_STOP_H
#define OBSTACLE_STOP_H

#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <fstream>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/common/time.h>
#include <pcl/registration/icp.h>
#include <pcl/io/pcd_io.h>

using namespace std;

// 去ROS化的消息结构体
namespace obstacle_stop_no_ros {

const double PI = 3.1415926;

/**
 * @brief 时间戳结构
 */
struct TimeStamp {
    double sec;
    
    TimeStamp() : sec(0.0) {}
    TimeStamp(double s) : sec(s) {}
    
    static TimeStamp now() {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        double seconds = std::chrono::duration<double>(duration).count();
        return TimeStamp(seconds);
    }
    
    double toSec() const { return sec; }
};

/**
 * @brief 消息头结构
 */
struct Header {
    TimeStamp stamp;
    std::string frame_id;
    
    Header() : frame_id("vehicle") {}
    
    void setCurrentTime() {
        stamp = TimeStamp::now();
    }
};

/**
 * @brief 3D点结构
 */
struct Point {
    double x, y, z;
    
    Point() : x(0.0), y(0.0), z(0.0) {}
    Point(double _x, double _y, double _z) : x(_x), y(_y), z(_z) {}
};

/**
 * @brief 四元数结构
 */
struct Quaternion {
    double x, y, z, w;
    
    Quaternion() : x(0.0), y(0.0), z(0.0), w(1.0) {}
    Quaternion(double _x, double _y, double _z, double _w) : x(_x), y(_y), z(_z), w(_w) {}
    
    // 从欧拉角创建四元数
    static Quaternion fromRPY(double roll, double pitch, double yaw) {
        Quaternion q;
        double cy = cos(yaw * 0.5);
        double sy = sin(yaw * 0.5);
        double cp = cos(pitch * 0.5);
        double sp = sin(pitch * 0.5);
        double cr = cos(roll * 0.5);
        double sr = sin(roll * 0.5);

        q.w = cr * cp * cy + sr * sp * sy;
        q.x = sr * cp * cy - cr * sp * sy;
        q.y = cr * sp * cy + sr * cp * sy;
        q.z = cr * cp * sy - sr * sp * cy;
        
        return q;
    }
    
    // 从yaw角创建四元数
    static Quaternion fromYaw(double yaw) {
        return fromRPY(0.0, 0.0, yaw);
    }
    
    // 转换为欧拉角
    void getRPY(double& roll, double& pitch, double& yaw) const {
        // Roll (x-axis rotation)
        double sinr_cosp = 2 * (w * x + y * z);
        double cosr_cosp = 1 - 2 * (x * x + y * y);
        roll = atan2(sinr_cosp, cosr_cosp);

        // Pitch (y-axis rotation)
        double sinp = 2 * (w * y - z * x);
        if (abs(sinp) >= 1)
            pitch = copysign(M_PI / 2, sinp);
        else
            pitch = asin(sinp);

        // Yaw (z-axis rotation)
        double siny_cosp = 2 * (w * z + x * y);
        double cosy_cosp = 1 - 2 * (y * y + z * z);
        yaw = atan2(siny_cosp, cosy_cosp);
    }
};

/**
 * @brief 位姿结构
 */
struct Pose {
    Point position;
    Quaternion orientation;
    
    Pose() {}
    Pose(double x, double y, double z, double roll, double pitch, double yaw) 
        : position(x, y, z), orientation(Quaternion::fromRPY(roll, pitch, yaw)) {}
};

/**
 * @brief 带时间戳的位姿
 */
struct PoseStamped {
    Header header;
    Pose pose;
    
    PoseStamped() {}
    PoseStamped(double x, double y, double z, double roll, double pitch, double yaw) 
        : pose(x, y, z, roll, pitch, yaw) {
        header.setCurrentTime();
    }
};

/**
 * @brief 里程计消息
 */
struct Odometry {
    Header header;
    std::string child_frame_id;
    struct {
        Pose pose;
        double covariance[36];
    } pose;
    struct {
        struct {
            double x, y, z;
        } linear;
        struct {
            double x, y, z;
        } angular;
        double covariance[36];
    } twist;
    
    Odometry() : child_frame_id("base_link") {
        for (int i = 0; i < 36; ++i) {
            pose.covariance[i] = 0.0;
            twist.covariance[i] = 0.0;
        }
        twist.linear.x = twist.linear.y = twist.linear.z = 0.0;
        twist.angular.x = twist.angular.y = twist.angular.z = 0.0;
    }
};

/**
 * @brief 路径消息
 */
struct Path {
    Header header;
    std::vector<PoseStamped> poses;
    
    Path() {}
};

/**
 * @brief 点云消息
 */
struct PointCloud2 {
    Header header;
    uint32_t height;
    uint32_t width;
    std::vector<uint8_t> data;
    bool is_bigendian;
    uint32_t point_step;
    uint32_t row_step;
    bool is_dense;
    
    PointCloud2() : height(1), width(0), is_bigendian(false), point_step(16), row_step(0), is_dense(true) {}
};

/**
 * @brief Bool消息
 */
struct BoolMsg {
    bool data;
    
    BoolMsg() : data(false) {}
    BoolMsg(bool d) : data(d) {}
};

/**
 * @brief Int8消息
 */
struct Int8Msg {
    int8_t data;
    
    Int8Msg() : data(0) {}
    Int8Msg(int8_t d) : data(d) {}
};

/**
 * @brief 导航目标消息
 */
struct NavigationTarget {
    int32_t nav_mode;
    int32_t point_id;
    double pose_x, pose_y, pose_z;
    double yaw;
    int32_t point_info;
    int32_t gait;
    int32_t speed;
    int32_t manner;
    int32_t obsmode;
    int32_t navmode;
    
    NavigationTarget() : nav_mode(0), point_id(0), pose_x(0.0), pose_y(0.0), pose_z(0.0),
                        yaw(0.0), point_info(0), gait(0), speed(1), manner(0), obsmode(1), navmode(0) {}
};

/**
 * @brief 导航结果消息
 */
struct NavigationResult {
    int32_t point_id;
    double target_pose_x, target_pose_y, target_pose_z;
    double target_yaw;
    double current_pose_x, current_pose_y, current_pose_z;
    double current_yaw;
    int32_t nav_state;
    
    NavigationResult() : point_id(0), target_pose_x(0.0), target_pose_y(0.0), target_pose_z(0.0),
                        target_yaw(0.0), current_pose_x(0.0), current_pose_y(0.0), current_pose_z(0.0),
                        current_yaw(0.0), nav_state(0) {}
};

} // namespace obstacle_stop_no_ros

/**
 * @brief 去ROS化的障碍物停止器类
 */
class ObstacleStopNoRos {
public:
    ObstacleStopNoRos(const std::string& name);
    ~ObstacleStopNoRos();
    
    // 初始化和控制
    bool init();
    bool initFromConfig(const std::string& config_file = "");
    void start();
    void stop();
    
    // 参数设置 (完全保留原有参数)
    void setParameters(double obstacleHeightThre, double vehicleLength, double vehicleWidth, 
                      int obsnumThre, double adjacentRange, double replan_time, 
                      double sensorOffsetX, double sensorOffsetY);
    void setObstacleHeightThre(double obstacleHeightThre);
    void setVehicleLength(double vehicleLength);
    void setVehicleWidth(double vehicleWidth);
    void setObsnumThre(int obsnumThre);
    void setAdjacentRange(double adjacentRange);
    void setReplanTime(double replan_time);
    void setSensorOffset(double sensorOffsetX, double sensorOffsetY);
    
    // 输入函数 (替换ROS订阅)
    void inputOdometry(const obstacle_stop_no_ros::Odometry& odom);
    void inputTerrainCloud(const obstacle_stop_no_ros::PointCloud2& cloud);
    void inputGoal(const obstacle_stop_no_ros::PoseStamped& goal);
    void inputTarget(const obstacle_stop_no_ros::PoseStamped& target);
    
    // 回调函数设置 (替换ROS发布)
    void setModeCallback(std::function<void(const std::shared_ptr<obstacle_stop_no_ros::BoolMsg>&)> callback);
    void setStopCallback(std::function<void(const std::shared_ptr<obstacle_stop_no_ros::Int8Msg>&)> callback);
    void setPathCallback(std::function<void(const std::shared_ptr<obstacle_stop_no_ros::Path>&)> callback);
    void setReplanCallback(std::function<void(const std::shared_ptr<obstacle_stop_no_ros::Int8Msg>&)> callback);
    void setNavigationResultCallback(std::function<void(const std::shared_ptr<obstacle_stop_no_ros::NavigationResult>&)> callback);
    
    // 状态查询
    bool isInitialized() const;
    bool isRunning() const;
    bool isNavigationStarted() const;
    bool isNewTerrainCloud() const;
    bool isInitFlag() const;
    
    // 获取当前参数和状态
    double getObstacleHeightThre() const;
    double getVehicleLength() const;
    double getVehicleWidth() const;
    int getObsnumThre() const;
    double getAdjacentRange() const;
    double getReplanTime() const;
    double getSensorOffsetX() const;
    double getSensorOffsetY() const;
    
    // 获取当前位置和目标信息
    obstacle_stop_no_ros::Point getCurrentPosition() const;
    obstacle_stop_no_ros::Point getTargetPosition() const;
    obstacle_stop_no_ros::Point getGoalPosition() const;
    double getCurrentYaw() const;
    double getTargetYaw() const;
    double getMaxSpeed() const;
    
    // 配置管理
    bool loadConfiguration(const std::string& config_file);
    bool saveConfiguration(const std::string& config_file) const;
    void printStatus() const;

private:
    // 成员变量
    std::string name_;
    bool initialized_;
    std::atomic<bool> running_;
    std::thread worker_thread_;
    std::mutex data_mutex_;
    
    // 原有的所有变量 (完全保留)
    double goalX_, goalY_, goalZ_, goalYaw_;                    // 局部目标点
    double sensorOffsetX_, sensorOffsetY_;                     // 传感器偏移
    int nav_start_, info_, obs_mode_, id_;                     // 导航状态和模式
    float vehicleX_, vehicleY_, vehicleZ_;                     // 载体位置
    float vehicleRoll_, vehiclePitch_, vehicleYaw_;            // 载体姿态
    double odomTime_;                                          // 里程计时间
    bool newTerrainCloud_;                                     // 新点云标志
    double vehicleLength_, vehicleWidth_;                      // 载体尺寸
    double obstacleHeightThre_;                               // 障碍物高度阈值
    int obsnumThre_;                                          // 障碍物数量阈值
    double start_time_, end_time_;                            // 时间记录
    bool init_;                                               // 初始化标志
    double targetX_, targetY_, targetZ_, targetYaw_;          // 目标位置
    double maxSpeed_;                                         // 最大速度
    bool twoWayDrive_;                                        // 双向驱动
    double adjacentRange_;                                    // 邻近范围
    double replan_time_;                                      // 重规划时间
    
    // PCL点云
    pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloud_;
    pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloudCrop_;
    
    // 状态消息
    obstacle_stop_no_ros::BoolMsg adjustmode_;
    obstacle_stop_no_ros::Int8Msg safetystop_;
    
    // 回调函数
    std::function<void(const std::shared_ptr<obstacle_stop_no_ros::BoolMsg>&)> mode_callback_;
    std::function<void(const std::shared_ptr<obstacle_stop_no_ros::Int8Msg>&)> stop_callback_;
    std::function<void(const std::shared_ptr<obstacle_stop_no_ros::Path>&)> path_callback_;
    std::function<void(const std::shared_ptr<obstacle_stop_no_ros::Int8Msg>&)> replan_callback_;
    std::function<void(const std::shared_ptr<obstacle_stop_no_ros::NavigationResult>&)> nav_result_callback_;
    
    // 原有的处理函数 (完全保留实现)
    void odometryHandler(const std::shared_ptr<const obstacle_stop_no_ros::Odometry>& odom);
    void terrainCloudHandler(const std::shared_ptr<const obstacle_stop_no_ros::PointCloud2>& terrainCloud2);
    void goalHandler(const std::shared_ptr<const obstacle_stop_no_ros::PoseStamped>& goal);
    void targetHandler(const std::shared_ptr<const obstacle_stop_no_ros::PoseStamped>& target);
    
    // 内部函数
    void controlLoop();
    void setDefaultParameters();
    bool loadConfigurationFromYAML(const std::string& yaml_file);
    bool loadConfigurationFromText(const std::string& config_file);
};

#endif // OBSTACLE_STOP_H
