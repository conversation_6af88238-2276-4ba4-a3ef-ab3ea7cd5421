#pragma once

#if COMMUNICATION_TYPE == ROS2

#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <nav_msgs/msg/odometry.hpp>

#include "ros2/core/publisher_base_ros2.h"
#include "data_types/pose_data.h"

namespace communication::ros2
{

    class PoseDataPublisherRos2 : public PublisherBaseRos2<PoseData, geometry_msgs::msg::PoseStamped>
    {
    public:
        PoseDataPublisherRos2(rclcpp::Node::SharedPtr node,
                              const std::string &topic,
                              const std::string &frame_id = "map",
                              size_t max_buffer_size = 10)
            : PublisherBaseRos2<PoseData, geometry_msgs::msg::PoseStamped>(node, topic, max_buffer_size),
              frame_id_(frame_id) {}

    protected:
        virtual void ToMsg() override
        {
            this->msg_.header.stamp = rclcpp::Time(static_cast<int64_t>(this->data_.time * 1e9));
            this->msg_.header.frame_id = frame_id_;

            this->msg_.pose.position.x = this->data_.position(0);
            this->msg_.pose.position.y = this->data_.position(1);
            this->msg_.pose.position.z = this->data_.position(2);

            this->msg_.pose.orientation.x = this->data_.orientation(0);
            this->msg_.pose.orientation.y = this->data_.orientation(1);
            this->msg_.pose.orientation.z = this->data_.orientation(2);
            this->msg_.pose.orientation.w = this->data_.orientation(3);
        }

    private:
        std::string frame_id_;
    };

    // class OdometryDataPublisherRos2 : public PublisherBaseRos2<PoseVelData, nav_msgs::msg::Odometry> {
    // public:
    //     OdometryDataPublisherRos2(rclcpp::Node::SharedPtr node,
    //                              const std::string& topic,
    //                              const std::string& frame_id = "map",
    //                              const std::string& child_frame_id = "body",
    //                              size_t max_buffer_size = 10)
    //         : PublisherBaseRos2<PoseVelData, nav_msgs::msg::Odometry>(node, topic, max_buffer_size),
    //           frame_id_(frame_id),
    //           child_frame_id_(child_frame_id) {}

    // protected:
    //     virtual void ToMsg() override {
    //         const auto& pose_data = this->data_.pose_data;

    //         this->msg_.header.stamp = rclcpp::Time(static_cast<int64_t>(pose_data.time * 1e9));
    //         this->msg_.header.frame_id = frame_id_;
    //         this->msg_.child_frame_id = child_frame_id_;

    //         // Position
    //         this->msg_.pose.pose.position.x = pose_data.position(0);
    //         this->msg_.pose.pose.position.y = pose_data.position(1);
    //         this->msg_.pose.pose.position.z = pose_data.position(2);

    //         // Orientation
    //         this->msg_.pose.pose.orientation.x = pose_data.orientation(0);
    //         this->msg_.pose.pose.orientation.y = pose_data.orientation(1);
    //         this->msg_.pose.pose.orientation.z = pose_data.orientation(2);
    //         this->msg_.pose.pose.orientation.w = pose_data.orientation(3);

    //         // Velocity
    //         this->msg_.twist.twist.linear.x = this->data_.vel(0);
    //         this->msg_.twist.twist.linear.y = this->data_.vel(1);
    //         this->msg_.twist.twist.linear.z = this->data_.vel(2);

    //         // Angular velocity
    //         this->msg_.twist.twist.angular.x = this->data_.angle_vel(0);
    //         this->msg_.twist.twist.angular.y = this->data_.angle_vel(1);
    //         this->msg_.twist.twist.angular.z = this->data_.angle_vel(2);
    //     }

    // private:
    //     std::string frame_id_;
    //     std::string child_frame_id_;
    // };

} // namespace communication::ros2

#endif
