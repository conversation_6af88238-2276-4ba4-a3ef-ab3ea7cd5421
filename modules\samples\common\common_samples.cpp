// main.cpp
#include <iostream>
#include "config.h"
#include "yaml_config.h"
void ConfigSamples() {
    // Load config parameters.
    std::string strConfigFile = "/mnt/d/work/01_code/DC200/cx-fusion-core-dc200/modules/samples/bin/config/test_config.yaml";
    // common_lib::GetExePath(strConfigFile);
    // strConfigFile += "/config/test_config.yaml";
    fprintf(stderr, ">>>> samples debug >>>> SetConfigPath: %s  \n", strConfigFile.c_str());
    common_lib::Config::GetInstance().Init(strConfigFile);
    std::string version_str = common_lib::Config::GetInstance().getParam<std::string>("version",
            "/mnt/d/work/01_code/jianmi/20250319/na_localization_lslidar/src/na_localization/");
    std::string strParentDir = common_lib::Config::GetInstance().getParam<std::string>("sensor/color_image_topic",
            "/mnt/d/work/01_code/jianmi/20250319/na_localization_lslidar/src/na_localization/");
    std::string strcolor_camera_info_topic = common_lib::Config::GetInstance().getParam<std::string>("sensor/color_camera_info_topic",
            "/mnt/d/work/01_code/jianmi/20250319/na_localization_lslidar/src/na_localization/");
    double test_double = common_lib::Config::GetInstance().getParam<double>("sensor/test_double", 0.0); 
    bool test_bool = common_lib::Config::GetInstance().getParam<bool>("test_bool", 0.0); 
    int test_int = common_lib::Config::GetInstance().getParam<int>("test/test_int", 0.0); 
    std::vector<double> test_double_vec = common_lib::Config::GetInstance().getParam<std::vector<double>>("test_double_vec", std::vector<double>()); 
            
    double i = 0;
}

void TestYamlConfig() {
    std::string strConfigFile = "/mnt/d/work/01_code/DC200/cx-fusion-core-dc200/modules/samples/bin/config/test_config.yaml";
    // common_lib::GetExePath(strConfigFile);
    // strConfigFile += "/config/test_config.yaml";
    fprintf(stderr, ">>>> samples debug >>>> test yaml config: %s  \n", strConfigFile.c_str());
    common_lib::YamlConfig& config = common_lib::YamlConfig::GetInstance();
    config.LoadFile(strConfigFile);

    // 使用默认值
    std::string version_str = common_lib::YamlConfig::GetInstance().GetParam<std::string>("version",
            "/mnt/d/work/01_code/jianmi/20250319/na_localization_lslidar/src/na_localization/");
    std::string color_image_topic = common_lib::YamlConfig::GetInstance().GetParam<std::string>("sensor/color_image_topic",
            "/mnt/d/work/01_code/jianmi/20250319/na_localization_lslidar/src/na_localization/");
    std::string color_camera_info_topic = common_lib::YamlConfig::GetInstance().GetParam<std::string>("sensor/color_camera_info_topic",
            "/mnt/d/work/01_code/jianmi/20250319/na_localization_lslidar/src/na_localization/");
    double test_double = common_lib::YamlConfig::GetInstance().GetParam<double>("sensor/test_double", 0.0); 
    bool test_bool = common_lib::YamlConfig::GetInstance().GetParam<bool>("test_bool", 0.0); 
    int test_int = common_lib::YamlConfig::GetInstance().GetParam<int>("test/test_int", 0.0); 
    std::vector<double> test_double_vec = common_lib::YamlConfig::GetInstance().GetParam<std::vector<double>>("test_double_vec", std::vector<double>()); 
    
    double i = 0;
}

int main() {
//     // Config samples
//     ConfigSamples();

    // test yaml config
    TestYamlConfig();
    return 0;
}