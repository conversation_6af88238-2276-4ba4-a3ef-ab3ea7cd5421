/**
 * * @file pointcloud_data_subscriber_ros1.h
 * @brief This file contains the ROS1 implementation of the PointCloudDataSubscriber class.
 * * It subscribes to point cloud data from a specified ROS topic and processes the incoming messages.
 * * It inherits from the CloudDataSubscriberBase class.
 */
#pragma once

#if COMMUNICATION_TYPE == ROS1

#include <ros/ros.h>
// #include <deque>
// #include <mutex>
#include <sensor_msgs/PointCloud2.h>

#include "subscriber_base.h"

namespace communication::ros1{

class LidarDataSubscriberRos1 :  public LidarDataSubscriberBase{
    public:
        LidarDataSubscriberRos1(ros::NodeHandle &nh, const std::string &lidar_topic, size_t max_buffer_size = 1);

        ~LidarDataSubscriberRos1() = default;

        void LidarDataCallbackRos1(const sensor_msgs::PointCloud2::ConstPtr &lidar_msg);

    private:
        ros::Node<PERSON><PERSON><PERSON>& nh_;
        ros::Subscriber subscriber_;
    };

} // namespace communication::ros1{

#endif