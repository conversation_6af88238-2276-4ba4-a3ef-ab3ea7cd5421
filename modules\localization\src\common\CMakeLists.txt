add_library(${PROJECT_NAME}_common SHARED
    esekfom.cpp
    ikd-Tree/ikd_Tree.cpp
    eskf/use-ikfom.cpp
    lla_enu.cpp
    coordtrans.cpp
    common.cpp
)

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/
    ${CMAKE_CURRENT_SOURCE_DIR}/ikd-Tree
    ${CMAKE_CURRENT_SOURCE_DIR}/eskf
    ${CMAKE_CURRENT_SOURCE_DIR}/../common
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/include/config
)

