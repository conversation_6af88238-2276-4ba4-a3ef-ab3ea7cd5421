/**
 * @file path_data_subscriber_ros1.h
 * @brief ROS1 Path Data Subscriber
 * This file contains the implementation of a ROS1 subscriber for path data.
 * It subscribes to a ROS topic that publishes `nav_msgs::Path` messages and converts them into a custom `PathData` format.
 * The subscriber handles the incoming messages and stores the path data in a thread-safe manner.
 * It inherits from the `PathDataSubscriberBase` class.
 */
#pragma once
#include <ros/ros.h>
#include <nav_msgs/Path.h>
#include "data_types/path_data.h"
#include "subscriber_base.h"

#if COMMUNICATION_TYPE == ROS1

namespace communication::ros1 {

class PathDataSubscriberRos1 : public PathDataSubscriberBase {
public:
    PathDataSubscriberRos1(ros::NodeHandle &nh, const std::string &topic, size_t max_buffer_size = 100);

private:
    ros::NodeHandle& nh_;
    ros::Subscriber subscriber_;

    void PathDataCallbackRos1(const nav_msgs::Path::ConstPtr &msg);
};

}// namespace communication::ros1

#endif // COMMUNICATION_TYPE == ROS1